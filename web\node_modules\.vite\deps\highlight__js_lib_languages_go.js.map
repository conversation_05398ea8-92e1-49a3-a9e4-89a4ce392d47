{"version": 3, "sources": ["../../.pnpm/highlight.js@11.10.0/node_modules/highlight.js/es/languages/go.js"], "sourcesContent": ["/*\nLanguage: Go\nAuthor: <PERSON> aka StepLg <<EMAIL>>\nContributors: <PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: Google go language (golang). For info about language\nWebsite: http://golang.org/\nCategory: common, system\n*/\n\nfunction go(hljs) {\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    \"iota\",\n    \"nil\"\n  ];\n  const BUILT_INS = [\n    \"append\",\n    \"cap\",\n    \"close\",\n    \"complex\",\n    \"copy\",\n    \"imag\",\n    \"len\",\n    \"make\",\n    \"new\",\n    \"panic\",\n    \"print\",\n    \"println\",\n    \"real\",\n    \"recover\",\n    \"delete\"\n  ];\n  const TYPES = [\n    \"bool\",\n    \"byte\",\n    \"complex64\",\n    \"complex128\",\n    \"error\",\n    \"float32\",\n    \"float64\",\n    \"int8\",\n    \"int16\",\n    \"int32\",\n    \"int64\",\n    \"string\",\n    \"uint8\",\n    \"uint16\",\n    \"uint32\",\n    \"uint64\",\n    \"int\",\n    \"uint\",\n    \"uintptr\",\n    \"rune\"\n  ];\n  const KWS = [\n    \"break\",\n    \"case\",\n    \"chan\",\n    \"const\",\n    \"continue\",\n    \"default\",\n    \"defer\",\n    \"else\",\n    \"fallthrough\",\n    \"for\",\n    \"func\",\n    \"go\",\n    \"goto\",\n    \"if\",\n    \"import\",\n    \"interface\",\n    \"map\",\n    \"package\",\n    \"range\",\n    \"return\",\n    \"select\",\n    \"struct\",\n    \"switch\",\n    \"type\",\n    \"var\",\n  ];\n  const KEYWORDS = {\n    keyword: KWS,\n    type: TYPES,\n    literal: LITERALS,\n    built_in: BUILT_INS\n  };\n  return {\n    name: 'Go',\n    aliases: [ 'golang' ],\n    keywords: KEYWORDS,\n    illegal: '</',\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'string',\n        variants: [\n          hljs.QUOTE_STRING_MODE,\n          hljs.APOS_STRING_MODE,\n          {\n            begin: '`',\n            end: '`'\n          }\n        ]\n      },\n      {\n        className: 'number',\n        variants: [\n          {\n            match: /-?\\b0[xX]\\.[a-fA-F0-9](_?[a-fA-F0-9])*[pP][+-]?\\d(_?\\d)*i?/, // hex without a present digit before . (making a digit afterwards required)\n            relevance: 0\n          },\n          {\n            match: /-?\\b0[xX](_?[a-fA-F0-9])+((\\.([a-fA-F0-9](_?[a-fA-F0-9])*)?)?[pP][+-]?\\d(_?\\d)*)?i?/, // hex with a present digit before . (making a digit afterwards optional)\n            relevance: 0\n          },\n          {\n            match: /-?\\b0[oO](_?[0-7])*i?/, // leading 0o octal\n            relevance: 0\n          },\n          {\n            match: /-?\\.\\d(_?\\d)*([eE][+-]?\\d(_?\\d)*)?i?/, // decimal without a present digit before . (making a digit afterwards required)\n            relevance: 0\n          },\n          {\n            match: /-?\\b\\d(_?\\d)*(\\.(\\d(_?\\d)*)?)?([eE][+-]?\\d(_?\\d)*)?i?/, // decimal with a present digit before . (making a digit afterwards optional)\n            relevance: 0\n          }\n        ]\n      },\n      { begin: /:=/ // relevance booster\n      },\n      {\n        className: 'function',\n        beginKeywords: 'func',\n        end: '\\\\s*(\\\\{|$)',\n        excludeEnd: true,\n        contains: [\n          hljs.TITLE_MODE,\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            endsParent: true,\n            keywords: KEYWORDS,\n            illegal: /[\"']/\n          }\n        ]\n      }\n    ]\n  };\n}\n\nexport { go as default };\n"], "mappings": ";;;AASA,SAAS,GAAG,MAAM;AAChB,QAAM,WAAW;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,YAAY;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,MAAM;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,WAAW;AAAA,IACf,SAAS;AAAA,IACT,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,EACZ;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS,CAAE,QAAS;AAAA,IACpB,UAAU;AAAA,IACV,SAAS;AAAA,IACT,UAAU;AAAA,MACR,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,QACE,WAAW;AAAA,QACX,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA;AAAA,YACP,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QAAE,OAAO;AAAA;AAAA,MACT;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,eAAe;AAAA,QACf,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,SAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;", "names": []}