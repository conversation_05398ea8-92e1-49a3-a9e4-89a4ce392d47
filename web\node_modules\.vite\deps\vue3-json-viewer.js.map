{"version": 3, "sources": ["../../.pnpm/vue3-json-viewer@2.4.1_vue@3.4.38_typescript@5.5.4_/node_modules/vue3-json-viewer/dist/vue3-json-viewer.mjs"], "sourcesContent": ["import { defineComponent as C, ref as w, onMounted as H, h as j, computed as O, createElementBlock as A, openBlock as N, normalizeClass as S, toDisplayString as V, watch as J, inject as F, provide as I, nextTick as fe, resolveComponent as de, createCommentVNode as Z, createElementVNode as q, renderSlot as pe, createTextVNode as ve, createVNode as ye } from \"vue\";\nconst me = /^([hH][tT]{2}[pP]:\\/\\/|[hH][tT]{2}[pP][sS]:\\/\\/)(([A-Za-z0-9-~]+)\\.)+([A-Za-z0-9-~\\/])+$/, G = C({\n  name: \"JsonString\",\n  props: {\n    /** The string value to display. */\n    jsonValue: {\n      type: String,\n      required: !0\n    }\n  },\n  /**\n   * Setup function for the JsonString component.\n   * @param props - The component's props.\n   */\n  setup(e) {\n    const m = w(!0), s = w(!1), l = w(null), p = w(null);\n    H(() => {\n      l.value && p.value && l.value.offsetHeight > p.value.offsetHeight && (s.value = !0);\n    });\n    const u = () => {\n      m.value = !m.value;\n    };\n    return () => {\n      const n = e.jsonValue, t = me.test(n);\n      let o;\n      !m.value && s.value ? o = j(\"span\", {\n        class: { \"jv-ellipsis\": !0 },\n        onClick: u\n      }, \"...\") : t ? o = j(\"span\", { class: { \"jv-item\": !0, \"jv-string\": !0 }, ref: l }, [\n        j(\"span\", null, '\"'),\n        // Opening quote\n        j(\"a\", { href: n, target: \"_blank\", class: \"jv-link\" }, n),\n        j(\"span\", null, '\"')\n        // Closing quote\n      ]) : o = j(\"span\", {\n        class: { \"jv-item\": !0, \"jv-string\": !0 },\n        ref: l\n        // Assign ref for height calculation\n      }, `\"${n}\"`);\n      const r = [];\n      return s.value && r.push(j(\"span\", {\n        class: {\n          \"jv-toggle\": !0,\n          // CSS class for the toggle button\n          open: m.value\n          // Class to indicate open/closed state\n        },\n        onClick: u\n      })), r.push(j(\"span\", { class: { \"jv-holder-node\": !0 }, ref: p })), r.push(o), j(\"span\", {}, r);\n    };\n  }\n}), he = {\n  class: /* @__PURE__ */ S([\"jv-item\", \"jv-undefined\"])\n}, ge = /* @__PURE__ */ C({\n  __name: \"json-undefined\",\n  props: {\n    /**\n     * The value to display. Expected to be `null` or `undefined`.\n     * The `type` is set to `null` and `PropType` is used to allow `undefined` as well,\n     * with a default of `undefined`.\n     */\n    jsonValue: {\n      type: null,\n      default: void 0\n    }\n  },\n  setup(e) {\n    const m = e, s = O(() => m.jsonValue === null ? \"null\" : \"undefined\");\n    return (l, p) => (N(), A(\"span\", he, V(s.value), 1));\n  }\n}), be = /* @__PURE__ */ C({\n  __name: \"json-number\",\n  props: {\n    /** The number value to display. */\n    jsonValue: {\n      type: Number,\n      required: !0\n    }\n  },\n  setup(e) {\n    const m = e, s = O(() => Number.isInteger(m.jsonValue));\n    return (l, p) => (N(), A(\"span\", {\n      class: S([\"jv-item\", \"jv-number\", s.value ? \"jv-number-integer\" : \"jv-number-float\"])\n    }, V(e.jsonValue.toString()), 3));\n  }\n}), je = {\n  class: /* @__PURE__ */ S([\"jv-item\", \"jv-boolean\"])\n}, xe = /* @__PURE__ */ C({\n  __name: \"json-boolean\",\n  props: {\n    /** The boolean value to display. */\n    jsonValue: {\n      type: Boolean,\n      required: !0\n    }\n  },\n  setup(e) {\n    return (m, s) => (N(), A(\"span\", je, V(e.jsonValue.toString()), 1));\n  }\n}), _e = C({\n  name: \"JsonObject\",\n  props: {\n    /** The object value to render. */\n    jsonValue: {\n      type: Object,\n      required: !0\n    },\n    /** The key name of this object if it's a property of another object. */\n    keyName: {\n      type: String,\n      default: \"\"\n    },\n    /** Current nesting depth of this object. */\n    depth: {\n      type: Number,\n      default: 0\n    },\n    /** Whether this object should be rendered in an expanded state. */\n    expand: Boolean,\n    /** Whether to sort the keys of this object alphabetically. */\n    sort: Boolean,\n    /** Whether preview mode is enabled. */\n    previewMode: Boolean\n  },\n  emits: [\"update:expand\"],\n  /**\n   * Setup function for the JsonObject component.\n   * @param props - The component's props.\n   * @param context - The setup context, including `emit`.\n   */\n  setup(e, { emit: m }) {\n    const s = w({});\n    let l = null;\n    const p = (o) => {\n      setTimeout(() => {\n        s.value = o;\n      }, 0);\n    };\n    J(() => e.jsonValue, (o) => {\n      p(o);\n    }, { immediate: !0, deep: !0 });\n    const u = O(() => {\n      if (!e.sort)\n        return s.value;\n      const o = Object.keys(s.value).sort(), r = {};\n      return o.forEach((v) => {\n        r[v] = s.value[v];\n      }), r;\n    }), n = () => {\n      if (l)\n        try {\n          l.dispatchEvent(new Event(\"resized\"));\n        } catch {\n          const r = document.createEvent(\"Event\");\n          r.initEvent(\"resized\", !0, !1), l.dispatchEvent(r);\n        }\n    }, t = () => {\n      m(\"update:expand\", !e.expand), n();\n    };\n    return () => {\n      const o = [];\n      if (o.push(j(\"span\", { class: [\"jv-item\", \"jv-object\"] }, \"{\")), e.expand) {\n        for (const r in u.value)\n          if (u.value.hasOwnProperty(r)) {\n            const v = u.value[r];\n            o.push(j(K, {\n              key: r,\n              // style: { display: !props.expand ? 'none' : undefined }, // Redundant\n              sort: e.sort,\n              keyName: r,\n              depth: e.depth + 1,\n              value: v,\n              previewMode: e.previewMode\n            }));\n          }\n      }\n      return !e.expand && Object.keys(s.value).length > 0 && o.push(j(\"span\", {\n        // style: { display: props.expand ? 'none' : undefined }, // Redundant\n        class: \"jv-ellipsis\",\n        onClick: t,\n        title: `click to reveal object content (keys: ${Object.keys(u.value).join(\", \")})`\n      }, \"...\")), o.push(j(\"span\", { class: [\"jv-item\", \"jv-object\"] }, \"}\")), j(\"span\", { ref: (r) => {\n        l = r;\n      } }, o);\n    };\n  }\n}), Ee = C({\n  name: \"JsonArray\",\n  props: {\n    /** The array value to render. */\n    jsonValue: {\n      type: Array,\n      required: !0\n    },\n    /** The key name of this array if it's a property of an object. */\n    keyName: {\n      type: String,\n      default: \"\"\n    },\n    /** Current nesting depth of this array. */\n    depth: {\n      type: Number,\n      default: 0\n    },\n    /** Whether to sort array items (Note: arrays are typically not sorted by key). */\n    sort: Boolean,\n    // This prop might be less relevant for arrays vs objects\n    /** Whether this array should be rendered in an expanded state. */\n    expand: Boolean,\n    /** Whether preview mode is enabled (potentially showing a condensed view). */\n    previewMode: Boolean\n  },\n  emits: [\"update:expand\"],\n  /**\n   * Setup function for the JsonArray component.\n   * @param props - The component's props.\n   * @param context - The setup context, including `emit`.\n   */\n  setup(e, { emit: m }) {\n    const s = w([]);\n    let l = null;\n    const p = (n, t = 0) => {\n      t === 0 && (s.value = []), setTimeout(() => {\n        n && n.length > t && (s.value.push(n[t]), p(n, t + 1));\n      }, 0);\n    };\n    J(() => e.jsonValue, (n) => {\n      p(n);\n    }, { immediate: !0, deep: !0 });\n    const u = () => {\n      if (m(\"update:expand\", !e.expand), l)\n        try {\n          l.dispatchEvent(new Event(\"resized\"));\n        } catch {\n          const t = document.createEvent(\"Event\");\n          t.initEvent(\"resized\", !0, !1), l.dispatchEvent(t);\n        }\n    };\n    return () => {\n      const n = [];\n      return !e.previewMode && !e.keyName && n.push(j(\"span\", {\n        class: {\n          \"jv-toggle\": !0,\n          open: !!e.expand\n        },\n        onClick: u\n      })), n.push(j(\"span\", {\n        class: [\"jv-item\", \"jv-array\"]\n      }, \"[\")), e.expand && s.value.forEach((t, o) => {\n        n.push(j(K, {\n          // Cast JsonBox to Component\n          key: o,\n          // style: { display: props.expand ? undefined : 'none' }, // This style is redundant if items are not rendered\n          sort: e.sort,\n          depth: e.depth + 1,\n          value: t,\n          previewMode: e.previewMode\n        }));\n      }), !e.expand && s.value.length > 0 && n.push(j(\"span\", {\n        class: \"jv-ellipsis\",\n        onClick: u,\n        title: `click to reveal ${s.value.length} hidden items`\n      }, \"...\")), n.push(j(\"span\", {\n        class: [\"jv-item\", \"jv-array\"]\n      }, \"]\")), j(\"span\", { ref: (t) => {\n        l = t;\n      } }, n);\n    };\n  }\n}), we = [\"title\"], Te = /* @__PURE__ */ C({\n  __name: \"json-function\",\n  props: {\n    /** The Function object to represent. */\n    jsonValue: {\n      type: Function,\n      required: !0\n    }\n  },\n  setup(e) {\n    return (m, s) => (N(), A(\"span\", {\n      class: S([\"jv-item\", \"jv-function\"]),\n      title: e.jsonValue.toString()\n    }, \" <function> \", 8, we));\n  }\n}), Se = {\n  class: /* @__PURE__ */ S([\"jv-item\", \"jv-string\"])\n}, Ce = /* @__PURE__ */ C({\n  __name: \"json-date\",\n  props: {\n    /** The Date object to display. */\n    jsonValue: {\n      type: Date,\n      required: !0\n    }\n  },\n  setup(e) {\n    const m = e, s = F(\"timeformat\", (p) => p.toLocaleString()), l = O(() => s(m.jsonValue));\n    return (p, u) => (N(), A(\"span\", Se, ' \"' + V(l.value) + '\" ', 1));\n  }\n}), ke = {\n  class: /* @__PURE__ */ S([\"jv-item\", \"jv-regexp\"])\n}, Ae = /* @__PURE__ */ C({\n  __name: \"json-regexp\",\n  props: {\n    /** The RegExp object to display. */\n    jsonValue: {\n      type: RegExp,\n      required: !0\n    }\n  },\n  setup(e) {\n    return (m, s) => (N(), A(\"span\", ke, V(e.jsonValue.toString()), 1));\n  }\n}), K = C({\n  name: \"JsonBox\",\n  props: {\n    /** The JSON value to render. Can be any valid JSON type. */\n    value: {\n      type: [Object, Array, String, Number, Boolean, Function, Date],\n      default: null\n    },\n    /** The key name for this value, if it's part of an object. */\n    keyName: {\n      type: String,\n      default: \"\"\n    },\n    /** Whether to sort object keys alphabetically. Passed down from JsonViewer. */\n    sort: Boolean,\n    /** Current nesting depth of this component. */\n    depth: {\n      type: Number,\n      default: 0\n    },\n    /** Whether preview mode is enabled. Passed down from JsonViewer. */\n    previewMode: Boolean\n  },\n  /**\n   * Setup function for the JsonBox component.\n   * @param props - The component's props.\n   */\n  setup(e) {\n    const m = F(\"expandDepth\", 1 / 0), s = F(\"keyClick\", () => {\n    }), l = w(!0);\n    let p = null;\n    H(() => {\n      l.value = e.previewMode || e.depth < m;\n    });\n    const u = () => {\n      if (l.value = !l.value, p)\n        try {\n          p.dispatchEvent(new Event(\"resized\"));\n        } catch {\n          const t = document.createEvent(\"Event\");\n          t.initEvent(\"resized\", !0, !1), p.dispatchEvent(t);\n        }\n    };\n    return () => {\n      const n = [];\n      let t;\n      e.value === null || e.value === void 0 ? t = ge : Array.isArray(e.value) ? t = Ee : e.value instanceof Date ? t = Ce : e.value instanceof RegExp ? t = Ae : typeof e.value == \"object\" ? t = _e : typeof e.value == \"number\" ? t = be : typeof e.value == \"string\" ? t = G : typeof e.value == \"boolean\" ? t = xe : typeof e.value == \"function\" ? t = Te : t = G;\n      const o = e.value && (Array.isArray(e.value) || typeof e.value == \"object\" && !(e.value instanceof Date) && // Exclude Date\n      !(e.value instanceof RegExp));\n      return !e.previewMode && o && !(e.value instanceof RegExp) && n.push(\n        j(\"span\", {\n          class: {\n            \"jv-toggle\": !0,\n            open: !!l.value\n            // Double negation to ensure boolean\n          },\n          onClick: u\n        })\n      ), e.keyName && n.push(\n        j(\"span\", {\n          class: \"jv-key\",\n          // 'jv-key' is a string, not an object\n          onClick: () => {\n            s && s(e.keyName);\n          }\n        }, `${e.keyName}:`)\n        // Text content as children\n      ), n.push(\n        j(t, {\n          class: \"jv-push\",\n          // 'jv-push' is a string\n          jsonValue: e.value,\n          keyName: e.keyName,\n          // Pass keyName for context if needed by child\n          sort: e.sort,\n          // Pass sort for objects/arrays\n          depth: e.depth,\n          // Pass current depth\n          expand: l.value,\n          // Pass current expand state\n          previewMode: e.previewMode,\n          // Listen for 'update:expand' events from child components (e.g., JsonArray, JsonObject)\n          // This allows children to request a change in their own expansion state.\n          \"onUpdate:expand\": (r) => {\n            l.value = r;\n          }\n        })\n      ), j(\n        \"div\",\n        {\n          class: {\n            \"jv-node\": !0,\n            \"jv-key-node\": !!e.keyName && !o,\n            // Apply if keyName exists and not a complex type\n            toggle: !e.previewMode && o\n            // Apply if not preview and is complex for styling indent\n          },\n          ref: (r) => {\n            p = r;\n          }\n        },\n        n\n      );\n    };\n  }\n});\nfunction Ne(e) {\n  return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, \"default\") ? e.default : e;\n}\nvar L = { exports: {} };\n/*!\n * clipboard.js v2.0.11\n * https://clipboardjs.com/\n *\n * Licensed MIT © Zeno Rocha\n */\nvar Oe = L.exports, X;\nfunction Ve() {\n  return X || (X = 1, function(e, m) {\n    (function(l, p) {\n      e.exports = p();\n    })(Oe, function() {\n      return (\n        /******/\n        function() {\n          var s = {\n            /***/\n            686: (\n              /***/\n              function(u, n, t) {\n                t.d(n, {\n                  default: function() {\n                    return (\n                      /* binding */\n                      le\n                    );\n                  }\n                });\n                var o = t(279), r = /* @__PURE__ */ t.n(o), v = t(370), _ = /* @__PURE__ */ t.n(v), x = t(817), E = /* @__PURE__ */ t.n(x);\n                function g(d) {\n                  try {\n                    return document.execCommand(d);\n                  } catch {\n                    return !1;\n                  }\n                }\n                var h = function(i) {\n                  var a = E()(i);\n                  return g(\"cut\"), a;\n                }, b = h;\n                function k(d) {\n                  var i = document.documentElement.getAttribute(\"dir\") === \"rtl\", a = document.createElement(\"textarea\");\n                  a.style.fontSize = \"12pt\", a.style.border = \"0\", a.style.padding = \"0\", a.style.margin = \"0\", a.style.position = \"absolute\", a.style[i ? \"right\" : \"left\"] = \"-9999px\";\n                  var c = window.pageYOffset || document.documentElement.scrollTop;\n                  return a.style.top = \"\".concat(c, \"px\"), a.setAttribute(\"readonly\", \"\"), a.value = d, a;\n                }\n                var U = function(i, a) {\n                  var c = k(i);\n                  a.container.appendChild(c);\n                  var f = E()(c);\n                  return g(\"copy\"), c.remove(), f;\n                }, W = function(i) {\n                  var a = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {\n                    container: document.body\n                  }, c = \"\";\n                  return typeof i == \"string\" ? c = U(i, a) : i instanceof HTMLInputElement && ![\"text\", \"search\", \"url\", \"tel\", \"password\"].includes(i == null ? void 0 : i.type) ? c = U(i.value, a) : (c = E()(i), g(\"copy\")), c;\n                }, D = W;\n                function $(d) {\n                  \"@babel/helpers - typeof\";\n                  return typeof Symbol == \"function\" && typeof Symbol.iterator == \"symbol\" ? $ = function(a) {\n                    return typeof a;\n                  } : $ = function(a) {\n                    return a && typeof Symbol == \"function\" && a.constructor === Symbol && a !== Symbol.prototype ? \"symbol\" : typeof a;\n                  }, $(d);\n                }\n                var ee = function() {\n                  var i = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, a = i.action, c = a === void 0 ? \"copy\" : a, f = i.container, y = i.target, T = i.text;\n                  if (c !== \"copy\" && c !== \"cut\")\n                    throw new Error('Invalid \"action\" value, use either \"copy\" or \"cut\"');\n                  if (y !== void 0)\n                    if (y && $(y) === \"object\" && y.nodeType === 1) {\n                      if (c === \"copy\" && y.hasAttribute(\"disabled\"))\n                        throw new Error('Invalid \"target\" attribute. Please use \"readonly\" instead of \"disabled\" attribute');\n                      if (c === \"cut\" && (y.hasAttribute(\"readonly\") || y.hasAttribute(\"disabled\")))\n                        throw new Error(`Invalid \"target\" attribute. You can't cut text from elements with \"readonly\" or \"disabled\" attributes`);\n                    } else\n                      throw new Error('Invalid \"target\" value, use a valid Element');\n                  if (T)\n                    return D(T, {\n                      container: f\n                    });\n                  if (y)\n                    return c === \"cut\" ? b(y) : D(y, {\n                      container: f\n                    });\n                }, te = ee;\n                function M(d) {\n                  \"@babel/helpers - typeof\";\n                  return typeof Symbol == \"function\" && typeof Symbol.iterator == \"symbol\" ? M = function(a) {\n                    return typeof a;\n                  } : M = function(a) {\n                    return a && typeof Symbol == \"function\" && a.constructor === Symbol && a !== Symbol.prototype ? \"symbol\" : typeof a;\n                  }, M(d);\n                }\n                function ne(d, i) {\n                  if (!(d instanceof i))\n                    throw new TypeError(\"Cannot call a class as a function\");\n                }\n                function Y(d, i) {\n                  for (var a = 0; a < i.length; a++) {\n                    var c = i[a];\n                    c.enumerable = c.enumerable || !1, c.configurable = !0, \"value\" in c && (c.writable = !0), Object.defineProperty(d, c.key, c);\n                  }\n                }\n                function oe(d, i, a) {\n                  return i && Y(d.prototype, i), a && Y(d, a), d;\n                }\n                function re(d, i) {\n                  if (typeof i != \"function\" && i !== null)\n                    throw new TypeError(\"Super expression must either be null or a function\");\n                  d.prototype = Object.create(i && i.prototype, { constructor: { value: d, writable: !0, configurable: !0 } }), i && P(d, i);\n                }\n                function P(d, i) {\n                  return P = Object.setPrototypeOf || function(c, f) {\n                    return c.__proto__ = f, c;\n                  }, P(d, i);\n                }\n                function ae(d) {\n                  var i = se();\n                  return function() {\n                    var c = R(d), f;\n                    if (i) {\n                      var y = R(this).constructor;\n                      f = Reflect.construct(c, arguments, y);\n                    } else\n                      f = c.apply(this, arguments);\n                    return ie(this, f);\n                  };\n                }\n                function ie(d, i) {\n                  return i && (M(i) === \"object\" || typeof i == \"function\") ? i : ue(d);\n                }\n                function ue(d) {\n                  if (d === void 0)\n                    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n                  return d;\n                }\n                function se() {\n                  if (typeof Reflect > \"u\" || !Reflect.construct || Reflect.construct.sham) return !1;\n                  if (typeof Proxy == \"function\") return !0;\n                  try {\n                    return Date.prototype.toString.call(Reflect.construct(Date, [], function() {\n                    })), !0;\n                  } catch {\n                    return !1;\n                  }\n                }\n                function R(d) {\n                  return R = Object.setPrototypeOf ? Object.getPrototypeOf : function(a) {\n                    return a.__proto__ || Object.getPrototypeOf(a);\n                  }, R(d);\n                }\n                function z(d, i) {\n                  var a = \"data-clipboard-\".concat(d);\n                  if (i.hasAttribute(a))\n                    return i.getAttribute(a);\n                }\n                var ce = /* @__PURE__ */ function(d) {\n                  re(a, d);\n                  var i = ae(a);\n                  function a(c, f) {\n                    var y;\n                    return ne(this, a), y = i.call(this), y.resolveOptions(f), y.listenClick(c), y;\n                  }\n                  return oe(a, [{\n                    key: \"resolveOptions\",\n                    value: function() {\n                      var f = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n                      this.action = typeof f.action == \"function\" ? f.action : this.defaultAction, this.target = typeof f.target == \"function\" ? f.target : this.defaultTarget, this.text = typeof f.text == \"function\" ? f.text : this.defaultText, this.container = M(f.container) === \"object\" ? f.container : document.body;\n                    }\n                    /**\n                     * Adds a click event listener to the passed trigger.\n                     * @param {String|HTMLElement|HTMLCollection|NodeList} trigger\n                     */\n                  }, {\n                    key: \"listenClick\",\n                    value: function(f) {\n                      var y = this;\n                      this.listener = _()(f, \"click\", function(T) {\n                        return y.onClick(T);\n                      });\n                    }\n                    /**\n                     * Defines a new `ClipboardAction` on each click event.\n                     * @param {Event} e\n                     */\n                  }, {\n                    key: \"onClick\",\n                    value: function(f) {\n                      var y = f.delegateTarget || f.currentTarget, T = this.action(y) || \"copy\", B = te({\n                        action: T,\n                        container: this.container,\n                        target: this.target(y),\n                        text: this.text(y)\n                      });\n                      this.emit(B ? \"success\" : \"error\", {\n                        action: T,\n                        text: B,\n                        trigger: y,\n                        clearSelection: function() {\n                          y && y.focus(), window.getSelection().removeAllRanges();\n                        }\n                      });\n                    }\n                    /**\n                     * Default `action` lookup function.\n                     * @param {Element} trigger\n                     */\n                  }, {\n                    key: \"defaultAction\",\n                    value: function(f) {\n                      return z(\"action\", f);\n                    }\n                    /**\n                     * Default `target` lookup function.\n                     * @param {Element} trigger\n                     */\n                  }, {\n                    key: \"defaultTarget\",\n                    value: function(f) {\n                      var y = z(\"target\", f);\n                      if (y)\n                        return document.querySelector(y);\n                    }\n                    /**\n                     * Allow fire programmatically a copy action\n                     * @param {String|HTMLElement} target\n                     * @param {Object} options\n                     * @returns Text copied.\n                     */\n                  }, {\n                    key: \"defaultText\",\n                    /**\n                     * Default `text` lookup function.\n                     * @param {Element} trigger\n                     */\n                    value: function(f) {\n                      return z(\"text\", f);\n                    }\n                    /**\n                     * Destroy lifecycle.\n                     */\n                  }, {\n                    key: \"destroy\",\n                    value: function() {\n                      this.listener.destroy();\n                    }\n                  }], [{\n                    key: \"copy\",\n                    value: function(f) {\n                      var y = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {\n                        container: document.body\n                      };\n                      return D(f, y);\n                    }\n                    /**\n                     * Allow fire programmatically a cut action\n                     * @param {String|HTMLElement} target\n                     * @returns Text cutted.\n                     */\n                  }, {\n                    key: \"cut\",\n                    value: function(f) {\n                      return b(f);\n                    }\n                    /**\n                     * Returns the support of the given action, or all actions if no action is\n                     * given.\n                     * @param {String} [action]\n                     */\n                  }, {\n                    key: \"isSupported\",\n                    value: function() {\n                      var f = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [\"copy\", \"cut\"], y = typeof f == \"string\" ? [f] : f, T = !!document.queryCommandSupported;\n                      return y.forEach(function(B) {\n                        T = T && !!document.queryCommandSupported(B);\n                      }), T;\n                    }\n                  }]), a;\n                }(r()), le = ce;\n              }\n            ),\n            /***/\n            828: (\n              /***/\n              function(u) {\n                var n = 9;\n                if (typeof Element < \"u\" && !Element.prototype.matches) {\n                  var t = Element.prototype;\n                  t.matches = t.matchesSelector || t.mozMatchesSelector || t.msMatchesSelector || t.oMatchesSelector || t.webkitMatchesSelector;\n                }\n                function o(r, v) {\n                  for (; r && r.nodeType !== n; ) {\n                    if (typeof r.matches == \"function\" && r.matches(v))\n                      return r;\n                    r = r.parentNode;\n                  }\n                }\n                u.exports = o;\n              }\n            ),\n            /***/\n            438: (\n              /***/\n              function(u, n, t) {\n                var o = t(828);\n                function r(x, E, g, h, b) {\n                  var k = _.apply(this, arguments);\n                  return x.addEventListener(g, k, b), {\n                    destroy: function() {\n                      x.removeEventListener(g, k, b);\n                    }\n                  };\n                }\n                function v(x, E, g, h, b) {\n                  return typeof x.addEventListener == \"function\" ? r.apply(null, arguments) : typeof g == \"function\" ? r.bind(null, document).apply(null, arguments) : (typeof x == \"string\" && (x = document.querySelectorAll(x)), Array.prototype.map.call(x, function(k) {\n                    return r(k, E, g, h, b);\n                  }));\n                }\n                function _(x, E, g, h) {\n                  return function(b) {\n                    b.delegateTarget = o(b.target, E), b.delegateTarget && h.call(x, b);\n                  };\n                }\n                u.exports = v;\n              }\n            ),\n            /***/\n            879: (\n              /***/\n              function(u, n) {\n                n.node = function(t) {\n                  return t !== void 0 && t instanceof HTMLElement && t.nodeType === 1;\n                }, n.nodeList = function(t) {\n                  var o = Object.prototype.toString.call(t);\n                  return t !== void 0 && (o === \"[object NodeList]\" || o === \"[object HTMLCollection]\") && \"length\" in t && (t.length === 0 || n.node(t[0]));\n                }, n.string = function(t) {\n                  return typeof t == \"string\" || t instanceof String;\n                }, n.fn = function(t) {\n                  var o = Object.prototype.toString.call(t);\n                  return o === \"[object Function]\";\n                };\n              }\n            ),\n            /***/\n            370: (\n              /***/\n              function(u, n, t) {\n                var o = t(879), r = t(438);\n                function v(g, h, b) {\n                  if (!g && !h && !b)\n                    throw new Error(\"Missing required arguments\");\n                  if (!o.string(h))\n                    throw new TypeError(\"Second argument must be a String\");\n                  if (!o.fn(b))\n                    throw new TypeError(\"Third argument must be a Function\");\n                  if (o.node(g))\n                    return _(g, h, b);\n                  if (o.nodeList(g))\n                    return x(g, h, b);\n                  if (o.string(g))\n                    return E(g, h, b);\n                  throw new TypeError(\"First argument must be a String, HTMLElement, HTMLCollection, or NodeList\");\n                }\n                function _(g, h, b) {\n                  return g.addEventListener(h, b), {\n                    destroy: function() {\n                      g.removeEventListener(h, b);\n                    }\n                  };\n                }\n                function x(g, h, b) {\n                  return Array.prototype.forEach.call(g, function(k) {\n                    k.addEventListener(h, b);\n                  }), {\n                    destroy: function() {\n                      Array.prototype.forEach.call(g, function(k) {\n                        k.removeEventListener(h, b);\n                      });\n                    }\n                  };\n                }\n                function E(g, h, b) {\n                  return r(document.body, g, h, b);\n                }\n                u.exports = v;\n              }\n            ),\n            /***/\n            817: (\n              /***/\n              function(u) {\n                function n(t) {\n                  var o;\n                  if (t.nodeName === \"SELECT\")\n                    t.focus(), o = t.value;\n                  else if (t.nodeName === \"INPUT\" || t.nodeName === \"TEXTAREA\") {\n                    var r = t.hasAttribute(\"readonly\");\n                    r || t.setAttribute(\"readonly\", \"\"), t.select(), t.setSelectionRange(0, t.value.length), r || t.removeAttribute(\"readonly\"), o = t.value;\n                  } else {\n                    t.hasAttribute(\"contenteditable\") && t.focus();\n                    var v = window.getSelection(), _ = document.createRange();\n                    _.selectNodeContents(t), v.removeAllRanges(), v.addRange(_), o = v.toString();\n                  }\n                  return o;\n                }\n                u.exports = n;\n              }\n            ),\n            /***/\n            279: (\n              /***/\n              function(u) {\n                function n() {\n                }\n                n.prototype = {\n                  on: function(t, o, r) {\n                    var v = this.e || (this.e = {});\n                    return (v[t] || (v[t] = [])).push({\n                      fn: o,\n                      ctx: r\n                    }), this;\n                  },\n                  once: function(t, o, r) {\n                    var v = this;\n                    function _() {\n                      v.off(t, _), o.apply(r, arguments);\n                    }\n                    return _._ = o, this.on(t, _, r);\n                  },\n                  emit: function(t) {\n                    var o = [].slice.call(arguments, 1), r = ((this.e || (this.e = {}))[t] || []).slice(), v = 0, _ = r.length;\n                    for (v; v < _; v++)\n                      r[v].fn.apply(r[v].ctx, o);\n                    return this;\n                  },\n                  off: function(t, o) {\n                    var r = this.e || (this.e = {}), v = r[t], _ = [];\n                    if (v && o)\n                      for (var x = 0, E = v.length; x < E; x++)\n                        v[x].fn !== o && v[x].fn._ !== o && _.push(v[x]);\n                    return _.length ? r[t] = _ : delete r[t], this;\n                  }\n                }, u.exports = n, u.exports.TinyEmitter = n;\n              }\n            )\n            /******/\n          }, l = {};\n          function p(u) {\n            if (l[u])\n              return l[u].exports;\n            var n = l[u] = {\n              /******/\n              // no module.id needed\n              /******/\n              // no module.loaded needed\n              /******/\n              exports: {}\n              /******/\n            };\n            return s[u](n, n.exports, p), n.exports;\n          }\n          return function() {\n            p.n = function(u) {\n              var n = u && u.__esModule ? (\n                /******/\n                function() {\n                  return u.default;\n                }\n              ) : (\n                /******/\n                function() {\n                  return u;\n                }\n              );\n              return p.d(n, { a: n }), n;\n            };\n          }(), function() {\n            p.d = function(u, n) {\n              for (var t in n)\n                p.o(n, t) && !p.o(u, t) && Object.defineProperty(u, t, { enumerable: !0, get: n[t] });\n            };\n          }(), function() {\n            p.o = function(u, n) {\n              return Object.prototype.hasOwnProperty.call(u, n);\n            };\n          }(), p(686);\n        }().default\n      );\n    });\n  }(L)), L.exports;\n}\nvar Me = Ve();\nconst $e = /* @__PURE__ */ Ne(Me), Re = function(e, m) {\n  let s = Date.now(), l;\n  return (...p) => {\n    Date.now() - s < m && l && clearTimeout(l), l = setTimeout(() => {\n      e(...p);\n    }, m), s = Date.now();\n  };\n}, Be = C({\n  name: \"JsonViewer\",\n  components: {\n    JsonBox: K\n  },\n  props: {\n    value: {\n      type: [Object, Array, String, Number, Boolean, Function],\n      required: !0\n    },\n    expanded: {\n      type: Boolean,\n      default: !1\n    },\n    expandDepth: {\n      type: Number,\n      default: 1\n    },\n    copyable: {\n      type: [Boolean, Object],\n      default: !1\n    },\n    sort: {\n      type: Boolean,\n      default: !1\n    },\n    boxed: {\n      type: Boolean,\n      default: !1\n    },\n    theme: {\n      type: String,\n      default: \"light\"\n    },\n    timeformat: {\n      type: Function,\n      default: (e) => e.toLocaleString()\n    },\n    previewMode: {\n      type: Boolean,\n      default: !1\n    },\n    parse: {\n      type: Boolean,\n      default: !1\n    }\n  },\n  emits: [\"onKeyClick\", \"copied\"],\n  /**\n   * Setup function for the JsonViewer component.\n   * @param props - The component's props.\n   * @param context - The setup context, including `emit`.\n   */\n  setup(e, { emit: m }) {\n    const s = w(!1), l = w(!1), p = w(e.expanded), u = w(null), n = w(null);\n    I(\"expandDepth\", e.expandDepth), I(\"timeformat\", e.timeformat), I(\"keyClick\", (h) => {\n      m(\"onKeyClick\", h);\n    });\n    const t = O(() => \"jv-container jv-\" + e.theme + (e.boxed ? \" boxed\" : \"\")), o = O(() => {\n      if (typeof e.copyable == \"boolean\" && !e.copyable)\n        return { copyText: \"copy\", copiedText: \"copied!\", timeout: 2e3, align: \"right\" };\n      const h = e.copyable;\n      return {\n        copyText: h.copyText || \"copy\",\n        copiedText: h.copiedText || \"copied!\",\n        timeout: h.timeout || 2e3,\n        align: h.align || \"right\"\n      };\n    }), r = O(() => {\n      if (!e.parse || typeof e.value != \"string\")\n        return e.value;\n      try {\n        return JSON.parse(e.value);\n      } catch {\n        return e.value;\n      }\n    }), v = () => {\n      x();\n    }, x = Re(() => {\n      fe(() => {\n        n.value && (n.value.$el.clientHeight >= 250 ? l.value = !0 : l.value = !1);\n      });\n    }, 200), E = (h) => {\n      s.value || (s.value = !0, setTimeout(() => {\n        s.value = !1;\n      }, o.value.timeout), m(\"copied\", h));\n    }, g = () => {\n      p.value = !p.value;\n    };\n    return J(() => e.value, () => {\n      v();\n    }), H(() => {\n      e.boxed && n.value && (v(), n.value.$el.addEventListener(\"resized\", v, !0)), e.copyable && u.value && new $e(u.value, {\n        text: () => JSON.stringify(r.value, null, 2)\n        // Use parseValue for copying\n      }).on(\"success\", E);\n    }), {\n      clip: u,\n      jsonBox: n,\n      copied: s,\n      expandableCode: l,\n      expandCode: p,\n      jvClass: t,\n      copyText: o,\n      parseValue: r,\n      toggleExpandCode: g\n      // Methods are already bound or don't need explicit exposure if not used in template\n      // onResized, // only called internally\n      // debResized, // only called internally\n      // onCopied, // only called internally\n    };\n  }\n}), Le = (e, m) => {\n  const s = e.__vccOpts || e;\n  for (const [l, p] of m)\n    s[l] = p;\n  return s;\n};\nfunction De(e, m, s, l, p, u) {\n  const n = de(\"json-box\");\n  return N(), A(\"div\", {\n    class: S(e.jvClass)\n  }, [\n    e.copyable ? (N(), A(\"div\", {\n      key: 0,\n      class: S(`jv-tooltip ${e.copyText.align || \"right\"}`)\n    }, [\n      q(\"span\", {\n        ref: \"clip\",\n        class: S([\"jv-button\", { copied: e.copied }])\n      }, [\n        pe(e.$slots, \"copy\", { copied: e.copied }, () => [\n          ve(V(e.copied ? e.copyText.copiedText : e.copyText.copyText), 1)\n        ])\n      ], 2)\n    ], 2)) : Z(\"\", !0),\n    q(\"div\", {\n      class: S([\"jv-code\", { open: e.expandCode, boxed: e.boxed }])\n    }, [\n      ye(n, {\n        ref: \"jsonBox\",\n        value: e.parseValue,\n        sort: e.sort,\n        \"preview-mode\": e.previewMode\n      }, null, 8, [\"value\", \"sort\", \"preview-mode\"])\n    ], 2),\n    e.expandableCode && e.boxed ? (N(), A(\"div\", {\n      key: 1,\n      class: \"jv-more\",\n      onClick: m[0] || (m[0] = (...t) => e.toggleExpandCode && e.toggleExpandCode(...t))\n    }, [\n      q(\"span\", {\n        class: S([\"jv-toggle\", { open: !!e.expandCode }])\n      }, null, 2)\n    ])) : Z(\"\", !0)\n  ], 2);\n}\nconst Q = /* @__PURE__ */ Le(Be, [[\"render\", De]]), Pe = (e) => {\n  e.component(Q.name, Q);\n}, qe = {\n  install: Pe\n};\nexport {\n  Q as JsonViewer,\n  qe as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAM,KAAK;AAAX,IAAuG,IAAI,gBAAE;AAAA,EAC3G,MAAM;AAAA,EACN,OAAO;AAAA;AAAA,IAEL,WAAW;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,GAAG;AACP,UAAM,IAAI,IAAE,IAAE,GAAG,IAAI,IAAE,KAAE,GAAG,IAAI,IAAE,IAAI,GAAG,IAAI,IAAE,IAAI;AACnD,cAAE,MAAM;AACN,QAAE,SAAS,EAAE,SAAS,EAAE,MAAM,eAAe,EAAE,MAAM,iBAAiB,EAAE,QAAQ;AAAA,IAClF,CAAC;AACD,UAAM,IAAI,MAAM;AACd,QAAE,QAAQ,CAAC,EAAE;AAAA,IACf;AACA,WAAO,MAAM;AACX,YAAM,IAAI,EAAE,WAAW,IAAI,GAAG,KAAK,CAAC;AACpC,UAAI;AACJ,OAAC,EAAE,SAAS,EAAE,QAAQ,IAAI,EAAE,QAAQ;AAAA,QAClC,OAAO,EAAE,eAAe,KAAG;AAAA,QAC3B,SAAS;AAAA,MACX,GAAG,KAAK,IAAI,IAAI,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,MAAI,aAAa,KAAG,GAAG,KAAK,EAAE,GAAG;AAAA,QACnF,EAAE,QAAQ,MAAM,GAAG;AAAA;AAAA,QAEnB,EAAE,KAAK,EAAE,MAAM,GAAG,QAAQ,UAAU,OAAO,UAAU,GAAG,CAAC;AAAA,QACzD,EAAE,QAAQ,MAAM,GAAG;AAAA;AAAA,MAErB,CAAC,IAAI,IAAI,EAAE,QAAQ;AAAA,QACjB,OAAO,EAAE,WAAW,MAAI,aAAa,KAAG;AAAA,QACxC,KAAK;AAAA;AAAA,MAEP,GAAG,IAAI,CAAC,GAAG;AACX,YAAM,IAAI,CAAC;AACX,aAAO,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ;AAAA,QACjC,OAAO;AAAA,UACL,aAAa;AAAA;AAAA,UAEb,MAAM,EAAE;AAAA;AAAA,QAEV;AAAA,QACA,SAAS;AAAA,MACX,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,kBAAkB,KAAG,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC;AAAA,IACjG;AAAA,EACF;AACF,CAAC;AAlDD,IAkDI,KAAK;AAAA,EACP,OAAuB,eAAE,CAAC,WAAW,cAAc,CAAC;AACtD;AApDA,IAoDG,KAAqB,gBAAE;AAAA,EACxB,QAAQ;AAAA,EACR,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAML,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,MAAM,GAAG;AACP,UAAM,IAAI,GAAG,IAAI,SAAE,MAAM,EAAE,cAAc,OAAO,SAAS,WAAW;AACpE,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,QAAQ,IAAI,gBAAE,EAAE,KAAK,GAAG,CAAC;AAAA,EACpD;AACF,CAAC;AArED,IAqEI,KAAqB,gBAAE;AAAA,EACzB,QAAQ;AAAA,EACR,OAAO;AAAA;AAAA,IAEL,WAAW;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,MAAM,GAAG;AACP,UAAM,IAAI,GAAG,IAAI,SAAE,MAAM,OAAO,UAAU,EAAE,SAAS,CAAC;AACtD,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,QAAQ;AAAA,MAC/B,OAAO,eAAE,CAAC,WAAW,aAAa,EAAE,QAAQ,sBAAsB,iBAAiB,CAAC;AAAA,IACtF,GAAG,gBAAE,EAAE,UAAU,SAAS,CAAC,GAAG,CAAC;AAAA,EACjC;AACF,CAAC;AApFD,IAoFI,KAAK;AAAA,EACP,OAAuB,eAAE,CAAC,WAAW,YAAY,CAAC;AACpD;AAtFA,IAsFG,KAAqB,gBAAE;AAAA,EACxB,QAAQ;AAAA,EACR,OAAO;AAAA;AAAA,IAEL,WAAW;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,MAAM,GAAG;AACP,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,QAAQ,IAAI,gBAAE,EAAE,UAAU,SAAS,CAAC,GAAG,CAAC;AAAA,EACnE;AACF,CAAC;AAlGD,IAkGI,KAAK,gBAAE;AAAA,EACT,MAAM;AAAA,EACN,OAAO;AAAA;AAAA,IAEL,WAAW;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA;AAAA,IAEA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,QAAQ;AAAA;AAAA,IAER,MAAM;AAAA;AAAA,IAEN,aAAa;AAAA,EACf;AAAA,EACA,OAAO,CAAC,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvB,MAAM,GAAG,EAAE,MAAM,EAAE,GAAG;AACpB,UAAM,IAAI,IAAE,CAAC,CAAC;AACd,QAAI,IAAI;AACR,UAAM,IAAI,CAAC,MAAM;AACf,iBAAW,MAAM;AACf,UAAE,QAAQ;AAAA,MACZ,GAAG,CAAC;AAAA,IACN;AACA,UAAE,MAAM,EAAE,WAAW,CAAC,MAAM;AAC1B,QAAE,CAAC;AAAA,IACL,GAAG,EAAE,WAAW,MAAI,MAAM,KAAG,CAAC;AAC9B,UAAM,IAAI,SAAE,MAAM;AAChB,UAAI,CAAC,EAAE;AACL,eAAO,EAAE;AACX,YAAM,IAAI,OAAO,KAAK,EAAE,KAAK,EAAE,KAAK,GAAG,IAAI,CAAC;AAC5C,aAAO,EAAE,QAAQ,CAAC,MAAM;AACtB,UAAE,CAAC,IAAI,EAAE,MAAM,CAAC;AAAA,MAClB,CAAC,GAAG;AAAA,IACN,CAAC,GAAG,IAAI,MAAM;AACZ,UAAI;AACF,YAAI;AACF,YAAE,cAAc,IAAI,MAAM,SAAS,CAAC;AAAA,QACtC,QAAQ;AACN,gBAAM,IAAI,SAAS,YAAY,OAAO;AACtC,YAAE,UAAU,WAAW,MAAI,KAAE,GAAG,EAAE,cAAc,CAAC;AAAA,QACnD;AAAA,IACJ,GAAG,IAAI,MAAM;AACX,QAAE,iBAAiB,CAAC,EAAE,MAAM,GAAG,EAAE;AAAA,IACnC;AACA,WAAO,MAAM;AACX,YAAM,IAAI,CAAC;AACX,UAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,WAAW,WAAW,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE,QAAQ;AACzE,mBAAW,KAAK,EAAE;AAChB,cAAI,EAAE,MAAM,eAAe,CAAC,GAAG;AAC7B,kBAAM,IAAI,EAAE,MAAM,CAAC;AACnB,cAAE,KAAK,EAAE,GAAG;AAAA,cACV,KAAK;AAAA;AAAA,cAEL,MAAM,EAAE;AAAA,cACR,SAAS;AAAA,cACT,OAAO,EAAE,QAAQ;AAAA,cACjB,OAAO;AAAA,cACP,aAAa,EAAE;AAAA,YACjB,CAAC,CAAC;AAAA,UACJ;AAAA,MACJ;AACA,aAAO,CAAC,EAAE,UAAU,OAAO,KAAK,EAAE,KAAK,EAAE,SAAS,KAAK,EAAE,KAAK,EAAE,QAAQ;AAAA;AAAA,QAEtE,OAAO;AAAA,QACP,SAAS;AAAA,QACT,OAAO,yCAAyC,OAAO,KAAK,EAAE,KAAK,EAAE,KAAK,IAAI,CAAC;AAAA,MACjF,GAAG,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC,WAAW,WAAW,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,CAAC,MAAM;AAC/F,YAAI;AAAA,MACN,EAAE,GAAG,CAAC;AAAA,IACR;AAAA,EACF;AACF,CAAC;AAzLD,IAyLI,KAAK,gBAAE;AAAA,EACT,MAAM;AAAA,EACN,OAAO;AAAA;AAAA,IAEL,WAAW;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA;AAAA,IAEA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,MAAM;AAAA;AAAA;AAAA,IAGN,QAAQ;AAAA;AAAA,IAER,aAAa;AAAA,EACf;AAAA,EACA,OAAO,CAAC,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvB,MAAM,GAAG,EAAE,MAAM,EAAE,GAAG;AACpB,UAAM,IAAI,IAAE,CAAC,CAAC;AACd,QAAI,IAAI;AACR,UAAM,IAAI,CAAC,GAAG,IAAI,MAAM;AACtB,YAAM,MAAM,EAAE,QAAQ,CAAC,IAAI,WAAW,MAAM;AAC1C,aAAK,EAAE,SAAS,MAAM,EAAE,MAAM,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;AAAA,MACtD,GAAG,CAAC;AAAA,IACN;AACA,UAAE,MAAM,EAAE,WAAW,CAAC,MAAM;AAC1B,QAAE,CAAC;AAAA,IACL,GAAG,EAAE,WAAW,MAAI,MAAM,KAAG,CAAC;AAC9B,UAAM,IAAI,MAAM;AACd,UAAI,EAAE,iBAAiB,CAAC,EAAE,MAAM,GAAG;AACjC,YAAI;AACF,YAAE,cAAc,IAAI,MAAM,SAAS,CAAC;AAAA,QACtC,QAAQ;AACN,gBAAM,IAAI,SAAS,YAAY,OAAO;AACtC,YAAE,UAAU,WAAW,MAAI,KAAE,GAAG,EAAE,cAAc,CAAC;AAAA,QACnD;AAAA,IACJ;AACA,WAAO,MAAM;AACX,YAAM,IAAI,CAAC;AACX,aAAO,CAAC,EAAE,eAAe,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ;AAAA,QACtD,OAAO;AAAA,UACL,aAAa;AAAA,UACb,MAAM,CAAC,CAAC,EAAE;AAAA,QACZ;AAAA,QACA,SAAS;AAAA,MACX,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ;AAAA,QACpB,OAAO,CAAC,WAAW,UAAU;AAAA,MAC/B,GAAG,GAAG,CAAC,GAAG,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC,GAAG,MAAM;AAC9C,UAAE,KAAK,EAAE,GAAG;AAAA;AAAA,UAEV,KAAK;AAAA;AAAA,UAEL,MAAM,EAAE;AAAA,UACR,OAAO,EAAE,QAAQ;AAAA,UACjB,OAAO;AAAA,UACP,aAAa,EAAE;AAAA,QACjB,CAAC,CAAC;AAAA,MACJ,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,MAAM,SAAS,KAAK,EAAE,KAAK,EAAE,QAAQ;AAAA,QACtD,OAAO;AAAA,QACP,SAAS;AAAA,QACT,OAAO,mBAAmB,EAAE,MAAM,MAAM;AAAA,MAC1C,GAAG,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ;AAAA,QAC3B,OAAO,CAAC,WAAW,UAAU;AAAA,MAC/B,GAAG,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,CAAC,MAAM;AAChC,YAAI;AAAA,MACN,EAAE,GAAG,CAAC;AAAA,IACR;AAAA,EACF;AACF,CAAC;AA5QD,IA4QI,KAAK,CAAC,OAAO;AA5QjB,IA4QoB,KAAqB,gBAAE;AAAA,EACzC,QAAQ;AAAA,EACR,OAAO;AAAA;AAAA,IAEL,WAAW;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,MAAM,GAAG;AACP,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,QAAQ;AAAA,MAC/B,OAAO,eAAE,CAAC,WAAW,aAAa,CAAC;AAAA,MACnC,OAAO,EAAE,UAAU,SAAS;AAAA,IAC9B,GAAG,gBAAgB,GAAG,EAAE;AAAA,EAC1B;AACF,CAAC;AA3RD,IA2RI,KAAK;AAAA,EACP,OAAuB,eAAE,CAAC,WAAW,WAAW,CAAC;AACnD;AA7RA,IA6RG,KAAqB,gBAAE;AAAA,EACxB,QAAQ;AAAA,EACR,OAAO;AAAA;AAAA,IAEL,WAAW;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,MAAM,GAAG;AACP,UAAM,IAAI,GAAG,IAAI,OAAE,cAAc,CAAC,MAAM,EAAE,eAAe,CAAC,GAAG,IAAI,SAAE,MAAM,EAAE,EAAE,SAAS,CAAC;AACvF,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,QAAQ,IAAI,OAAO,gBAAE,EAAE,KAAK,IAAI,MAAM,CAAC;AAAA,EAClE;AACF,CAAC;AA1SD,IA0SI,KAAK;AAAA,EACP,OAAuB,eAAE,CAAC,WAAW,WAAW,CAAC;AACnD;AA5SA,IA4SG,KAAqB,gBAAE;AAAA,EACxB,QAAQ;AAAA,EACR,OAAO;AAAA;AAAA,IAEL,WAAW;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,EACF;AAAA,EACA,MAAM,GAAG;AACP,WAAO,CAAC,GAAG,OAAO,UAAE,GAAG,mBAAE,QAAQ,IAAI,gBAAE,EAAE,UAAU,SAAS,CAAC,GAAG,CAAC;AAAA,EACnE;AACF,CAAC;AAxTD,IAwTI,IAAI,gBAAE;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA;AAAA,IAEL,OAAO;AAAA,MACL,MAAM,CAAC,QAAQ,OAAO,QAAQ,QAAQ,SAAS,UAAU,IAAI;AAAA,MAC7D,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,MAAM;AAAA;AAAA,IAEN,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA,IAEA,aAAa;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,GAAG;AACP,UAAM,IAAI,OAAE,eAAe,IAAI,CAAC,GAAG,IAAI,OAAE,YAAY,MAAM;AAAA,IAC3D,CAAC,GAAG,IAAI,IAAE,IAAE;AACZ,QAAI,IAAI;AACR,cAAE,MAAM;AACN,QAAE,QAAQ,EAAE,eAAe,EAAE,QAAQ;AAAA,IACvC,CAAC;AACD,UAAM,IAAI,MAAM;AACd,UAAI,EAAE,QAAQ,CAAC,EAAE,OAAO;AACtB,YAAI;AACF,YAAE,cAAc,IAAI,MAAM,SAAS,CAAC;AAAA,QACtC,QAAQ;AACN,gBAAM,IAAI,SAAS,YAAY,OAAO;AACtC,YAAE,UAAU,WAAW,MAAI,KAAE,GAAG,EAAE,cAAc,CAAC;AAAA,QACnD;AAAA,IACJ;AACA,WAAO,MAAM;AACX,YAAM,IAAI,CAAC;AACX,UAAI;AACJ,QAAE,UAAU,QAAQ,EAAE,UAAU,SAAS,IAAI,KAAK,MAAM,QAAQ,EAAE,KAAK,IAAI,IAAI,KAAK,EAAE,iBAAiB,OAAO,IAAI,KAAK,EAAE,iBAAiB,SAAS,IAAI,KAAK,OAAO,EAAE,SAAS,WAAW,IAAI,KAAK,OAAO,EAAE,SAAS,WAAW,IAAI,KAAK,OAAO,EAAE,SAAS,WAAW,IAAI,IAAI,OAAO,EAAE,SAAS,YAAY,IAAI,KAAK,OAAO,EAAE,SAAS,aAAa,IAAI,KAAK,IAAI;AAChW,YAAM,IAAI,EAAE,UAAU,MAAM,QAAQ,EAAE,KAAK,KAAK,OAAO,EAAE,SAAS,YAAY,EAAE,EAAE,iBAAiB;AAAA,MACnG,EAAE,EAAE,iBAAiB;AACrB,aAAO,CAAC,EAAE,eAAe,KAAK,EAAE,EAAE,iBAAiB,WAAW,EAAE;AAAA,QAC9D,EAAE,QAAQ;AAAA,UACR,OAAO;AAAA,YACL,aAAa;AAAA,YACb,MAAM,CAAC,CAAC,EAAE;AAAA;AAAA,UAEZ;AAAA,UACA,SAAS;AAAA,QACX,CAAC;AAAA,MACH,GAAG,EAAE,WAAW,EAAE;AAAA,QAChB,EAAE,QAAQ;AAAA,UACR,OAAO;AAAA;AAAA,UAEP,SAAS,MAAM;AACb,iBAAK,EAAE,EAAE,OAAO;AAAA,UAClB;AAAA,QACF,GAAG,GAAG,EAAE,OAAO,GAAG;AAAA;AAAA,MAEpB,GAAG,EAAE;AAAA,QACH,EAAE,GAAG;AAAA,UACH,OAAO;AAAA;AAAA,UAEP,WAAW,EAAE;AAAA,UACb,SAAS,EAAE;AAAA;AAAA,UAEX,MAAM,EAAE;AAAA;AAAA,UAER,OAAO,EAAE;AAAA;AAAA,UAET,QAAQ,EAAE;AAAA;AAAA,UAEV,aAAa,EAAE;AAAA;AAAA;AAAA,UAGf,mBAAmB,CAAC,MAAM;AACxB,cAAE,QAAQ;AAAA,UACZ;AAAA,QACF,CAAC;AAAA,MACH,GAAG;AAAA,QACD;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW;AAAA,YACX,eAAe,CAAC,CAAC,EAAE,WAAW,CAAC;AAAA;AAAA,YAE/B,QAAQ,CAAC,EAAE,eAAe;AAAA;AAAA,UAE5B;AAAA,UACA,KAAK,CAAC,MAAM;AACV,gBAAI;AAAA,UACN;AAAA,QACF;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF,CAAC;AACD,SAAS,GAAG,GAAG;AACb,SAAO,KAAK,EAAE,cAAc,OAAO,UAAU,eAAe,KAAK,GAAG,SAAS,IAAI,EAAE,UAAU;AAC/F;AACA,IAAI,IAAI,EAAE,SAAS,CAAC,EAAE;AAOtB,IAAI,KAAK,EAAE;AAAX,IAAoB;AACpB,SAAS,KAAK;AACZ,SAAO,MAAM,IAAI,GAAG,SAAS,GAAG,GAAG;AACjC,KAAC,SAAS,GAAG,GAAG;AACd,QAAE,UAAU,EAAE;AAAA,IAChB,GAAG,IAAI,WAAW;AAChB;AAAA;AAAA,QAEE,WAAW;AACT,cAAI,IAAI;AAAA;AAAA,YAEN;AAAA;AAAA,cAEE,SAAS,GAAG,GAAG,GAAG;AAChB,kBAAE,EAAE,GAAG;AAAA,kBACL,SAAS,WAAW;AAClB;AAAA;AAAA,sBAEE;AAAA;AAAA,kBAEJ;AAAA,gBACF,CAAC;AACD,oBAAI,IAAI,EAAE,GAAG,GAAG,IAAoB,EAAE,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,GAAG,IAAoB,EAAE,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,GAAG,IAAoB,EAAE,EAAE,CAAC;AACzH,yBAAS,EAAE,GAAG;AACZ,sBAAI;AACF,2BAAO,SAAS,YAAY,CAAC;AAAA,kBAC/B,QAAQ;AACN,2BAAO;AAAA,kBACT;AAAA,gBACF;AACA,oBAAIA,KAAI,SAAS,GAAG;AAClB,sBAAI,IAAI,EAAE,EAAE,CAAC;AACb,yBAAO,EAAE,KAAK,GAAG;AAAA,gBACnB,GAAG,IAAIA;AACP,yBAAS,EAAE,GAAG;AACZ,sBAAI,IAAI,SAAS,gBAAgB,aAAa,KAAK,MAAM,OAAO,IAAI,SAAS,cAAc,UAAU;AACrG,oBAAE,MAAM,WAAW,QAAQ,EAAE,MAAM,SAAS,KAAK,EAAE,MAAM,UAAU,KAAK,EAAE,MAAM,SAAS,KAAK,EAAE,MAAM,WAAW,YAAY,EAAE,MAAM,IAAI,UAAU,MAAM,IAAI;AAC7J,sBAAI,IAAI,OAAO,eAAe,SAAS,gBAAgB;AACvD,yBAAO,EAAE,MAAM,MAAM,GAAG,OAAO,GAAG,IAAI,GAAG,EAAE,aAAa,YAAY,EAAE,GAAG,EAAE,QAAQ,GAAG;AAAA,gBACxF;AACA,oBAAI,IAAI,SAAS,GAAG,GAAG;AACrB,sBAAI,IAAI,EAAE,CAAC;AACX,oBAAE,UAAU,YAAY,CAAC;AACzB,sBAAI,IAAI,EAAE,EAAE,CAAC;AACb,yBAAO,EAAE,MAAM,GAAG,EAAE,OAAO,GAAG;AAAA,gBAChC,GAAG,IAAI,SAAS,GAAG;AACjB,sBAAI,IAAI,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI;AAAA,oBACvE,WAAW,SAAS;AAAA,kBACtB,GAAG,IAAI;AACP,yBAAO,OAAO,KAAK,WAAW,IAAI,EAAE,GAAG,CAAC,IAAI,aAAa,oBAAoB,CAAC,CAAC,QAAQ,UAAU,OAAO,OAAO,UAAU,EAAE,SAAS,KAAK,OAAO,SAAS,EAAE,IAAI,IAAI,IAAI,EAAE,EAAE,OAAO,CAAC,KAAK,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM,IAAI;AAAA,gBAClN,GAAG,IAAI;AACP,yBAAS,EAAE,GAAG;AACZ;AACA,yBAAO,OAAO,UAAU,cAAc,OAAO,OAAO,YAAY,WAAW,IAAI,SAAS,GAAG;AACzF,2BAAO,OAAO;AAAA,kBAChB,IAAI,IAAI,SAAS,GAAG;AAClB,2BAAO,KAAK,OAAO,UAAU,cAAc,EAAE,gBAAgB,UAAU,MAAM,OAAO,YAAY,WAAW,OAAO;AAAA,kBACpH,GAAG,EAAE,CAAC;AAAA,gBACR;AACA,oBAAI,KAAK,WAAW;AAClB,sBAAI,IAAI,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,QAAQ,IAAI,MAAM,SAAS,SAAS,GAAG,IAAI,EAAE,WAAW,IAAI,EAAE,QAAQ,IAAI,EAAE;AAC/J,sBAAI,MAAM,UAAU,MAAM;AACxB,0BAAM,IAAI,MAAM,oDAAoD;AACtE,sBAAI,MAAM;AACR,wBAAI,KAAK,EAAE,CAAC,MAAM,YAAY,EAAE,aAAa,GAAG;AAC9C,0BAAI,MAAM,UAAU,EAAE,aAAa,UAAU;AAC3C,8BAAM,IAAI,MAAM,mFAAmF;AACrG,0BAAI,MAAM,UAAU,EAAE,aAAa,UAAU,KAAK,EAAE,aAAa,UAAU;AACzE,8BAAM,IAAI,MAAM,uGAAuG;AAAA,oBAC3H;AACE,4BAAM,IAAI,MAAM,6CAA6C;AACjE,sBAAI;AACF,2BAAO,EAAE,GAAG;AAAA,sBACV,WAAW;AAAA,oBACb,CAAC;AACH,sBAAI;AACF,2BAAO,MAAM,QAAQ,EAAE,CAAC,IAAI,EAAE,GAAG;AAAA,sBAC/B,WAAW;AAAA,oBACb,CAAC;AAAA,gBACL,GAAG,KAAK;AACR,yBAAS,EAAE,GAAG;AACZ;AACA,yBAAO,OAAO,UAAU,cAAc,OAAO,OAAO,YAAY,WAAW,IAAI,SAAS,GAAG;AACzF,2BAAO,OAAO;AAAA,kBAChB,IAAI,IAAI,SAAS,GAAG;AAClB,2BAAO,KAAK,OAAO,UAAU,cAAc,EAAE,gBAAgB,UAAU,MAAM,OAAO,YAAY,WAAW,OAAO;AAAA,kBACpH,GAAG,EAAE,CAAC;AAAA,gBACR;AACA,yBAAS,GAAG,GAAG,GAAG;AAChB,sBAAI,EAAE,aAAa;AACjB,0BAAM,IAAI,UAAU,mCAAmC;AAAA,gBAC3D;AACA,yBAAS,EAAE,GAAG,GAAG;AACf,2BAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,wBAAI,IAAI,EAAE,CAAC;AACX,sBAAE,aAAa,EAAE,cAAc,OAAI,EAAE,eAAe,MAAI,WAAW,MAAM,EAAE,WAAW,OAAK,OAAO,eAAe,GAAG,EAAE,KAAK,CAAC;AAAA,kBAC9H;AAAA,gBACF;AACA,yBAAS,GAAG,GAAG,GAAG,GAAG;AACnB,yBAAO,KAAK,EAAE,EAAE,WAAW,CAAC,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG;AAAA,gBAC/C;AACA,yBAAS,GAAG,GAAG,GAAG;AAChB,sBAAI,OAAO,KAAK,cAAc,MAAM;AAClC,0BAAM,IAAI,UAAU,oDAAoD;AAC1E,oBAAE,YAAY,OAAO,OAAO,KAAK,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,GAAG,UAAU,MAAI,cAAc,KAAG,EAAE,CAAC,GAAG,KAAK,EAAE,GAAG,CAAC;AAAA,gBAC3H;AACA,yBAAS,EAAE,GAAG,GAAG;AACf,yBAAO,IAAI,OAAO,kBAAkB,SAAS,GAAG,GAAG;AACjD,2BAAO,EAAE,YAAY,GAAG;AAAA,kBAC1B,GAAG,EAAE,GAAG,CAAC;AAAA,gBACX;AACA,yBAAS,GAAG,GAAG;AACb,sBAAI,IAAI,GAAG;AACX,yBAAO,WAAW;AAChB,wBAAI,IAAI,EAAE,CAAC,GAAG;AACd,wBAAI,GAAG;AACL,0BAAI,IAAI,EAAE,IAAI,EAAE;AAChB,0BAAI,QAAQ,UAAU,GAAG,WAAW,CAAC;AAAA,oBACvC;AACE,0BAAI,EAAE,MAAM,MAAM,SAAS;AAC7B,2BAAO,GAAG,MAAM,CAAC;AAAA,kBACnB;AAAA,gBACF;AACA,yBAAS,GAAG,GAAG,GAAG;AAChB,yBAAO,MAAM,EAAE,CAAC,MAAM,YAAY,OAAO,KAAK,cAAc,IAAI,GAAG,CAAC;AAAA,gBACtE;AACA,yBAAS,GAAG,GAAG;AACb,sBAAI,MAAM;AACR,0BAAM,IAAI,eAAe,2DAA2D;AACtF,yBAAO;AAAA,gBACT;AACA,yBAAS,KAAK;AACZ,sBAAI,OAAO,UAAU,OAAO,CAAC,QAAQ,aAAa,QAAQ,UAAU,KAAM,QAAO;AACjF,sBAAI,OAAO,SAAS,WAAY,QAAO;AACvC,sBAAI;AACF,2BAAO,KAAK,UAAU,SAAS,KAAK,QAAQ,UAAU,MAAM,CAAC,GAAG,WAAW;AAAA,oBAC3E,CAAC,CAAC,GAAG;AAAA,kBACP,QAAQ;AACN,2BAAO;AAAA,kBACT;AAAA,gBACF;AACA,yBAAS,EAAE,GAAG;AACZ,yBAAO,IAAI,OAAO,iBAAiB,OAAO,iBAAiB,SAAS,GAAG;AACrE,2BAAO,EAAE,aAAa,OAAO,eAAe,CAAC;AAAA,kBAC/C,GAAG,EAAE,CAAC;AAAA,gBACR;AACA,yBAAS,EAAE,GAAG,GAAG;AACf,sBAAI,IAAI,kBAAkB,OAAO,CAAC;AAClC,sBAAI,EAAE,aAAa,CAAC;AAClB,2BAAO,EAAE,aAAa,CAAC;AAAA,gBAC3B;AACA,oBAAI,KAAqB,SAAS,GAAG;AACnC,qBAAG,GAAG,CAAC;AACP,sBAAI,IAAI,GAAG,CAAC;AACZ,2BAAS,EAAE,GAAG,GAAG;AACf,wBAAI;AACJ,2BAAO,GAAG,MAAM,CAAC,GAAG,IAAI,EAAE,KAAK,IAAI,GAAG,EAAE,eAAe,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG;AAAA,kBAC/E;AACA,yBAAO,GAAG,GAAG,CAAC;AAAA,oBACZ,KAAK;AAAA,oBACL,OAAO,WAAW;AAChB,0BAAI,IAAI,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI,CAAC;AAC1E,2BAAK,SAAS,OAAO,EAAE,UAAU,aAAa,EAAE,SAAS,KAAK,eAAe,KAAK,SAAS,OAAO,EAAE,UAAU,aAAa,EAAE,SAAS,KAAK,eAAe,KAAK,OAAO,OAAO,EAAE,QAAQ,aAAa,EAAE,OAAO,KAAK,aAAa,KAAK,YAAY,EAAE,EAAE,SAAS,MAAM,WAAW,EAAE,YAAY,SAAS;AAAA,oBACvS;AAAA;AAAA;AAAA;AAAA;AAAA,kBAKF,GAAG;AAAA,oBACD,KAAK;AAAA,oBACL,OAAO,SAAS,GAAG;AACjB,0BAAI,IAAI;AACR,2BAAK,WAAW,EAAE,EAAE,GAAG,SAAS,SAAS,GAAG;AAC1C,+BAAO,EAAE,QAAQ,CAAC;AAAA,sBACpB,CAAC;AAAA,oBACH;AAAA;AAAA;AAAA;AAAA;AAAA,kBAKF,GAAG;AAAA,oBACD,KAAK;AAAA,oBACL,OAAO,SAAS,GAAG;AACjB,0BAAI,IAAI,EAAE,kBAAkB,EAAE,eAAe,IAAI,KAAK,OAAO,CAAC,KAAK,QAAQ,IAAI,GAAG;AAAA,wBAChF,QAAQ;AAAA,wBACR,WAAW,KAAK;AAAA,wBAChB,QAAQ,KAAK,OAAO,CAAC;AAAA,wBACrB,MAAM,KAAK,KAAK,CAAC;AAAA,sBACnB,CAAC;AACD,2BAAK,KAAK,IAAI,YAAY,SAAS;AAAA,wBACjC,QAAQ;AAAA,wBACR,MAAM;AAAA,wBACN,SAAS;AAAA,wBACT,gBAAgB,WAAW;AACzB,+BAAK,EAAE,MAAM,GAAG,OAAO,aAAa,EAAE,gBAAgB;AAAA,wBACxD;AAAA,sBACF,CAAC;AAAA,oBACH;AAAA;AAAA;AAAA;AAAA;AAAA,kBAKF,GAAG;AAAA,oBACD,KAAK;AAAA,oBACL,OAAO,SAAS,GAAG;AACjB,6BAAO,EAAE,UAAU,CAAC;AAAA,oBACtB;AAAA;AAAA;AAAA;AAAA;AAAA,kBAKF,GAAG;AAAA,oBACD,KAAK;AAAA,oBACL,OAAO,SAAS,GAAG;AACjB,0BAAI,IAAI,EAAE,UAAU,CAAC;AACrB,0BAAI;AACF,+BAAO,SAAS,cAAc,CAAC;AAAA,oBACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAOF,GAAG;AAAA,oBACD,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,oBAKL,OAAO,SAAS,GAAG;AACjB,6BAAO,EAAE,QAAQ,CAAC;AAAA,oBACpB;AAAA;AAAA;AAAA;AAAA,kBAIF,GAAG;AAAA,oBACD,KAAK;AAAA,oBACL,OAAO,WAAW;AAChB,2BAAK,SAAS,QAAQ;AAAA,oBACxB;AAAA,kBACF,CAAC,GAAG,CAAC;AAAA,oBACH,KAAK;AAAA,oBACL,OAAO,SAAS,GAAG;AACjB,0BAAI,IAAI,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI;AAAA,wBACvE,WAAW,SAAS;AAAA,sBACtB;AACA,6BAAO,EAAE,GAAG,CAAC;AAAA,oBACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAMF,GAAG;AAAA,oBACD,KAAK;AAAA,oBACL,OAAO,SAAS,GAAG;AACjB,6BAAO,EAAE,CAAC;AAAA,oBACZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAMF,GAAG;AAAA,oBACD,KAAK;AAAA,oBACL,OAAO,WAAW;AAChB,0BAAI,IAAI,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS,UAAU,CAAC,IAAI,CAAC,QAAQ,KAAK,GAAG,IAAI,OAAO,KAAK,WAAW,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,SAAS;AAC7I,6BAAO,EAAE,QAAQ,SAAS,GAAG;AAC3B,4BAAI,KAAK,CAAC,CAAC,SAAS,sBAAsB,CAAC;AAAA,sBAC7C,CAAC,GAAG;AAAA,oBACN;AAAA,kBACF,CAAC,CAAC,GAAG;AAAA,gBACP,EAAE,EAAE,CAAC,GAAG,KAAK;AAAA,cACf;AAAA;AAAA;AAAA,YAGF;AAAA;AAAA,cAEE,SAAS,GAAG;AACV,oBAAI,IAAI;AACR,oBAAI,OAAO,UAAU,OAAO,CAAC,QAAQ,UAAU,SAAS;AACtD,sBAAI,IAAI,QAAQ;AAChB,oBAAE,UAAU,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE;AAAA,gBAC1G;AACA,yBAAS,EAAE,GAAG,GAAG;AACf,yBAAO,KAAK,EAAE,aAAa,KAAK;AAC9B,wBAAI,OAAO,EAAE,WAAW,cAAc,EAAE,QAAQ,CAAC;AAC/C,6BAAO;AACT,wBAAI,EAAE;AAAA,kBACR;AAAA,gBACF;AACA,kBAAE,UAAU;AAAA,cACd;AAAA;AAAA;AAAA,YAGF;AAAA;AAAA,cAEE,SAAS,GAAG,GAAG,GAAG;AAChB,oBAAI,IAAI,EAAE,GAAG;AACb,yBAAS,EAAE,GAAG,GAAG,GAAGA,IAAG,GAAG;AACxB,sBAAI,IAAI,EAAE,MAAM,MAAM,SAAS;AAC/B,yBAAO,EAAE,iBAAiB,GAAG,GAAG,CAAC,GAAG;AAAA,oBAClC,SAAS,WAAW;AAClB,wBAAE,oBAAoB,GAAG,GAAG,CAAC;AAAA,oBAC/B;AAAA,kBACF;AAAA,gBACF;AACA,yBAAS,EAAE,GAAG,GAAG,GAAGA,IAAG,GAAG;AACxB,yBAAO,OAAO,EAAE,oBAAoB,aAAa,EAAE,MAAM,MAAM,SAAS,IAAI,OAAO,KAAK,aAAa,EAAE,KAAK,MAAM,QAAQ,EAAE,MAAM,MAAM,SAAS,KAAK,OAAO,KAAK,aAAa,IAAI,SAAS,iBAAiB,CAAC,IAAI,MAAM,UAAU,IAAI,KAAK,GAAG,SAAS,GAAG;AACxP,2BAAO,EAAE,GAAG,GAAG,GAAGA,IAAG,CAAC;AAAA,kBACxB,CAAC;AAAA,gBACH;AACA,yBAAS,EAAE,GAAG,GAAG,GAAGA,IAAG;AACrB,yBAAO,SAAS,GAAG;AACjB,sBAAE,iBAAiB,EAAE,EAAE,QAAQ,CAAC,GAAG,EAAE,kBAAkBA,GAAE,KAAK,GAAG,CAAC;AAAA,kBACpE;AAAA,gBACF;AACA,kBAAE,UAAU;AAAA,cACd;AAAA;AAAA;AAAA,YAGF;AAAA;AAAA,cAEE,SAAS,GAAG,GAAG;AACb,kBAAE,OAAO,SAAS,GAAG;AACnB,yBAAO,MAAM,UAAU,aAAa,eAAe,EAAE,aAAa;AAAA,gBACpE,GAAG,EAAE,WAAW,SAAS,GAAG;AAC1B,sBAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC;AACxC,yBAAO,MAAM,WAAW,MAAM,uBAAuB,MAAM,8BAA8B,YAAY,MAAM,EAAE,WAAW,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AAAA,gBAC1I,GAAG,EAAE,SAAS,SAAS,GAAG;AACxB,yBAAO,OAAO,KAAK,YAAY,aAAa;AAAA,gBAC9C,GAAG,EAAE,KAAK,SAAS,GAAG;AACpB,sBAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC;AACxC,yBAAO,MAAM;AAAA,gBACf;AAAA,cACF;AAAA;AAAA;AAAA,YAGF;AAAA;AAAA,cAEE,SAAS,GAAG,GAAG,GAAG;AAChB,oBAAI,IAAI,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG;AACzB,yBAAS,EAAE,GAAGA,IAAG,GAAG;AAClB,sBAAI,CAAC,KAAK,CAACA,MAAK,CAAC;AACf,0BAAM,IAAI,MAAM,4BAA4B;AAC9C,sBAAI,CAAC,EAAE,OAAOA,EAAC;AACb,0BAAM,IAAI,UAAU,kCAAkC;AACxD,sBAAI,CAAC,EAAE,GAAG,CAAC;AACT,0BAAM,IAAI,UAAU,mCAAmC;AACzD,sBAAI,EAAE,KAAK,CAAC;AACV,2BAAO,EAAE,GAAGA,IAAG,CAAC;AAClB,sBAAI,EAAE,SAAS,CAAC;AACd,2BAAO,EAAE,GAAGA,IAAG,CAAC;AAClB,sBAAI,EAAE,OAAO,CAAC;AACZ,2BAAO,EAAE,GAAGA,IAAG,CAAC;AAClB,wBAAM,IAAI,UAAU,2EAA2E;AAAA,gBACjG;AACA,yBAAS,EAAE,GAAGA,IAAG,GAAG;AAClB,yBAAO,EAAE,iBAAiBA,IAAG,CAAC,GAAG;AAAA,oBAC/B,SAAS,WAAW;AAClB,wBAAE,oBAAoBA,IAAG,CAAC;AAAA,oBAC5B;AAAA,kBACF;AAAA,gBACF;AACA,yBAAS,EAAE,GAAGA,IAAG,GAAG;AAClB,yBAAO,MAAM,UAAU,QAAQ,KAAK,GAAG,SAAS,GAAG;AACjD,sBAAE,iBAAiBA,IAAG,CAAC;AAAA,kBACzB,CAAC,GAAG;AAAA,oBACF,SAAS,WAAW;AAClB,4BAAM,UAAU,QAAQ,KAAK,GAAG,SAAS,GAAG;AAC1C,0BAAE,oBAAoBA,IAAG,CAAC;AAAA,sBAC5B,CAAC;AAAA,oBACH;AAAA,kBACF;AAAA,gBACF;AACA,yBAAS,EAAE,GAAGA,IAAG,GAAG;AAClB,yBAAO,EAAE,SAAS,MAAM,GAAGA,IAAG,CAAC;AAAA,gBACjC;AACA,kBAAE,UAAU;AAAA,cACd;AAAA;AAAA;AAAA,YAGF;AAAA;AAAA,cAEE,SAAS,GAAG;AACV,yBAAS,EAAE,GAAG;AACZ,sBAAI;AACJ,sBAAI,EAAE,aAAa;AACjB,sBAAE,MAAM,GAAG,IAAI,EAAE;AAAA,2BACV,EAAE,aAAa,WAAW,EAAE,aAAa,YAAY;AAC5D,wBAAI,IAAI,EAAE,aAAa,UAAU;AACjC,yBAAK,EAAE,aAAa,YAAY,EAAE,GAAG,EAAE,OAAO,GAAG,EAAE,kBAAkB,GAAG,EAAE,MAAM,MAAM,GAAG,KAAK,EAAE,gBAAgB,UAAU,GAAG,IAAI,EAAE;AAAA,kBACrI,OAAO;AACL,sBAAE,aAAa,iBAAiB,KAAK,EAAE,MAAM;AAC7C,wBAAI,IAAI,OAAO,aAAa,GAAG,IAAI,SAAS,YAAY;AACxD,sBAAE,mBAAmB,CAAC,GAAG,EAAE,gBAAgB,GAAG,EAAE,SAAS,CAAC,GAAG,IAAI,EAAE,SAAS;AAAA,kBAC9E;AACA,yBAAO;AAAA,gBACT;AACA,kBAAE,UAAU;AAAA,cACd;AAAA;AAAA;AAAA,YAGF;AAAA;AAAA,cAEE,SAAS,GAAG;AACV,yBAAS,IAAI;AAAA,gBACb;AACA,kBAAE,YAAY;AAAA,kBACZ,IAAI,SAAS,GAAG,GAAG,GAAG;AACpB,wBAAI,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC;AAC7B,4BAAQ,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK;AAAA,sBAChC,IAAI;AAAA,sBACJ,KAAK;AAAA,oBACP,CAAC,GAAG;AAAA,kBACN;AAAA,kBACA,MAAM,SAAS,GAAG,GAAG,GAAG;AACtB,wBAAI,IAAI;AACR,6BAAS,IAAI;AACX,wBAAE,IAAI,GAAG,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS;AAAA,oBACnC;AACA,2BAAO,EAAE,IAAI,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC;AAAA,kBACjC;AAAA,kBACA,MAAM,SAAS,GAAG;AAChB,wBAAI,IAAI,CAAC,EAAE,MAAM,KAAK,WAAW,CAAC,GAAG,MAAM,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,IAAI,GAAG,IAAI,EAAE;AACpG,yBAAK,GAAG,IAAI,GAAG;AACb,wBAAE,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC;AAC3B,2BAAO;AAAA,kBACT;AAAA,kBACA,KAAK,SAAS,GAAG,GAAG;AAClB,wBAAI,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC;AAChD,wBAAI,KAAK;AACP,+BAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,GAAG;AACnC,0BAAE,CAAC,EAAE,OAAO,KAAK,EAAE,CAAC,EAAE,GAAG,MAAM,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AACnD,2BAAO,EAAE,SAAS,EAAE,CAAC,IAAI,IAAI,OAAO,EAAE,CAAC,GAAG;AAAA,kBAC5C;AAAA,gBACF,GAAG,EAAE,UAAU,GAAG,EAAE,QAAQ,cAAc;AAAA,cAC5C;AAAA;AAAA;AAAA,UAGJ,GAAG,IAAI,CAAC;AACR,mBAAS,EAAE,GAAG;AACZ,gBAAI,EAAE,CAAC;AACL,qBAAO,EAAE,CAAC,EAAE;AACd,gBAAI,IAAI,EAAE,CAAC,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAMb,SAAS,CAAC;AAAA;AAAA,YAEZ;AACA,mBAAO,EAAE,CAAC,EAAE,GAAG,EAAE,SAAS,CAAC,GAAG,EAAE;AAAA,UAClC;AACA,iBAAO,WAAW;AAChB,cAAE,IAAI,SAAS,GAAG;AAChB,kBAAI,IAAI,KAAK,EAAE;AAAA;AAAA,gBAEb,WAAW;AACT,yBAAO,EAAE;AAAA,gBACX;AAAA;AAAA;AAAA,gBAGA,WAAW;AACT,yBAAO;AAAA,gBACT;AAAA;AAEF,qBAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG;AAAA,YAC3B;AAAA,UACF,EAAE,GAAG,WAAW;AACd,cAAE,IAAI,SAAS,GAAG,GAAG;AACnB,uBAAS,KAAK;AACZ,kBAAE,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,KAAK,OAAO,eAAe,GAAG,GAAG,EAAE,YAAY,MAAI,KAAK,EAAE,CAAC,EAAE,CAAC;AAAA,YACxF;AAAA,UACF,EAAE,GAAG,WAAW;AACd,cAAE,IAAI,SAAS,GAAG,GAAG;AACnB,qBAAO,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAAA,YAClD;AAAA,UACF,EAAE,GAAG,EAAE,GAAG;AAAA,QACZ,EAAE,EAAE;AAAA;AAAA,IAER,CAAC;AAAA,EACH,EAAE,CAAC,IAAI,EAAE;AACX;AACA,IAAI,KAAK,GAAG;AACZ,IAAM,KAAqB,GAAG,EAAE;AAAhC,IAAmC,KAAK,SAAS,GAAG,GAAG;AACrD,MAAI,IAAI,KAAK,IAAI,GAAG;AACpB,SAAO,IAAI,MAAM;AACf,SAAK,IAAI,IAAI,IAAI,KAAK,KAAK,aAAa,CAAC,GAAG,IAAI,WAAW,MAAM;AAC/D,QAAE,GAAG,CAAC;AAAA,IACR,GAAG,CAAC,GAAG,IAAI,KAAK,IAAI;AAAA,EACtB;AACF;AAPA,IAOG,KAAK,gBAAE;AAAA,EACR,MAAM;AAAA,EACN,YAAY;AAAA,IACV,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM,CAAC,QAAQ,OAAO,QAAQ,QAAQ,SAAS,QAAQ;AAAA,MACvD,UAAU;AAAA,IACZ;AAAA,IACA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,UAAU;AAAA,MACR,MAAM,CAAC,SAAS,MAAM;AAAA,MACtB,SAAS;AAAA,IACX;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS,CAAC,MAAM,EAAE,eAAe;AAAA,IACnC;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,OAAO,CAAC,cAAc,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM9B,MAAM,GAAG,EAAE,MAAM,EAAE,GAAG;AACpB,UAAM,IAAI,IAAE,KAAE,GAAG,IAAI,IAAE,KAAE,GAAG,IAAI,IAAE,EAAE,QAAQ,GAAG,IAAI,IAAE,IAAI,GAAG,IAAI,IAAE,IAAI;AACtE,YAAE,eAAe,EAAE,WAAW,GAAG,QAAE,cAAc,EAAE,UAAU,GAAG,QAAE,YAAY,CAACA,OAAM;AACnF,QAAE,cAAcA,EAAC;AAAA,IACnB,CAAC;AACD,UAAM,IAAI,SAAE,MAAM,qBAAqB,EAAE,SAAS,EAAE,QAAQ,WAAW,GAAG,GAAG,IAAI,SAAE,MAAM;AACvF,UAAI,OAAO,EAAE,YAAY,aAAa,CAAC,EAAE;AACvC,eAAO,EAAE,UAAU,QAAQ,YAAY,WAAW,SAAS,KAAK,OAAO,QAAQ;AACjF,YAAMA,KAAI,EAAE;AACZ,aAAO;AAAA,QACL,UAAUA,GAAE,YAAY;AAAA,QACxB,YAAYA,GAAE,cAAc;AAAA,QAC5B,SAASA,GAAE,WAAW;AAAA,QACtB,OAAOA,GAAE,SAAS;AAAA,MACpB;AAAA,IACF,CAAC,GAAG,IAAI,SAAE,MAAM;AACd,UAAI,CAAC,EAAE,SAAS,OAAO,EAAE,SAAS;AAChC,eAAO,EAAE;AACX,UAAI;AACF,eAAO,KAAK,MAAM,EAAE,KAAK;AAAA,MAC3B,QAAQ;AACN,eAAO,EAAE;AAAA,MACX;AAAA,IACF,CAAC,GAAG,IAAI,MAAM;AACZ,QAAE;AAAA,IACJ,GAAG,IAAI,GAAG,MAAM;AACd,eAAG,MAAM;AACP,UAAE,UAAU,EAAE,MAAM,IAAI,gBAAgB,MAAM,EAAE,QAAQ,OAAK,EAAE,QAAQ;AAAA,MACzE,CAAC;AAAA,IACH,GAAG,GAAG,GAAG,IAAI,CAACA,OAAM;AAClB,QAAE,UAAU,EAAE,QAAQ,MAAI,WAAW,MAAM;AACzC,UAAE,QAAQ;AAAA,MACZ,GAAG,EAAE,MAAM,OAAO,GAAG,EAAE,UAAUA,EAAC;AAAA,IACpC,GAAG,IAAI,MAAM;AACX,QAAE,QAAQ,CAAC,EAAE;AAAA,IACf;AACA,WAAO,MAAE,MAAM,EAAE,OAAO,MAAM;AAC5B,QAAE;AAAA,IACJ,CAAC,GAAG,UAAE,MAAM;AACV,QAAE,SAAS,EAAE,UAAU,EAAE,GAAG,EAAE,MAAM,IAAI,iBAAiB,WAAW,GAAG,IAAE,IAAI,EAAE,YAAY,EAAE,SAAS,IAAI,GAAG,EAAE,OAAO;AAAA,QACpH,MAAM,MAAM,KAAK,UAAU,EAAE,OAAO,MAAM,CAAC;AAAA;AAAA,MAE7C,CAAC,EAAE,GAAG,WAAW,CAAC;AAAA,IACpB,CAAC,GAAG;AAAA,MACF,MAAM;AAAA,MACN,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,IAKpB;AAAA,EACF;AACF,CAAC;AAvHD,IAuHI,KAAK,CAAC,GAAG,MAAM;AACjB,QAAM,IAAI,EAAE,aAAa;AACzB,aAAW,CAAC,GAAG,CAAC,KAAK;AACnB,MAAE,CAAC,IAAI;AACT,SAAO;AACT;AACA,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC5B,QAAM,IAAI,iBAAG,UAAU;AACvB,SAAO,UAAE,GAAG,mBAAE,OAAO;AAAA,IACnB,OAAO,eAAE,EAAE,OAAO;AAAA,EACpB,GAAG;AAAA,IACD,EAAE,YAAY,UAAE,GAAG,mBAAE,OAAO;AAAA,MAC1B,KAAK;AAAA,MACL,OAAO,eAAE,cAAc,EAAE,SAAS,SAAS,OAAO,EAAE;AAAA,IACtD,GAAG;AAAA,MACD,gBAAE,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,OAAO,eAAE,CAAC,aAAa,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AAAA,MAC9C,GAAG;AAAA,QACD,WAAG,EAAE,QAAQ,QAAQ,EAAE,QAAQ,EAAE,OAAO,GAAG,MAAM;AAAA,UAC/C,gBAAG,gBAAE,EAAE,SAAS,EAAE,SAAS,aAAa,EAAE,SAAS,QAAQ,GAAG,CAAC;AAAA,QACjE,CAAC;AAAA,MACH,GAAG,CAAC;AAAA,IACN,GAAG,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,IACjB,gBAAE,OAAO;AAAA,MACP,OAAO,eAAE,CAAC,WAAW,EAAE,MAAM,EAAE,YAAY,OAAO,EAAE,MAAM,CAAC,CAAC;AAAA,IAC9D,GAAG;AAAA,MACD,YAAG,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,OAAO,EAAE;AAAA,QACT,MAAM,EAAE;AAAA,QACR,gBAAgB,EAAE;AAAA,MACpB,GAAG,MAAM,GAAG,CAAC,SAAS,QAAQ,cAAc,CAAC;AAAA,IAC/C,GAAG,CAAC;AAAA,IACJ,EAAE,kBAAkB,EAAE,SAAS,UAAE,GAAG,mBAAE,OAAO;AAAA,MAC3C,KAAK;AAAA,MACL,OAAO;AAAA,MACP,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,IAAI,MAAM,EAAE,oBAAoB,EAAE,iBAAiB,GAAG,CAAC;AAAA,IAClF,GAAG;AAAA,MACD,gBAAE,QAAQ;AAAA,QACR,OAAO,eAAE,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;AAAA,MAClD,GAAG,MAAM,CAAC;AAAA,IACZ,CAAC,KAAK,mBAAE,IAAI,IAAE;AAAA,EAChB,GAAG,CAAC;AACN;AACA,IAAM,IAAoB,GAAG,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;AAAjD,IAAoD,KAAK,CAAC,MAAM;AAC9D,IAAE,UAAU,EAAE,MAAM,CAAC;AACvB;AAFA,IAEG,KAAK;AAAA,EACN,SAAS;AACX;", "names": ["h"]}