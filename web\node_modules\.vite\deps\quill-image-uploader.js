import {
  require_quill
} from "./chunk-J3P3DZUB.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/.pnpm/quill-image-uploader@1.3.0_quill@1.3.7/node_modules/quill-image-uploader/src/blots/image.js
var import_quill = __toESM(require_quill());
var InlineBlot = import_quill.default.import("blots/block");
var LoadingImage = class extends InlineBlot {
  static create(src) {
    const node = super.create(src);
    if (src === true) return node;
    const image = document.createElement("img");
    image.setAttribute("src", src);
    node.appendChild(image);
    return node;
  }
  deleteAt(index, length) {
    super.deleteAt(index, length);
    this.cache = {};
  }
  static value(domNode) {
    const { src, custom } = domNode.dataset;
    return { src, custom };
  }
};
LoadingImage.blotName = "imageBlot";
LoadingImage.className = "image-uploading";
LoadingImage.tagName = "span";
import_quill.default.register({ "formats/imageBlot": LoadingImage });
var image_default = LoadingImage;

// node_modules/.pnpm/quill-image-uploader@1.3.0_quill@1.3.7/node_modules/quill-image-uploader/src/quill.imageUploader.js
var ImageUploader = class {
  constructor(quill, options) {
    this.quill = quill;
    this.options = options;
    this.range = null;
    this.placeholderDelta = null;
    if (typeof this.options.upload !== "function")
      console.warn(
        "[Missing config] upload function that returns a promise is required"
      );
    var toolbar = this.quill.getModule("toolbar");
    if (toolbar) {
      toolbar.addHandler("image", this.selectLocalImage.bind(this));
    }
    this.handleDrop = this.handleDrop.bind(this);
    this.handlePaste = this.handlePaste.bind(this);
    this.quill.root.addEventListener("drop", this.handleDrop, false);
    this.quill.root.addEventListener("paste", this.handlePaste, false);
  }
  selectLocalImage() {
    this.quill.focus();
    this.range = this.quill.getSelection();
    this.fileHolder = document.createElement("input");
    this.fileHolder.setAttribute("type", "file");
    this.fileHolder.setAttribute("accept", "image/*");
    this.fileHolder.setAttribute("style", "visibility:hidden");
    this.fileHolder.onchange = this.fileChanged.bind(this);
    document.body.appendChild(this.fileHolder);
    this.fileHolder.click();
    window.requestAnimationFrame(() => {
      document.body.removeChild(this.fileHolder);
    });
  }
  handleDrop(evt) {
    if (evt.dataTransfer && evt.dataTransfer.files && evt.dataTransfer.files.length) {
      evt.stopPropagation();
      evt.preventDefault();
      if (document.caretRangeFromPoint) {
        const selection = document.getSelection();
        const range = document.caretRangeFromPoint(evt.clientX, evt.clientY);
        if (selection && range) {
          selection.setBaseAndExtent(
            range.startContainer,
            range.startOffset,
            range.startContainer,
            range.startOffset
          );
        }
      } else {
        const selection = document.getSelection();
        const range = document.caretPositionFromPoint(evt.clientX, evt.clientY);
        if (selection && range) {
          selection.setBaseAndExtent(
            range.offsetNode,
            range.offset,
            range.offsetNode,
            range.offset
          );
        }
      }
      this.quill.focus();
      this.range = this.quill.getSelection();
      let file = evt.dataTransfer.files[0];
      setTimeout(() => {
        this.quill.focus();
        this.range = this.quill.getSelection();
        this.readAndUploadFile(file);
      }, 0);
    }
  }
  handlePaste(evt) {
    let clipboard = evt.clipboardData || window.clipboardData;
    if (clipboard && (clipboard.items || clipboard.files)) {
      let items = clipboard.items || clipboard.files;
      const IMAGE_MIME_REGEX = /^image\/(jpe?g|gif|png|svg|webp)$/i;
      for (let i = 0; i < items.length; i++) {
        if (IMAGE_MIME_REGEX.test(items[i].type)) {
          let file = items[i].getAsFile ? items[i].getAsFile() : items[i];
          if (file) {
            this.quill.focus();
            this.range = this.quill.getSelection();
            evt.preventDefault();
            setTimeout(() => {
              this.quill.focus();
              this.range = this.quill.getSelection();
              this.readAndUploadFile(file);
            }, 0);
          }
        }
      }
    }
  }
  readAndUploadFile(file) {
    let isUploadReject = false;
    const fileReader = new FileReader();
    fileReader.addEventListener(
      "load",
      () => {
        if (!isUploadReject) {
          let base64ImageSrc = fileReader.result;
          this.insertBase64Image(base64ImageSrc);
        }
      },
      false
    );
    if (file) {
      fileReader.readAsDataURL(file);
    }
    this.options.upload(file).then(
      (imageUrl) => {
        this.insertToEditor(imageUrl);
      },
      (error) => {
        isUploadReject = true;
        this.removeBase64Image();
        console.warn(error);
      }
    );
  }
  fileChanged() {
    const file = this.fileHolder.files[0];
    this.readAndUploadFile(file);
  }
  insertBase64Image(url) {
    const range = this.range;
    this.placeholderDelta = this.quill.insertEmbed(
      range.index,
      image_default.blotName,
      `${url}`,
      "user"
    );
  }
  insertToEditor(url) {
    const range = this.range;
    const lengthToDelete = this.calculatePlaceholderInsertLength();
    this.quill.deleteText(range.index, lengthToDelete, "user");
    this.quill.insertEmbed(range.index, "image", `${url}`, "user");
    range.index++;
    this.quill.setSelection(range, "user");
  }
  // The length of the insert delta from insertBase64Image can vary depending on what part of the line the insert occurs
  calculatePlaceholderInsertLength() {
    return this.placeholderDelta.ops.reduce((accumulator, deltaOperation) => {
      if (deltaOperation.hasOwnProperty("insert"))
        accumulator++;
      return accumulator;
    }, 0);
  }
  removeBase64Image() {
    const range = this.range;
    const lengthToDelete = this.calculatePlaceholderInsertLength();
    this.quill.deleteText(range.index, lengthToDelete, "user");
  }
};
window.ImageUploader = ImageUploader;
var quill_imageUploader_default = ImageUploader;
export {
  quill_imageUploader_default as default
};
//# sourceMappingURL=quill-image-uploader.js.map
