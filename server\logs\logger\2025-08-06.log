2025-08-06T10:05:15.795+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/cmd/cmd.go:57: starting all server
2025-08-06T10:05:15.797+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/websocket/init.go:29: start websocket..
2025-08-06T10:05:15.812+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/tcpclient/cron.go:37: CronClient start..
2025-08-06T10:05:15.946+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/tcpserver/server.go:36: TCPServer start..
2025-08-06T10:05:20.826+08:00 [DEBU] {d096e1ff440b59187c69f1320826c31c} E:/NaviHotgo/server/internal/logic/tcpserver/server_handle.go:130: onServerLogin succeed. appid:1002, group:cron, name:cron1
2025-08-06T10:05:20.826+08:00 [DEBU] {8c198400450b59187f69f132838e7cc4} E:/NaviHotgo/server/internal/logic/tcpclient/cron.go:96: CronClient login succeed.
2025-08-06T10:09:29.326+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/websocket/client_manager.go:299: websocket closeSignal quit..
2025-08-06T10:09:29.326+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/tcpserver/server.go:72: TCPServer stop..
2025-08-06T10:09:29.326+08:00 [DEBU] {d09b43dc7e0b59188c69f1327e3de6cd} E:/NaviHotgo/server/internal/logic/tcpclient/cron.go:101: CronClient closed.
2025-08-06T10:09:29.327+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/tcpclient/cron.go:90: CronClient stop..
2025-08-06T10:09:29.327+08:00 [DEBU] {00f44cdc7e0b59188d69f1320f43959d} E:/NaviHotgo/server/internal/logic/tcpclient/cron.go:101: CronClient closed.
2025-08-06T10:09:29.390+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/cmd/http.go:97: http successfully closed ..
2025-08-06T10:09:29.390+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/cmd/cmd.go:76: all service successfully closed ..
2025-08-06T10:12:12.112+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/cmd/cmd.go:57: starting all server
2025-08-06T10:12:12.115+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/websocket/init.go:29: start websocket..
2025-08-06T10:12:12.199+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/tcpclient/cron.go:37: CronClient start..
2025-08-06T10:12:12.255+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/tcpserver/server.go:36: TCPServer start..
2025-08-06T10:12:17.211+08:00 [DEBU] {341470f2a50b5918b53f1f108183bbad} E:/NaviHotgo/server/internal/logic/tcpserver/server_handle.go:130: onServerLogin succeed. appid:1002, group:cron, name:cron1
2025-08-06T10:12:17.211+08:00 [DEBU] {e4eb00f3a50b5918b83f1f10e1794b4d} E:/NaviHotgo/server/internal/logic/tcpclient/cron.go:96: CronClient login succeed.
2025-08-06T10:23:09.481+08:00 [ERRO] {709668d03d0c5918cf3f1f10a5e84e23} E:/NaviHotgo/server/internal/logic/middleware/response.go:104: exception:获取我的消息失败！: SELECT "id" FROM "hg_admin_notice" WHERE (("status"=1) AND ((`type` IN(1,2) OR (`type` = 3 and JSON_CONTAINS(`receiver`,'1'))))) AND "deleted_at" IS NULL: pq: 语法错误 在 "IN" 或附近的 
Stack:
1.  hotgo/internal/logic/middleware.parseResponse
    E:/NaviHotgo/server/internal/logic/middleware/response.go:104
2.  hotgo/internal/logic/middleware.responseJson
    E:/NaviHotgo/server/internal/logic/middleware/response.go:72
3.  hotgo/internal/logic/middleware.(*sMiddleware).ResponseHandler
    E:/NaviHotgo/server/internal/logic/middleware/response.go:49
4.  hotgo/internal/logic/middleware.(*sMiddleware).PreFilter
    E:/NaviHotgo/server/internal/logic/middleware/pre_filter.go:65
5.  hotgo/internal/logic/middleware.(*sMiddleware).DemoLimit
    E:/NaviHotgo/server/internal/logic/middleware/init.go:112
6.  hotgo/internal/logic/middleware.(*sMiddleware).Blacklist
    E:/NaviHotgo/server/internal/logic/middleware/limit_blacklist.go:20
7.  hotgo/internal/logic/middleware.(*sMiddleware).CORS
    E:/NaviHotgo/server/internal/logic/middleware/init.go:106
8.  hotgo/internal/logic/middleware.(*sMiddleware).Ctx
    E:/NaviHotgo/server/internal/logic/middleware/init.go:86

