import "./chunk-372EWZB6.js";
import {
  computed,
  createBaseVNode,
  createCommentVNode,
  createElementBlock,
  createTextVNode,
  createVNode,
  defineComponent,
  h,
  inject,
  nextTick,
  normalizeClass,
  onMounted,
  openBlock,
  provide,
  ref,
  renderSlot,
  resolveComponent,
  toDisplayString,
  watch
} from "./chunk-TNRWGK26.js";
import "./chunk-PLDDJCW6.js";

// node_modules/.pnpm/vue3-json-viewer@2.4.1_vue@3.4.38_typescript@5.5.4_/node_modules/vue3-json-viewer/dist/vue3-json-viewer.mjs
var me = /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+)\.)+([A-Za-z0-9-~\/])+$/;
var G = defineComponent({
  name: "<PERSON>sonString",
  props: {
    /** The string value to display. */
    jsonValue: {
      type: String,
      required: true
    }
  },
  /**
   * Setup function for the JsonString component.
   * @param props - The component's props.
   */
  setup(e) {
    const m = ref(true), s = ref(false), l = ref(null), p = ref(null);
    onMounted(() => {
      l.value && p.value && l.value.offsetHeight > p.value.offsetHeight && (s.value = true);
    });
    const u = () => {
      m.value = !m.value;
    };
    return () => {
      const n = e.jsonValue, t = me.test(n);
      let o;
      !m.value && s.value ? o = h("span", {
        class: { "jv-ellipsis": true },
        onClick: u
      }, "...") : t ? o = h("span", { class: { "jv-item": true, "jv-string": true }, ref: l }, [
        h("span", null, '"'),
        // Opening quote
        h("a", { href: n, target: "_blank", class: "jv-link" }, n),
        h("span", null, '"')
        // Closing quote
      ]) : o = h("span", {
        class: { "jv-item": true, "jv-string": true },
        ref: l
        // Assign ref for height calculation
      }, `"${n}"`);
      const r = [];
      return s.value && r.push(h("span", {
        class: {
          "jv-toggle": true,
          // CSS class for the toggle button
          open: m.value
          // Class to indicate open/closed state
        },
        onClick: u
      })), r.push(h("span", { class: { "jv-holder-node": true }, ref: p })), r.push(o), h("span", {}, r);
    };
  }
});
var he = {
  class: normalizeClass(["jv-item", "jv-undefined"])
};
var ge = defineComponent({
  __name: "json-undefined",
  props: {
    /**
     * The value to display. Expected to be `null` or `undefined`.
     * The `type` is set to `null` and `PropType` is used to allow `undefined` as well,
     * with a default of `undefined`.
     */
    jsonValue: {
      type: null,
      default: void 0
    }
  },
  setup(e) {
    const m = e, s = computed(() => m.jsonValue === null ? "null" : "undefined");
    return (l, p) => (openBlock(), createElementBlock("span", he, toDisplayString(s.value), 1));
  }
});
var be = defineComponent({
  __name: "json-number",
  props: {
    /** The number value to display. */
    jsonValue: {
      type: Number,
      required: true
    }
  },
  setup(e) {
    const m = e, s = computed(() => Number.isInteger(m.jsonValue));
    return (l, p) => (openBlock(), createElementBlock("span", {
      class: normalizeClass(["jv-item", "jv-number", s.value ? "jv-number-integer" : "jv-number-float"])
    }, toDisplayString(e.jsonValue.toString()), 3));
  }
});
var je = {
  class: normalizeClass(["jv-item", "jv-boolean"])
};
var xe = defineComponent({
  __name: "json-boolean",
  props: {
    /** The boolean value to display. */
    jsonValue: {
      type: Boolean,
      required: true
    }
  },
  setup(e) {
    return (m, s) => (openBlock(), createElementBlock("span", je, toDisplayString(e.jsonValue.toString()), 1));
  }
});
var _e = defineComponent({
  name: "JsonObject",
  props: {
    /** The object value to render. */
    jsonValue: {
      type: Object,
      required: true
    },
    /** The key name of this object if it's a property of another object. */
    keyName: {
      type: String,
      default: ""
    },
    /** Current nesting depth of this object. */
    depth: {
      type: Number,
      default: 0
    },
    /** Whether this object should be rendered in an expanded state. */
    expand: Boolean,
    /** Whether to sort the keys of this object alphabetically. */
    sort: Boolean,
    /** Whether preview mode is enabled. */
    previewMode: Boolean
  },
  emits: ["update:expand"],
  /**
   * Setup function for the JsonObject component.
   * @param props - The component's props.
   * @param context - The setup context, including `emit`.
   */
  setup(e, { emit: m }) {
    const s = ref({});
    let l = null;
    const p = (o) => {
      setTimeout(() => {
        s.value = o;
      }, 0);
    };
    watch(() => e.jsonValue, (o) => {
      p(o);
    }, { immediate: true, deep: true });
    const u = computed(() => {
      if (!e.sort)
        return s.value;
      const o = Object.keys(s.value).sort(), r = {};
      return o.forEach((v) => {
        r[v] = s.value[v];
      }), r;
    }), n = () => {
      if (l)
        try {
          l.dispatchEvent(new Event("resized"));
        } catch {
          const r = document.createEvent("Event");
          r.initEvent("resized", true, false), l.dispatchEvent(r);
        }
    }, t = () => {
      m("update:expand", !e.expand), n();
    };
    return () => {
      const o = [];
      if (o.push(h("span", { class: ["jv-item", "jv-object"] }, "{")), e.expand) {
        for (const r in u.value)
          if (u.value.hasOwnProperty(r)) {
            const v = u.value[r];
            o.push(h(K, {
              key: r,
              // style: { display: !props.expand ? 'none' : undefined }, // Redundant
              sort: e.sort,
              keyName: r,
              depth: e.depth + 1,
              value: v,
              previewMode: e.previewMode
            }));
          }
      }
      return !e.expand && Object.keys(s.value).length > 0 && o.push(h("span", {
        // style: { display: props.expand ? 'none' : undefined }, // Redundant
        class: "jv-ellipsis",
        onClick: t,
        title: `click to reveal object content (keys: ${Object.keys(u.value).join(", ")})`
      }, "...")), o.push(h("span", { class: ["jv-item", "jv-object"] }, "}")), h("span", { ref: (r) => {
        l = r;
      } }, o);
    };
  }
});
var Ee = defineComponent({
  name: "JsonArray",
  props: {
    /** The array value to render. */
    jsonValue: {
      type: Array,
      required: true
    },
    /** The key name of this array if it's a property of an object. */
    keyName: {
      type: String,
      default: ""
    },
    /** Current nesting depth of this array. */
    depth: {
      type: Number,
      default: 0
    },
    /** Whether to sort array items (Note: arrays are typically not sorted by key). */
    sort: Boolean,
    // This prop might be less relevant for arrays vs objects
    /** Whether this array should be rendered in an expanded state. */
    expand: Boolean,
    /** Whether preview mode is enabled (potentially showing a condensed view). */
    previewMode: Boolean
  },
  emits: ["update:expand"],
  /**
   * Setup function for the JsonArray component.
   * @param props - The component's props.
   * @param context - The setup context, including `emit`.
   */
  setup(e, { emit: m }) {
    const s = ref([]);
    let l = null;
    const p = (n, t = 0) => {
      t === 0 && (s.value = []), setTimeout(() => {
        n && n.length > t && (s.value.push(n[t]), p(n, t + 1));
      }, 0);
    };
    watch(() => e.jsonValue, (n) => {
      p(n);
    }, { immediate: true, deep: true });
    const u = () => {
      if (m("update:expand", !e.expand), l)
        try {
          l.dispatchEvent(new Event("resized"));
        } catch {
          const t = document.createEvent("Event");
          t.initEvent("resized", true, false), l.dispatchEvent(t);
        }
    };
    return () => {
      const n = [];
      return !e.previewMode && !e.keyName && n.push(h("span", {
        class: {
          "jv-toggle": true,
          open: !!e.expand
        },
        onClick: u
      })), n.push(h("span", {
        class: ["jv-item", "jv-array"]
      }, "[")), e.expand && s.value.forEach((t, o) => {
        n.push(h(K, {
          // Cast JsonBox to Component
          key: o,
          // style: { display: props.expand ? undefined : 'none' }, // This style is redundant if items are not rendered
          sort: e.sort,
          depth: e.depth + 1,
          value: t,
          previewMode: e.previewMode
        }));
      }), !e.expand && s.value.length > 0 && n.push(h("span", {
        class: "jv-ellipsis",
        onClick: u,
        title: `click to reveal ${s.value.length} hidden items`
      }, "...")), n.push(h("span", {
        class: ["jv-item", "jv-array"]
      }, "]")), h("span", { ref: (t) => {
        l = t;
      } }, n);
    };
  }
});
var we = ["title"];
var Te = defineComponent({
  __name: "json-function",
  props: {
    /** The Function object to represent. */
    jsonValue: {
      type: Function,
      required: true
    }
  },
  setup(e) {
    return (m, s) => (openBlock(), createElementBlock("span", {
      class: normalizeClass(["jv-item", "jv-function"]),
      title: e.jsonValue.toString()
    }, " <function> ", 8, we));
  }
});
var Se = {
  class: normalizeClass(["jv-item", "jv-string"])
};
var Ce = defineComponent({
  __name: "json-date",
  props: {
    /** The Date object to display. */
    jsonValue: {
      type: Date,
      required: true
    }
  },
  setup(e) {
    const m = e, s = inject("timeformat", (p) => p.toLocaleString()), l = computed(() => s(m.jsonValue));
    return (p, u) => (openBlock(), createElementBlock("span", Se, ' "' + toDisplayString(l.value) + '" ', 1));
  }
});
var ke = {
  class: normalizeClass(["jv-item", "jv-regexp"])
};
var Ae = defineComponent({
  __name: "json-regexp",
  props: {
    /** The RegExp object to display. */
    jsonValue: {
      type: RegExp,
      required: true
    }
  },
  setup(e) {
    return (m, s) => (openBlock(), createElementBlock("span", ke, toDisplayString(e.jsonValue.toString()), 1));
  }
});
var K = defineComponent({
  name: "JsonBox",
  props: {
    /** The JSON value to render. Can be any valid JSON type. */
    value: {
      type: [Object, Array, String, Number, Boolean, Function, Date],
      default: null
    },
    /** The key name for this value, if it's part of an object. */
    keyName: {
      type: String,
      default: ""
    },
    /** Whether to sort object keys alphabetically. Passed down from JsonViewer. */
    sort: Boolean,
    /** Current nesting depth of this component. */
    depth: {
      type: Number,
      default: 0
    },
    /** Whether preview mode is enabled. Passed down from JsonViewer. */
    previewMode: Boolean
  },
  /**
   * Setup function for the JsonBox component.
   * @param props - The component's props.
   */
  setup(e) {
    const m = inject("expandDepth", 1 / 0), s = inject("keyClick", () => {
    }), l = ref(true);
    let p = null;
    onMounted(() => {
      l.value = e.previewMode || e.depth < m;
    });
    const u = () => {
      if (l.value = !l.value, p)
        try {
          p.dispatchEvent(new Event("resized"));
        } catch {
          const t = document.createEvent("Event");
          t.initEvent("resized", true, false), p.dispatchEvent(t);
        }
    };
    return () => {
      const n = [];
      let t;
      e.value === null || e.value === void 0 ? t = ge : Array.isArray(e.value) ? t = Ee : e.value instanceof Date ? t = Ce : e.value instanceof RegExp ? t = Ae : typeof e.value == "object" ? t = _e : typeof e.value == "number" ? t = be : typeof e.value == "string" ? t = G : typeof e.value == "boolean" ? t = xe : typeof e.value == "function" ? t = Te : t = G;
      const o = e.value && (Array.isArray(e.value) || typeof e.value == "object" && !(e.value instanceof Date) && // Exclude Date
      !(e.value instanceof RegExp));
      return !e.previewMode && o && !(e.value instanceof RegExp) && n.push(
        h("span", {
          class: {
            "jv-toggle": true,
            open: !!l.value
            // Double negation to ensure boolean
          },
          onClick: u
        })
      ), e.keyName && n.push(
        h("span", {
          class: "jv-key",
          // 'jv-key' is a string, not an object
          onClick: () => {
            s && s(e.keyName);
          }
        }, `${e.keyName}:`)
        // Text content as children
      ), n.push(
        h(t, {
          class: "jv-push",
          // 'jv-push' is a string
          jsonValue: e.value,
          keyName: e.keyName,
          // Pass keyName for context if needed by child
          sort: e.sort,
          // Pass sort for objects/arrays
          depth: e.depth,
          // Pass current depth
          expand: l.value,
          // Pass current expand state
          previewMode: e.previewMode,
          // Listen for 'update:expand' events from child components (e.g., JsonArray, JsonObject)
          // This allows children to request a change in their own expansion state.
          "onUpdate:expand": (r) => {
            l.value = r;
          }
        })
      ), h(
        "div",
        {
          class: {
            "jv-node": true,
            "jv-key-node": !!e.keyName && !o,
            // Apply if keyName exists and not a complex type
            toggle: !e.previewMode && o
            // Apply if not preview and is complex for styling indent
          },
          ref: (r) => {
            p = r;
          }
        },
        n
      );
    };
  }
});
function Ne(e) {
  return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
var L = { exports: {} };
var Oe = L.exports;
var X;
function Ve() {
  return X || (X = 1, function(e, m) {
    (function(l, p) {
      e.exports = p();
    })(Oe, function() {
      return (
        /******/
        function() {
          var s = {
            /***/
            686: (
              /***/
              function(u, n, t) {
                t.d(n, {
                  default: function() {
                    return (
                      /* binding */
                      le
                    );
                  }
                });
                var o = t(279), r = t.n(o), v = t(370), _ = t.n(v), x = t(817), E = t.n(x);
                function g(d) {
                  try {
                    return document.execCommand(d);
                  } catch {
                    return false;
                  }
                }
                var h2 = function(i) {
                  var a = E()(i);
                  return g("cut"), a;
                }, b = h2;
                function k(d) {
                  var i = document.documentElement.getAttribute("dir") === "rtl", a = document.createElement("textarea");
                  a.style.fontSize = "12pt", a.style.border = "0", a.style.padding = "0", a.style.margin = "0", a.style.position = "absolute", a.style[i ? "right" : "left"] = "-9999px";
                  var c = window.pageYOffset || document.documentElement.scrollTop;
                  return a.style.top = "".concat(c, "px"), a.setAttribute("readonly", ""), a.value = d, a;
                }
                var U = function(i, a) {
                  var c = k(i);
                  a.container.appendChild(c);
                  var f = E()(c);
                  return g("copy"), c.remove(), f;
                }, W = function(i) {
                  var a = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {
                    container: document.body
                  }, c = "";
                  return typeof i == "string" ? c = U(i, a) : i instanceof HTMLInputElement && !["text", "search", "url", "tel", "password"].includes(i == null ? void 0 : i.type) ? c = U(i.value, a) : (c = E()(i), g("copy")), c;
                }, D = W;
                function $(d) {
                  "@babel/helpers - typeof";
                  return typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? $ = function(a) {
                    return typeof a;
                  } : $ = function(a) {
                    return a && typeof Symbol == "function" && a.constructor === Symbol && a !== Symbol.prototype ? "symbol" : typeof a;
                  }, $(d);
                }
                var ee = function() {
                  var i = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, a = i.action, c = a === void 0 ? "copy" : a, f = i.container, y = i.target, T = i.text;
                  if (c !== "copy" && c !== "cut")
                    throw new Error('Invalid "action" value, use either "copy" or "cut"');
                  if (y !== void 0)
                    if (y && $(y) === "object" && y.nodeType === 1) {
                      if (c === "copy" && y.hasAttribute("disabled"))
                        throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');
                      if (c === "cut" && (y.hasAttribute("readonly") || y.hasAttribute("disabled")))
                        throw new Error(`Invalid "target" attribute. You can't cut text from elements with "readonly" or "disabled" attributes`);
                    } else
                      throw new Error('Invalid "target" value, use a valid Element');
                  if (T)
                    return D(T, {
                      container: f
                    });
                  if (y)
                    return c === "cut" ? b(y) : D(y, {
                      container: f
                    });
                }, te = ee;
                function M(d) {
                  "@babel/helpers - typeof";
                  return typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? M = function(a) {
                    return typeof a;
                  } : M = function(a) {
                    return a && typeof Symbol == "function" && a.constructor === Symbol && a !== Symbol.prototype ? "symbol" : typeof a;
                  }, M(d);
                }
                function ne(d, i) {
                  if (!(d instanceof i))
                    throw new TypeError("Cannot call a class as a function");
                }
                function Y(d, i) {
                  for (var a = 0; a < i.length; a++) {
                    var c = i[a];
                    c.enumerable = c.enumerable || false, c.configurable = true, "value" in c && (c.writable = true), Object.defineProperty(d, c.key, c);
                  }
                }
                function oe(d, i, a) {
                  return i && Y(d.prototype, i), a && Y(d, a), d;
                }
                function re(d, i) {
                  if (typeof i != "function" && i !== null)
                    throw new TypeError("Super expression must either be null or a function");
                  d.prototype = Object.create(i && i.prototype, { constructor: { value: d, writable: true, configurable: true } }), i && P(d, i);
                }
                function P(d, i) {
                  return P = Object.setPrototypeOf || function(c, f) {
                    return c.__proto__ = f, c;
                  }, P(d, i);
                }
                function ae(d) {
                  var i = se();
                  return function() {
                    var c = R(d), f;
                    if (i) {
                      var y = R(this).constructor;
                      f = Reflect.construct(c, arguments, y);
                    } else
                      f = c.apply(this, arguments);
                    return ie(this, f);
                  };
                }
                function ie(d, i) {
                  return i && (M(i) === "object" || typeof i == "function") ? i : ue(d);
                }
                function ue(d) {
                  if (d === void 0)
                    throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
                  return d;
                }
                function se() {
                  if (typeof Reflect > "u" || !Reflect.construct || Reflect.construct.sham) return false;
                  if (typeof Proxy == "function") return true;
                  try {
                    return Date.prototype.toString.call(Reflect.construct(Date, [], function() {
                    })), true;
                  } catch {
                    return false;
                  }
                }
                function R(d) {
                  return R = Object.setPrototypeOf ? Object.getPrototypeOf : function(a) {
                    return a.__proto__ || Object.getPrototypeOf(a);
                  }, R(d);
                }
                function z(d, i) {
                  var a = "data-clipboard-".concat(d);
                  if (i.hasAttribute(a))
                    return i.getAttribute(a);
                }
                var ce = function(d) {
                  re(a, d);
                  var i = ae(a);
                  function a(c, f) {
                    var y;
                    return ne(this, a), y = i.call(this), y.resolveOptions(f), y.listenClick(c), y;
                  }
                  return oe(a, [{
                    key: "resolveOptions",
                    value: function() {
                      var f = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
                      this.action = typeof f.action == "function" ? f.action : this.defaultAction, this.target = typeof f.target == "function" ? f.target : this.defaultTarget, this.text = typeof f.text == "function" ? f.text : this.defaultText, this.container = M(f.container) === "object" ? f.container : document.body;
                    }
                    /**
                     * Adds a click event listener to the passed trigger.
                     * @param {String|HTMLElement|HTMLCollection|NodeList} trigger
                     */
                  }, {
                    key: "listenClick",
                    value: function(f) {
                      var y = this;
                      this.listener = _()(f, "click", function(T) {
                        return y.onClick(T);
                      });
                    }
                    /**
                     * Defines a new `ClipboardAction` on each click event.
                     * @param {Event} e
                     */
                  }, {
                    key: "onClick",
                    value: function(f) {
                      var y = f.delegateTarget || f.currentTarget, T = this.action(y) || "copy", B = te({
                        action: T,
                        container: this.container,
                        target: this.target(y),
                        text: this.text(y)
                      });
                      this.emit(B ? "success" : "error", {
                        action: T,
                        text: B,
                        trigger: y,
                        clearSelection: function() {
                          y && y.focus(), window.getSelection().removeAllRanges();
                        }
                      });
                    }
                    /**
                     * Default `action` lookup function.
                     * @param {Element} trigger
                     */
                  }, {
                    key: "defaultAction",
                    value: function(f) {
                      return z("action", f);
                    }
                    /**
                     * Default `target` lookup function.
                     * @param {Element} trigger
                     */
                  }, {
                    key: "defaultTarget",
                    value: function(f) {
                      var y = z("target", f);
                      if (y)
                        return document.querySelector(y);
                    }
                    /**
                     * Allow fire programmatically a copy action
                     * @param {String|HTMLElement} target
                     * @param {Object} options
                     * @returns Text copied.
                     */
                  }, {
                    key: "defaultText",
                    /**
                     * Default `text` lookup function.
                     * @param {Element} trigger
                     */
                    value: function(f) {
                      return z("text", f);
                    }
                    /**
                     * Destroy lifecycle.
                     */
                  }, {
                    key: "destroy",
                    value: function() {
                      this.listener.destroy();
                    }
                  }], [{
                    key: "copy",
                    value: function(f) {
                      var y = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {
                        container: document.body
                      };
                      return D(f, y);
                    }
                    /**
                     * Allow fire programmatically a cut action
                     * @param {String|HTMLElement} target
                     * @returns Text cutted.
                     */
                  }, {
                    key: "cut",
                    value: function(f) {
                      return b(f);
                    }
                    /**
                     * Returns the support of the given action, or all actions if no action is
                     * given.
                     * @param {String} [action]
                     */
                  }, {
                    key: "isSupported",
                    value: function() {
                      var f = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : ["copy", "cut"], y = typeof f == "string" ? [f] : f, T = !!document.queryCommandSupported;
                      return y.forEach(function(B) {
                        T = T && !!document.queryCommandSupported(B);
                      }), T;
                    }
                  }]), a;
                }(r()), le = ce;
              }
            ),
            /***/
            828: (
              /***/
              function(u) {
                var n = 9;
                if (typeof Element < "u" && !Element.prototype.matches) {
                  var t = Element.prototype;
                  t.matches = t.matchesSelector || t.mozMatchesSelector || t.msMatchesSelector || t.oMatchesSelector || t.webkitMatchesSelector;
                }
                function o(r, v) {
                  for (; r && r.nodeType !== n; ) {
                    if (typeof r.matches == "function" && r.matches(v))
                      return r;
                    r = r.parentNode;
                  }
                }
                u.exports = o;
              }
            ),
            /***/
            438: (
              /***/
              function(u, n, t) {
                var o = t(828);
                function r(x, E, g, h2, b) {
                  var k = _.apply(this, arguments);
                  return x.addEventListener(g, k, b), {
                    destroy: function() {
                      x.removeEventListener(g, k, b);
                    }
                  };
                }
                function v(x, E, g, h2, b) {
                  return typeof x.addEventListener == "function" ? r.apply(null, arguments) : typeof g == "function" ? r.bind(null, document).apply(null, arguments) : (typeof x == "string" && (x = document.querySelectorAll(x)), Array.prototype.map.call(x, function(k) {
                    return r(k, E, g, h2, b);
                  }));
                }
                function _(x, E, g, h2) {
                  return function(b) {
                    b.delegateTarget = o(b.target, E), b.delegateTarget && h2.call(x, b);
                  };
                }
                u.exports = v;
              }
            ),
            /***/
            879: (
              /***/
              function(u, n) {
                n.node = function(t) {
                  return t !== void 0 && t instanceof HTMLElement && t.nodeType === 1;
                }, n.nodeList = function(t) {
                  var o = Object.prototype.toString.call(t);
                  return t !== void 0 && (o === "[object NodeList]" || o === "[object HTMLCollection]") && "length" in t && (t.length === 0 || n.node(t[0]));
                }, n.string = function(t) {
                  return typeof t == "string" || t instanceof String;
                }, n.fn = function(t) {
                  var o = Object.prototype.toString.call(t);
                  return o === "[object Function]";
                };
              }
            ),
            /***/
            370: (
              /***/
              function(u, n, t) {
                var o = t(879), r = t(438);
                function v(g, h2, b) {
                  if (!g && !h2 && !b)
                    throw new Error("Missing required arguments");
                  if (!o.string(h2))
                    throw new TypeError("Second argument must be a String");
                  if (!o.fn(b))
                    throw new TypeError("Third argument must be a Function");
                  if (o.node(g))
                    return _(g, h2, b);
                  if (o.nodeList(g))
                    return x(g, h2, b);
                  if (o.string(g))
                    return E(g, h2, b);
                  throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList");
                }
                function _(g, h2, b) {
                  return g.addEventListener(h2, b), {
                    destroy: function() {
                      g.removeEventListener(h2, b);
                    }
                  };
                }
                function x(g, h2, b) {
                  return Array.prototype.forEach.call(g, function(k) {
                    k.addEventListener(h2, b);
                  }), {
                    destroy: function() {
                      Array.prototype.forEach.call(g, function(k) {
                        k.removeEventListener(h2, b);
                      });
                    }
                  };
                }
                function E(g, h2, b) {
                  return r(document.body, g, h2, b);
                }
                u.exports = v;
              }
            ),
            /***/
            817: (
              /***/
              function(u) {
                function n(t) {
                  var o;
                  if (t.nodeName === "SELECT")
                    t.focus(), o = t.value;
                  else if (t.nodeName === "INPUT" || t.nodeName === "TEXTAREA") {
                    var r = t.hasAttribute("readonly");
                    r || t.setAttribute("readonly", ""), t.select(), t.setSelectionRange(0, t.value.length), r || t.removeAttribute("readonly"), o = t.value;
                  } else {
                    t.hasAttribute("contenteditable") && t.focus();
                    var v = window.getSelection(), _ = document.createRange();
                    _.selectNodeContents(t), v.removeAllRanges(), v.addRange(_), o = v.toString();
                  }
                  return o;
                }
                u.exports = n;
              }
            ),
            /***/
            279: (
              /***/
              function(u) {
                function n() {
                }
                n.prototype = {
                  on: function(t, o, r) {
                    var v = this.e || (this.e = {});
                    return (v[t] || (v[t] = [])).push({
                      fn: o,
                      ctx: r
                    }), this;
                  },
                  once: function(t, o, r) {
                    var v = this;
                    function _() {
                      v.off(t, _), o.apply(r, arguments);
                    }
                    return _._ = o, this.on(t, _, r);
                  },
                  emit: function(t) {
                    var o = [].slice.call(arguments, 1), r = ((this.e || (this.e = {}))[t] || []).slice(), v = 0, _ = r.length;
                    for (v; v < _; v++)
                      r[v].fn.apply(r[v].ctx, o);
                    return this;
                  },
                  off: function(t, o) {
                    var r = this.e || (this.e = {}), v = r[t], _ = [];
                    if (v && o)
                      for (var x = 0, E = v.length; x < E; x++)
                        v[x].fn !== o && v[x].fn._ !== o && _.push(v[x]);
                    return _.length ? r[t] = _ : delete r[t], this;
                  }
                }, u.exports = n, u.exports.TinyEmitter = n;
              }
            )
            /******/
          }, l = {};
          function p(u) {
            if (l[u])
              return l[u].exports;
            var n = l[u] = {
              /******/
              // no module.id needed
              /******/
              // no module.loaded needed
              /******/
              exports: {}
              /******/
            };
            return s[u](n, n.exports, p), n.exports;
          }
          return function() {
            p.n = function(u) {
              var n = u && u.__esModule ? (
                /******/
                function() {
                  return u.default;
                }
              ) : (
                /******/
                function() {
                  return u;
                }
              );
              return p.d(n, { a: n }), n;
            };
          }(), function() {
            p.d = function(u, n) {
              for (var t in n)
                p.o(n, t) && !p.o(u, t) && Object.defineProperty(u, t, { enumerable: true, get: n[t] });
            };
          }(), function() {
            p.o = function(u, n) {
              return Object.prototype.hasOwnProperty.call(u, n);
            };
          }(), p(686);
        }().default
      );
    });
  }(L)), L.exports;
}
var Me = Ve();
var $e = Ne(Me);
var Re = function(e, m) {
  let s = Date.now(), l;
  return (...p) => {
    Date.now() - s < m && l && clearTimeout(l), l = setTimeout(() => {
      e(...p);
    }, m), s = Date.now();
  };
};
var Be = defineComponent({
  name: "JsonViewer",
  components: {
    JsonBox: K
  },
  props: {
    value: {
      type: [Object, Array, String, Number, Boolean, Function],
      required: true
    },
    expanded: {
      type: Boolean,
      default: false
    },
    expandDepth: {
      type: Number,
      default: 1
    },
    copyable: {
      type: [Boolean, Object],
      default: false
    },
    sort: {
      type: Boolean,
      default: false
    },
    boxed: {
      type: Boolean,
      default: false
    },
    theme: {
      type: String,
      default: "light"
    },
    timeformat: {
      type: Function,
      default: (e) => e.toLocaleString()
    },
    previewMode: {
      type: Boolean,
      default: false
    },
    parse: {
      type: Boolean,
      default: false
    }
  },
  emits: ["onKeyClick", "copied"],
  /**
   * Setup function for the JsonViewer component.
   * @param props - The component's props.
   * @param context - The setup context, including `emit`.
   */
  setup(e, { emit: m }) {
    const s = ref(false), l = ref(false), p = ref(e.expanded), u = ref(null), n = ref(null);
    provide("expandDepth", e.expandDepth), provide("timeformat", e.timeformat), provide("keyClick", (h2) => {
      m("onKeyClick", h2);
    });
    const t = computed(() => "jv-container jv-" + e.theme + (e.boxed ? " boxed" : "")), o = computed(() => {
      if (typeof e.copyable == "boolean" && !e.copyable)
        return { copyText: "copy", copiedText: "copied!", timeout: 2e3, align: "right" };
      const h2 = e.copyable;
      return {
        copyText: h2.copyText || "copy",
        copiedText: h2.copiedText || "copied!",
        timeout: h2.timeout || 2e3,
        align: h2.align || "right"
      };
    }), r = computed(() => {
      if (!e.parse || typeof e.value != "string")
        return e.value;
      try {
        return JSON.parse(e.value);
      } catch {
        return e.value;
      }
    }), v = () => {
      x();
    }, x = Re(() => {
      nextTick(() => {
        n.value && (n.value.$el.clientHeight >= 250 ? l.value = true : l.value = false);
      });
    }, 200), E = (h2) => {
      s.value || (s.value = true, setTimeout(() => {
        s.value = false;
      }, o.value.timeout), m("copied", h2));
    }, g = () => {
      p.value = !p.value;
    };
    return watch(() => e.value, () => {
      v();
    }), onMounted(() => {
      e.boxed && n.value && (v(), n.value.$el.addEventListener("resized", v, true)), e.copyable && u.value && new $e(u.value, {
        text: () => JSON.stringify(r.value, null, 2)
        // Use parseValue for copying
      }).on("success", E);
    }), {
      clip: u,
      jsonBox: n,
      copied: s,
      expandableCode: l,
      expandCode: p,
      jvClass: t,
      copyText: o,
      parseValue: r,
      toggleExpandCode: g
      // Methods are already bound or don't need explicit exposure if not used in template
      // onResized, // only called internally
      // debResized, // only called internally
      // onCopied, // only called internally
    };
  }
});
var Le = (e, m) => {
  const s = e.__vccOpts || e;
  for (const [l, p] of m)
    s[l] = p;
  return s;
};
function De(e, m, s, l, p, u) {
  const n = resolveComponent("json-box");
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(e.jvClass)
  }, [
    e.copyable ? (openBlock(), createElementBlock("div", {
      key: 0,
      class: normalizeClass(`jv-tooltip ${e.copyText.align || "right"}`)
    }, [
      createBaseVNode("span", {
        ref: "clip",
        class: normalizeClass(["jv-button", { copied: e.copied }])
      }, [
        renderSlot(e.$slots, "copy", { copied: e.copied }, () => [
          createTextVNode(toDisplayString(e.copied ? e.copyText.copiedText : e.copyText.copyText), 1)
        ])
      ], 2)
    ], 2)) : createCommentVNode("", true),
    createBaseVNode("div", {
      class: normalizeClass(["jv-code", { open: e.expandCode, boxed: e.boxed }])
    }, [
      createVNode(n, {
        ref: "jsonBox",
        value: e.parseValue,
        sort: e.sort,
        "preview-mode": e.previewMode
      }, null, 8, ["value", "sort", "preview-mode"])
    ], 2),
    e.expandableCode && e.boxed ? (openBlock(), createElementBlock("div", {
      key: 1,
      class: "jv-more",
      onClick: m[0] || (m[0] = (...t) => e.toggleExpandCode && e.toggleExpandCode(...t))
    }, [
      createBaseVNode("span", {
        class: normalizeClass(["jv-toggle", { open: !!e.expandCode }])
      }, null, 2)
    ])) : createCommentVNode("", true)
  ], 2);
}
var Q = Le(Be, [["render", De]]);
var Pe = (e) => {
  e.component(Q.name, Q);
};
var qe = {
  install: Pe
};
export {
  Q as JsonViewer,
  qe as default
};
/*! Bundled license information:

vue3-json-viewer/dist/vue3-json-viewer.mjs:
  (*!
   * clipboard.js v2.0.11
   * https://clipboardjs.com/
   *
   * Licensed MIT © Zeno Rocha
   *)
*/
//# sourceMappingURL=vue3-json-viewer.js.map
