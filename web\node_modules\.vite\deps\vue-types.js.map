{"version": 3, "sources": ["../../.pnpm/is-plain-object@5.0.0/node_modules/is-plain-object/dist/is-plain-object.mjs", "../../.pnpm/vue-types@5.1.3_vue@3.4.38_typescript@5.5.4_/node_modules/vue-types/src/config.ts", "../../.pnpm/vue-types@5.1.3_vue@3.4.38_typescript@5.5.4_/node_modules/vue-types/src/utils.ts", "../../.pnpm/vue-types@5.1.3_vue@3.4.38_typescript@5.5.4_/node_modules/vue-types/src/validators/native.ts", "../../.pnpm/vue-types@5.1.3_vue@3.4.38_typescript@5.5.4_/node_modules/vue-types/src/validators/custom.ts", "../../.pnpm/vue-types@5.1.3_vue@3.4.38_typescript@5.5.4_/node_modules/vue-types/src/validators/oneof.ts", "../../.pnpm/vue-types@5.1.3_vue@3.4.38_typescript@5.5.4_/node_modules/vue-types/src/validators/oneoftype.ts", "../../.pnpm/vue-types@5.1.3_vue@3.4.38_typescript@5.5.4_/node_modules/vue-types/src/validators/arrayof.ts", "../../.pnpm/vue-types@5.1.3_vue@3.4.38_typescript@5.5.4_/node_modules/vue-types/src/validators/instanceof.ts", "../../.pnpm/vue-types@5.1.3_vue@3.4.38_typescript@5.5.4_/node_modules/vue-types/src/validators/objectof.ts", "../../.pnpm/vue-types@5.1.3_vue@3.4.38_typescript@5.5.4_/node_modules/vue-types/src/validators/shape.ts", "../../.pnpm/vue-types@5.1.3_vue@3.4.38_typescript@5.5.4_/node_modules/vue-types/src/index.ts", "../../.pnpm/vue-types@5.1.3_vue@3.4.38_typescript@5.5.4_/node_modules/vue-types/src/sensibles.ts"], "sourcesContent": ["/*!\n * is-plain-object <https://github.com/jonschlinkert/is-plain-object>\n *\n * Copyright (c) 2014-2017, <PERSON>.\n * Released under the MIT License.\n */\n\nfunction isObject(o) {\n  return Object.prototype.toString.call(o) === '[object Object]';\n}\n\nfunction isPlainObject(o) {\n  var ctor,prot;\n\n  if (isObject(o) === false) return false;\n\n  // If has modified constructor\n  ctor = o.constructor;\n  if (ctor === undefined) return true;\n\n  // If has modified prototype\n  prot = ctor.prototype;\n  if (isObject(prot) === false) return false;\n\n  // If constructor does not have an Object-specific method\n  if (prot.hasOwnProperty('isPrototypeOf') === false) {\n    return false;\n  }\n\n  // Most likely a plain Object\n  return true;\n}\n\nexport { isPlainObject };\n", "import { VueTypesConfig } from './types'\n\nexport const config: VueTypesConfig = {\n  silent: false,\n  logLevel: 'warn',\n}\n", "import { isPlainObject as _isPlainObject } from 'is-plain-object'\nimport { config } from './config'\nimport {\n  VueTypeDef,\n  VueTypeValidableDef,\n  VueProp,\n  InferType,\n  PropOptions,\n  VueTypesConfig,\n} from './types'\n\nconst ObjProto = Object.prototype\nconst toString = ObjProto.toString\nexport const hasOwn = ObjProto.hasOwnProperty\n\nconst FN_MATCH_REGEXP = /^\\s*function (\\w+)/\n\n// https://github.com/vuejs/vue/blob/dev/src/core/util/props.js#L177\nexport function getType(\n  fn: VueProp<any> | (() => any) | (new (...args: any[]) => any),\n): string {\n  const type = (fn as VueProp<any>)?.type ?? fn\n  if (type) {\n    const match = type.toString().match(FN_MATCH_REGEXP)\n    return match ? match[1] : ''\n  }\n  return ''\n}\n\nexport function getNativeType(value: any): string {\n  if (value === null || value === undefined) return ''\n  const match = value.constructor.toString().match(FN_MATCH_REGEXP)\n  return match ? match[1].replace(/^Async/, '') : ''\n}\n\ntype PlainObject = Record<string, any>\nexport const isPlainObject = _isPlainObject as (obj: any) => obj is PlainObject\n\n/**\n * No-op function\n */\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nexport function noop() {}\n\n/**\n * A function that returns its first argument\n *\n * @param arg - Any argument\n */\nexport const identity = (arg: any) => arg\n\nlet warn: (msg: string, level?: VueTypesConfig['logLevel']) => void = noop\n\nif (process.env.NODE_ENV !== 'production') {\n  const hasConsole = typeof console !== 'undefined'\n  warn = hasConsole\n    ? function warn(msg: string, level = config.logLevel) {\n        if (config.silent === false) {\n          console[level](`[VueTypes warn]: ${msg}`)\n        }\n      }\n    : noop\n}\n\nexport { warn }\n\n/**\n * Checks for a own property in an object\n *\n * @param {object} obj - Object\n * @param {string} prop - Property to check\n */\nexport const has = <T, U extends keyof T>(obj: T, prop: U) =>\n  hasOwn.call(obj, prop)\n\n/**\n * Determines whether the passed value is an integer. Uses `Number.isInteger` if available\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isInteger\n * @param {*} value - The value to be tested for being an integer.\n * @returns {boolean}\n */\nexport const isInteger =\n  Number.isInteger ||\n  function isInteger(value: unknown): value is number {\n    return (\n      typeof value === 'number' &&\n      isFinite(value) &&\n      Math.floor(value) === value\n    )\n  }\n\n/**\n * Determines whether the passed value is an Array.\n *\n * @param {*} value - The value to be tested for being an array.\n * @returns {boolean}\n */\nexport const isArray =\n  Array.isArray ||\n  function isArray(value): value is any[] {\n    return toString.call(value) === '[object Array]'\n  }\n\n/**\n * Checks if a value is a function\n *\n * @param {any} value - Value to check\n * @returns {boolean}\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport const isFunction = <T extends Function>(value: unknown): value is T =>\n  toString.call(value) === '[object Function]'\n\n/**\n * Checks if the passed-in value is a VueTypes type\n * @param value - The value to check\n * @param name - Optional validator name\n */\nexport const isVueTypeDef = <T>(\n  value: any,\n  name?: string,\n): value is VueTypeDef<T> | VueTypeValidableDef<T> =>\n  isPlainObject(value) &&\n  has(value, '_vueTypes_name') &&\n  (!name || value._vueTypes_name === name)\n\n/**\n * Checks if the passed-in value is a Vue prop definition object or a VueTypes type\n * @param value - The value to check\n */\nexport const isComplexType = <T>(value: any): value is VueProp<T> =>\n  isPlainObject(value) &&\n  (has(value, 'type') ||\n    ['_vueTypes_name', 'validator', 'default', 'required'].some((k) =>\n      has(value, k),\n    ))\n\nexport interface WrappedFn {\n  (...args: any[]): any\n  __original: (...args: any[]) => any\n}\n\n/**\n * Binds a function to a context and saves a reference to the original.\n *\n * @param fn - Target function\n * @param ctx - New function context\n */\nexport function bindTo(fn: (...args: any[]) => any, ctx: any): WrappedFn {\n  return Object.defineProperty(fn.bind(ctx) as WrappedFn, '__original', {\n    value: fn,\n  })\n}\n\n/**\n * Returns the original function bounded with `bindTo`. If the passed-in function\n * has not be bound, the function itself will be returned instead.\n *\n * @param fn - Function to unwrap\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function unwrap<T extends WrappedFn | Function>(fn: T) {\n  return (fn as WrappedFn).__original ?? fn\n}\n\n/**\n * Validates a given value against a prop type object.\n *\n * If `silent` is `false` (default) will return a boolean. If it is set to `true`\n * it will return `true` on success or a string error message on failure\n *\n * @param {Object|*} type - Type to use for validation. Either a type object or a constructor\n * @param {*} value - Value to check\n * @param {boolean} silent - Silence warnings\n */\nexport function validateType<T, U>(\n  type: T,\n  value: U,\n  silent = false,\n): string | boolean {\n  let typeToCheck: Record<string, any>\n  let valid = true\n  let expectedType = ''\n  if (!isPlainObject(type)) {\n    typeToCheck = { type }\n  } else {\n    typeToCheck = type\n  }\n  const namePrefix = isVueTypeDef(typeToCheck)\n    ? typeToCheck._vueTypes_name + ' - '\n    : ''\n\n  if (isComplexType(typeToCheck) && typeToCheck.type !== null) {\n    if (typeToCheck.type === undefined || typeToCheck.type === true) {\n      return valid\n    }\n    if (!typeToCheck.required && value == null) {\n      return valid\n    }\n    if (isArray(typeToCheck.type)) {\n      valid = typeToCheck.type.some(\n        (type: any) => validateType(type, value, true) === true,\n      )\n      expectedType = typeToCheck.type\n        .map((type: any) => getType(type))\n        .join(' or ')\n    } else {\n      expectedType = getType(typeToCheck)\n\n      if (expectedType === 'Array') {\n        valid = isArray(value)\n      } else if (expectedType === 'Object') {\n        valid = isPlainObject(value)\n      } else if (\n        expectedType === 'String' ||\n        expectedType === 'Number' ||\n        expectedType === 'Boolean' ||\n        expectedType === 'Function'\n      ) {\n        valid = getNativeType(value) === expectedType\n      } else {\n        valid = value instanceof typeToCheck.type\n      }\n    }\n  }\n\n  if (!valid) {\n    const msg = `${namePrefix}value \"${value}\" should be of type \"${expectedType}\"`\n    if (silent === false) {\n      warn(msg)\n      return false\n    }\n    return msg\n  }\n\n  if (has(typeToCheck, 'validator') && isFunction(typeToCheck.validator)) {\n    const oldWarn = warn\n    const warnLog: string[] = []\n    warn = (msg) => {\n      warnLog.push(msg)\n    }\n\n    valid = typeToCheck.validator(value)\n    warn = oldWarn\n\n    if (!valid) {\n      const msg = (warnLog.length > 1 ? '* ' : '') + warnLog.join('\\n* ')\n      warnLog.length = 0\n      if (silent === false) {\n        warn(msg)\n        return valid\n      }\n      return msg\n    }\n  }\n  return valid\n}\n\n/**\n * Adds `isRequired` and `def` modifiers to an object\n *\n * @param {string} name - Type internal name\n * @param {object} obj - Object to enhance\n */\nexport function toType<T = any>(name: string, obj: PropOptions<T>) {\n  const type: VueTypeDef<T> = Object.defineProperties(obj as VueTypeDef<T>, {\n    _vueTypes_name: {\n      value: name,\n      writable: true,\n    },\n    isRequired: {\n      get() {\n        this.required = true\n        return this\n      },\n    },\n    def: {\n      value(def?: any) {\n        if (def === undefined) {\n          if (\n            this.type === Boolean ||\n            (Array.isArray(this.type) && this.type.includes(Boolean))\n          ) {\n            this.default = undefined\n            return\n          }\n          if (has(this, 'default')) {\n            delete this.default\n          }\n          return this\n        }\n        if (!isFunction(def) && validateType(this, def, true) !== true) {\n          warn(`${this._vueTypes_name} - invalid default value: \"${def}\"`)\n          return this\n        }\n        if (isArray(def)) {\n          this.default = () => [...def]\n        } else if (isPlainObject(def)) {\n          this.default = () => Object.assign({}, def)\n        } else {\n          this.default = def\n        }\n        return this\n      },\n    },\n  })\n\n  const { validator } = type\n  if (isFunction(validator)) {\n    type.validator = bindTo(validator, type)\n  }\n\n  return type\n}\n\n/**\n * Like `toType` but also adds the `validate()` method to the type object\n *\n * @param {string} name - Type internal name\n * @param {object} obj - Object to enhance\n */\nexport function toValidableType<T = any>(name: string, obj: PropOptions<T>) {\n  const type = toType<T>(name, obj)\n  return Object.defineProperty(type, 'validate', {\n    value(fn: (value: T) => boolean) {\n      if (isFunction(this.validator)) {\n        warn(\n          `${\n            this._vueTypes_name\n          } - calling .validate() will overwrite the current custom validator function. Validator info:\\n${JSON.stringify(\n            this,\n          )}`,\n        )\n      }\n      this.validator = bindTo(fn, this)\n      return this\n    },\n  }) as VueTypeValidableDef<T>\n}\n\n/**\n *  Clones an object preserving all of it's own keys.\n *\n * @param obj - Object to clone\n */\n\nexport function clone<T extends object>(obj: T): T {\n  const descriptors = {} as { [P in keyof T]: any }\n  Object.getOwnPropertyNames(obj).forEach((key) => {\n    descriptors[key as keyof T] = Object.getOwnPropertyDescriptor(obj, key)\n  })\n  return Object.defineProperties({}, descriptors) as T\n}\n\n/**\n * Return a new VueTypes type using another type as base.\n *\n * Properties in the `props` object will overwrite those defined in the source one\n * expect for the `validator` function. In that case both functions will be executed in series.\n *\n * @param name - Name of the new type\n * @param source - Source type\n * @param props - Custom type properties\n */\nexport function fromType<T extends VueTypeDef<any>>(name: string, source: T): T\nexport function fromType<\n  T extends VueTypeDef<any>,\n  V extends PropOptions<InferType<T>>,\n>(name: string, source: T, props: V): Omit<T, keyof V> & V\nexport function fromType<\n  T extends VueTypeDef<any>,\n  V extends PropOptions<InferType<T>>,\n>(name: string, source: T, props?: V) {\n  // 1. create an exact copy of the source type\n  const copy = clone(source)\n\n  // 2. give it a new name\n  copy._vueTypes_name = name\n\n  if (!isPlainObject(props)) {\n    return copy\n  }\n  const { validator, ...rest } = props\n\n  // 3. compose the validator function\n  // with the one on the source (if present)\n  // and ensure it is bound to the copy\n  if (isFunction(validator)) {\n    let { validator: prevValidator } = copy\n\n    if (prevValidator) {\n      prevValidator = unwrap(prevValidator) as (_v: any) => boolean\n    }\n\n    copy.validator = bindTo(\n      prevValidator\n        ? function (this: T, value: any) {\n            return (\n              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n              prevValidator!.call(this, value) && validator.call(this, value)\n            )\n          }\n        : validator,\n      copy,\n    )\n  }\n  // 4. overwrite the rest, if present\n  return Object.assign(copy, rest as V)\n}\n\nexport function indent(string: string) {\n  return string.replace(/^(?!\\s*$)/gm, '  ')\n}\n", "import { toType, toValidableType, isInteger, warn } from '../utils'\nimport { PropOptions, PropType } from '../types'\n\nexport const any = <T = any>() => toValidableType<T>('any', {})\n\nexport const func = <T extends (...args: any[]) => any>() =>\n  toValidableType<T>('function', {\n    type: Function as PropType<T>,\n  })\n\nexport const bool = () =>\n  toValidableType('boolean', {\n    type: Boolean,\n  })\n\nexport const string = <T extends string = string>() =>\n  toValidableType<T>('string', {\n    type: String as unknown as PropType<T>,\n  })\n\nexport const number = <T extends number = number>() =>\n  toValidableType<T>('number', {\n    type: Number as unknown as PropType<T>,\n  })\n\nexport const array = <T>() =>\n  toValidableType<T[]>('array', {\n    type: Array,\n  })\n\nexport const object = <T extends Record<string, any>>() =>\n  toValidableType<T>('object', {\n    type: Object,\n  })\n\nexport const integer = <T extends number = number>() =>\n  toType<T>('integer', {\n    type: Number as unknown as PropType<T>,\n    validator(value) {\n      const res = isInteger(value)\n      if (res === false) {\n        warn(`integer - \"${value}\" is not an integer`)\n      }\n      return res\n    },\n  })\n\nexport const symbol = () =>\n  toType<symbol>('symbol', {\n    validator(value: unknown) {\n      const res = typeof value === 'symbol'\n      if (res === false) {\n        warn(`symbol - invalid value \"${value}\"`)\n      }\n      return res\n    },\n  })\n\nexport const nullable = () =>\n  Object.defineProperty(\n    {\n      type: null as unknown as PropType<null>,\n      validator(value: unknown) {\n        const res = value === null\n        if (res === false) {\n          warn(`nullable - value should be null`)\n        }\n        return res\n      },\n    },\n    '_vueTypes_name',\n    { value: 'nullable' },\n  ) as PropOptions<null>\n", "import { toType, warn } from '../utils'\nimport { ValidatorFunction, VueTypeDef, PropType } from '../types'\n\nexport default function custom<T>(\n  validatorFn: ValidatorFunction<T>,\n  warnMsg = 'custom validation failed',\n) {\n  if (typeof validatorFn !== 'function') {\n    throw new TypeError(\n      '[VueTypes error]: You must provide a function as argument',\n    )\n  }\n\n  return toType<T>(validatorFn.name || '<<anonymous function>>', {\n    type: null as unknown as PropType<T>,\n    validator(this: VueTypeDef<T>, value: T) {\n      const valid = validatorFn(value)\n      if (!valid) warn(`${this._vueTypes_name} - ${warnMsg}`)\n      return valid\n    },\n  })\n}\n", "import { Prop, PropOptions } from '../types'\nimport { toType, warn, isArray } from '../utils'\n\nexport default function oneOf<D, T extends readonly D[] = readonly D[]>(\n  arr: T,\n) {\n  if (!isArray(arr)) {\n    throw new TypeError(\n      '[VueTypes error]: You must provide an array as argument.',\n    )\n  }\n  const msg = `oneOf - value should be one of \"${arr\n    .map((v: any) => (typeof v === 'symbol' ? v.toString() : v))\n    .join('\", \"')}\".`\n  const base: PropOptions<T[number]> = {\n    validator(value) {\n      const valid = arr.indexOf(value) !== -1\n      if (!valid) warn(msg)\n      return valid\n    },\n  }\n  if (arr.indexOf(null) === -1) {\n    const type = arr.reduce(\n      (ret, v) => {\n        if (v !== null && v !== undefined) {\n          const constr = (v as any).constructor\n          ret.indexOf(constr) === -1 && ret.push(constr)\n        }\n        return ret\n      },\n      [] as Prop<T[number]>[],\n    )\n\n    if (type.length > 0) {\n      base.type = type\n    }\n  }\n\n  return toType<T[number]>('oneOf', base)\n}\n", "import { Prop, VueProp, InferType, PropType } from '../types'\nimport {\n  isArray,\n  isComplexType,\n  isVueTypeDef,\n  isFunction,\n  toType,\n  validateType,\n  warn,\n  indent,\n} from '../utils'\n\nexport default function oneOfType<\n  D extends V,\n  U extends VueProp<any> | Prop<any> = any,\n  V = InferType<U>,\n>(arr: U[]) {\n  if (!isArray(arr)) {\n    throw new TypeError(\n      '[VueTypes error]: You must provide an array as argument',\n    )\n  }\n\n  let hasCustomValidators = false\n  let hasNullable = false\n\n  let nativeChecks: Prop<V>[] = []\n\n  // eslint-disable-next-line @typescript-eslint/prefer-for-of\n  for (let i = 0; i < arr.length; i += 1) {\n    const type = arr[i]\n    if (isComplexType<V>(type)) {\n      if (isFunction(type.validator)) {\n        hasCustomValidators = true\n      }\n      if (isVueTypeDef<V>(type, 'oneOf') && type.type) {\n        nativeChecks = nativeChecks.concat(type.type as PropType<V>)\n        continue\n      }\n      if (isVueTypeDef<V>(type, 'nullable')) {\n        hasNullable = true\n        continue\n      }\n      if (type.type === true || !type.type) {\n        warn('oneOfType - invalid usage of \"true\" and \"null\" as types.')\n        continue\n      }\n      nativeChecks = nativeChecks.concat(type.type)\n    } else {\n      nativeChecks.push(type as Prop<V>)\n    }\n  }\n\n  // filter duplicates\n  nativeChecks = nativeChecks.filter((t, i) => nativeChecks.indexOf(t) === i)\n\n  const typeProp =\n    hasNullable === false && nativeChecks.length > 0 ? nativeChecks : null\n\n  if (!hasCustomValidators) {\n    // we got just native objects (ie: Array, Object)\n    // delegate to Vue native prop check\n    return toType<D>('oneOfType', {\n      type: typeProp as unknown as PropType<D>,\n    })\n  }\n\n  return toType<D>('oneOfType', {\n    type: typeProp as unknown as PropType<D>,\n    validator(value) {\n      const err: string[] = []\n      const valid = arr.some((type) => {\n        const res = validateType(type, value, true)\n        if (typeof res === 'string') {\n          err.push(res)\n        }\n        return res === true\n      })\n\n      if (!valid) {\n        warn(\n          `oneOfType - provided value does not match any of the ${\n            err.length\n          } passed-in validators:\\n${indent(err.join('\\n'))}`,\n        )\n      }\n\n      return valid\n    },\n  })\n}\n", "import { Prop, VueProp, InferType } from '../types'\nimport { toType, validateType, warn, indent } from '../utils'\n\nexport default function arrayOf<T extends VueProp<any> | Prop<any>>(type: T) {\n  return toType<InferType<T>[]>('arrayOf', {\n    type: Array,\n    validator(values: any[]) {\n      let vResult: string | boolean = ''\n      const valid = values.every((value) => {\n        vResult = validateType(type, value, true)\n        return vResult === true\n      })\n      if (!valid) {\n        warn(`arrayOf - value validation error:\\n${indent(vResult as string)}`)\n      }\n      return valid\n    },\n  })\n}\n", "import { toType } from '../utils'\nimport { Constructor } from '../types'\n\nexport default function instanceOf<C extends Constructor>(\n  instanceConstructor: C,\n) {\n  return toType<InstanceType<C>>('instanceOf', {\n    type: instanceConstructor,\n  })\n}\n", "import { Prop, VueProp, InferType } from '../types'\nimport { toType, validateType, warn, indent } from '../utils'\n\nexport default function objectOf<T extends VueProp<any> | Prop<any>>(type: T) {\n  return toType<Record<string, InferType<T>>>('objectOf', {\n    type: Object,\n    validator(obj) {\n      let vResult: string | boolean = ''\n      const valid = Object.keys(obj).every((key) => {\n        vResult = validateType(type, obj[key], true)\n        return vResult === true\n      })\n\n      if (!valid) {\n        warn(`objectOf - value validation error:\\n${indent(vResult as string)}`)\n      }\n      return valid\n    },\n  })\n}\n", "import { <PERSON>p, VueProp, VueType<PERSON>ha<PERSON>, VueTypeLooseShape } from '../types'\nimport { toType, validateType, warn, isPlainObject, indent } from '../utils'\n\nexport default function shape<T extends object>(obj: {\n  [K in keyof T]: Prop<T[K]> | VueProp<T[K]>\n}): VueTypeShape<T> {\n  const keys = Object.keys(obj)\n  const requiredKeys = keys.filter((key) => !!(obj as any)[key]?.required)\n\n  const type = toType('shape', {\n    type: Object,\n    validator(this: VueTypeShape<T> | VueTypeLooseShape<T>, value) {\n      if (!isPlainObject(value)) {\n        return false\n      }\n      const valueKeys = Object.keys(value)\n\n      // check for required keys (if any)\n      if (\n        requiredKeys.length > 0 &&\n        requiredKeys.some((req) => valueKeys.indexOf(req) === -1)\n      ) {\n        const missing = requiredKeys.filter(\n          (req) => valueKeys.indexOf(req) === -1,\n        )\n        if (missing.length === 1) {\n          warn(`shape - required property \"${missing[0]}\" is not defined.`)\n        } else {\n          warn(\n            `shape - required properties \"${missing.join(\n              '\", \"',\n            )}\" are not defined.`,\n          )\n        }\n\n        return false\n      }\n\n      return valueKeys.every((key) => {\n        if (keys.indexOf(key) === -1) {\n          if ((this as VueTypeLooseShape<T>)._vueTypes_isLoose === true)\n            return true\n          warn(\n            `shape - shape definition does not include a \"${key}\" property. Allowed keys: \"${keys.join(\n              '\", \"',\n            )}\".`,\n          )\n          return false\n        }\n        const type = (obj as any)[key]\n        const valid = validateType(type, value[key], true)\n        if (typeof valid === 'string') {\n          warn(`shape - \"${key}\" property validation error:\\n ${indent(valid)}`)\n        }\n        return valid === true\n      })\n    },\n  }) as VueTypeShape<T>\n\n  Object.defineProperty(type, '_vueTypes_isLoose', {\n    writable: true,\n    value: false,\n  })\n\n  Object.defineProperty(type, 'loose', {\n    get() {\n      this._vueTypes_isLoose = true\n      return this\n    },\n  })\n\n  return type\n}\n", "import {\n  toType,\n  toValidableType,\n  validateType,\n  isArray,\n  isVueTypeDef,\n  has,\n  fromType,\n  warn,\n} from './utils'\n\nimport {\n  VueTypesDefaults,\n  ExtendProps,\n  VueTypeDef,\n  VueTypeValidableDef,\n  VueTypeShape,\n  VueTypeLooseShape,\n} from './types'\nimport { typeDefaults } from './sensibles'\nimport { PropOptions } from './types'\n\nimport {\n  any,\n  func,\n  bool,\n  string,\n  number,\n  array,\n  integer,\n  symbol,\n  object,\n  nullable,\n} from './validators/native'\nimport custom from './validators/custom'\nimport oneOf from './validators/oneof'\nimport oneOfType from './validators/oneoftype'\nimport arrayOf from './validators/arrayof'\nimport instanceOf from './validators/instanceof'\nimport objectOf from './validators/objectof'\nimport shape from './validators/shape'\nimport { config } from './config'\n\nconst BaseVueTypes = /*#__PURE__*/ (() =>\n  // eslint-disable-next-line @typescript-eslint/no-extraneous-class\n  class BaseVueTypes {\n    static defaults: Partial<VueTypesDefaults> = {}\n\n    static sensibleDefaults: Partial<VueTypesDefaults> | boolean\n\n    static config = config\n\n    static get any() {\n      return any()\n    }\n    static get func() {\n      return func().def(this.defaults.func)\n    }\n    static get bool() {\n      // prevent undefined to be explicitly set\n      if (this.defaults.bool === undefined) {\n        return bool()\n      }\n      return bool().def(this.defaults.bool)\n    }\n    static get string() {\n      return string().def(this.defaults.string)\n    }\n    static get number() {\n      return number().def(this.defaults.number)\n    }\n    static get array() {\n      return array().def(this.defaults.array)\n    }\n    static get object() {\n      return object().def(this.defaults.object)\n    }\n    static get integer() {\n      return integer().def(this.defaults.integer)\n    }\n    static get symbol() {\n      return symbol()\n    }\n\n    static get nullable() {\n      return nullable()\n    }\n\n    static readonly custom = custom\n    static readonly oneOf = oneOf\n    static readonly instanceOf = instanceOf\n    static readonly oneOfType = oneOfType\n    static readonly arrayOf = arrayOf\n    static readonly objectOf = objectOf\n    static readonly shape = shape\n\n    static extend<T = any>(props: ExtendProps | ExtendProps[]): T {\n      warn(\n        `VueTypes.extend is deprecated. Use the ES6+ method instead. See https://dwightjack.github.io/vue-types/advanced/extending-vue-types.html#extending-namespaced-validators-in-es6 for details.`,\n      )\n      if (isArray(props)) {\n        props.forEach((p) => this.extend(p))\n        return this as any\n      }\n\n      const { name, validate = false, getter = false, ...opts } = props\n\n      if (has(this, name as any)) {\n        throw new TypeError(`[VueTypes error]: Type \"${name}\" already defined`)\n      }\n\n      const { type } = opts\n      if (isVueTypeDef(type)) {\n        // we are using as base type a vue-type object\n\n        // detach the original type\n        // we are going to inherit the parent data.\n        delete opts.type\n\n        if (getter) {\n          return Object.defineProperty(this as unknown as T, name, {\n            get: () => fromType(name, type, opts as Omit<ExtendProps, 'type'>),\n          })\n        }\n        return Object.defineProperty(this as unknown as T, name, {\n          value(...args: unknown[]) {\n            const t = fromType(name, type, opts as Omit<ExtendProps, 'type'>)\n            if (t.validator) {\n              t.validator = t.validator.bind(t, ...args)\n            }\n            return t\n          },\n        })\n      }\n\n      let descriptor: PropertyDescriptor\n      if (getter) {\n        descriptor = {\n          get() {\n            const typeOptions = Object.assign({}, opts as PropOptions<T>)\n            if (validate) {\n              return toValidableType<T>(name, typeOptions)\n            }\n            return toType<T>(name, typeOptions)\n          },\n          enumerable: true,\n        }\n      } else {\n        descriptor = {\n          value(...args: T[]) {\n            const typeOptions = Object.assign({}, opts as PropOptions<T>)\n            let ret: VueTypeDef<T>\n            if (validate) {\n              ret = toValidableType<T>(name, typeOptions)\n            } else {\n              ret = toType<T>(name, typeOptions)\n            }\n\n            if (typeOptions.validator) {\n              ret.validator = typeOptions.validator.bind(ret, ...args)\n            }\n            return ret\n          },\n          enumerable: true,\n        }\n      }\n\n      return Object.defineProperty(this as unknown as T, name, descriptor)\n    }\n\n    static utils = {\n      validate<T, U>(value: T, type: U) {\n        return validateType<U, T>(type, value, true) === true\n      },\n      toType<T = unknown>(\n        name: string,\n        obj: PropOptions<T>,\n        validable = false,\n      ): VueTypeDef<T> | VueTypeValidableDef<T> {\n        return validable ? toValidableType<T>(name, obj) : toType<T>(name, obj)\n      },\n    }\n  })()\n\nfunction createTypes(defs: Partial<VueTypesDefaults> = typeDefaults()) {\n  return class extends BaseVueTypes {\n    static defaults: Partial<VueTypesDefaults> = { ...defs }\n\n    static get sensibleDefaults() {\n      return { ...this.defaults }\n    }\n\n    static set sensibleDefaults(v: boolean | Partial<VueTypesDefaults>) {\n      if (v === false) {\n        this.defaults = {}\n        return\n      }\n      if (v === true) {\n        this.defaults = { ...defs }\n        return\n      }\n      this.defaults = { ...v }\n    }\n  }\n}\n\nexport default class VueTypes /*#__PURE__*/ extends createTypes() {}\n\nexport {\n  any,\n  func,\n  bool,\n  string,\n  number,\n  array,\n  integer,\n  symbol,\n  object,\n  custom,\n  oneOf,\n  oneOfType,\n  arrayOf,\n  instanceOf,\n  objectOf,\n  shape,\n  nullable,\n  createTypes,\n  toType,\n  toValidableType,\n  validateType,\n  fromType,\n  config,\n}\n\nexport type VueTypesInterface = ReturnType<typeof createTypes>\nexport type { VueTypeDef, VueTypeValidableDef, VueTypeShape, VueTypeLooseShape }\n", "import { VueTypesDefaults } from './types'\n\nexport const typeDefaults = (): VueTypesDefaults => ({\n  func: () => undefined,\n  bool: true,\n  string: '',\n  number: 0,\n  array: () => [],\n  object: () => ({}),\n  integer: 0,\n})\n"], "mappings": ";;;AAOA,SAAS,SAASA,IAAG;AACnB,SAAO,OAAO,UAAU,SAAS,KAAKA,EAAC,MAAM;AAC/C;AAEA,SAAS,cAAcA,IAAG;AACxB,MAAI,MAAK;AAET,MAAI,SAASA,EAAC,MAAM,MAAO,QAAO;AAGlC,SAAOA,GAAE;AACT,MAAI,SAAS,OAAW,QAAO;AAG/B,SAAO,KAAK;AACZ,MAAI,SAAS,IAAI,MAAM,MAAO,QAAO;AAGrC,MAAI,KAAK,eAAe,eAAe,MAAM,OAAO;AAClD,WAAO;AAAA,EACT;AAGA,SAAO;AACT;;;;;;;;;;;;;;;;;;;;;AC7Ba,IAAAC,IAAyB,EACpCC,QAAAA,OACAC,UAAU,OAAA;AAFC,ICFbC,IAAA,CAAA,WAAA;ADEa,ICSPC,IAAWC,OAAOC;ADTX,ICUPC,IAAWH,EAASG;ADVb,ICWAC,IAASJ,EAASK;ADXlB,ICaPC,IAAkB;AAGR,SAAAC,EACdC,GAAAA;AAA8D,MAAAC;AAE9D,QAAMC,KAAiC,UAA7BD,KAAID,QAAAA,IAAAA,SAAAA,EAAqBE,SAAAA,WAAID,KAAAA,KAAID;AAC3C,MAAIE,IAAM;AACR,UAAMC,KAAQD,GAAKP,SAAAA,EAAWQ,MAAML,CAAAA;AACpC,WAAOK,KAAQA,GAAM,CAAA,IAAK;EAC5B;AACA,SAAO;AACT;AAAA,IASaC,IAAgBC;AAMb,SAAAC,IAAAA;AAAI;AASpB,IAAIC,IAAkED;AAEtE,IAA6B,MAAc;AACzC,QAAME,IAAgC,eAAA,OAAZC;AAC1BF,MAAOC,IACH,SAAcE,IAAaC,KAAQvB,EAAOE,UAAAA;AAAAA,cACpCF,EAAOC,UACToB,QAAQE,EAAAA,EAAAA,oBAA2BD,EAAAA,EAAAA;EAEvC,IACAJ;AACN;AAUO,IAAMM,IAAMA,CAAuBC,GAAQC,OAChDlB,EAAOmB,KAAKF,GAAKC,EAAAA;AADZ,IAUME,IACXC,OAAOD,aACP,SAAmBE,GAAAA;AACjB,SACmB,YAAA,OAAVA,KACPC,SAASD,CAAAA,KACTE,KAAKC,MAAMH,CAAAA,MAAWA;AAE1B;AAlBK,IA0BMI,IACXC,MAAMD,WACN,SAAiBJ,GAAAA;AACf,SAAgC,qBAAzBvB,EAASoB,KAAKG,CAAAA;AACvB;AA9BK,IAuCMM,IAAkCN,OACpB,wBAAzBvB,EAASoB,KAAKG,CAAAA;AAxCT,IA+CMO,IAAeA,CAC1BP,GACAQ,OAEAtB,EAAcc,CAAAA,KACdN,EAAIM,GAAO,gBAAA,MAAA,CACTQ,MAAQR,EAAMS,mBAAmBD;AArD9B,IA2DME,IAAoBV,OAC/Bd,EAAcc,CAAAA,MACbN,EAAIM,GAAO,MAAA,KACV,CAAC,kBAAkB,aAAa,WAAW,UAAA,EAAYW,KAAMC,CAAAA,OAC3DlB,EAAIM,GAAOY,EAAAA,CAAAA;AAcD,SAAAC,EAAO/B,GAA6BgC,IAAAA;AAClD,SAAOvC,OAAOwC,eAAejC,EAAGkC,KAAKF,EAAAA,GAAmB,cAAc,EACpEd,OAAOlB,EAAAA,CAAAA;AAEX;AAuBM,SAAUmC,EACdjC,GACAgB,IACA7B,KAAAA,OAAS;AAET,MAAI+C,IACAC,KAAAA,MACAC,KAAe;AAIjBF,EAAAA,KAHGhC,EAAcF,CAAAA,IAGHA,IAFA,EAAEA,MAAAA,EAAAA;AAIlB,QAAMqC,KAAad,EAAaW,EAAAA,IAC5BA,GAAYT,iBAAiB,QAC7B;AAEJ,MAAIC,EAAcQ,EAAAA,KAAqC,SAArBA,GAAYlC,MAAe;AAC3D,QAAA,WAAIkC,GAAYlC,QAAAA,SAAsBkC,GAAYlC,KAChD,QAAOmC;AAET,QAAA,CAAKD,GAAYI,YAAqB,QAATtB,GAC3B,QAAOmB;AAELf,MAAQc,GAAYlC,IAAAA,KACtBmC,KAAQD,GAAYlC,KAAK2B,KACtB3B,CAAAA,OAAAA,SAAciC,EAAajC,IAAMgB,IAAAA,IAAO,CAAA,GAE3CoB,KAAeF,GAAYlC,KACxBuC,IAAKvC,CAAAA,OAAcH,EAAQG,EAAAA,CAAAA,EAC3BwC,KAAK,MAAA,MAERJ,KAAevC,EAAQqC,EAAAA,GAGrBC,KADmB,YAAjBC,KACMhB,EAAQJ,EAAAA,IACU,aAAjBoB,KACDlC,EAAcc,EAAAA,IAEL,aAAjBoB,MACiB,aAAjBA,MACiB,cAAjBA,MACiB,eAAjBA,KAAAA,SA7LsBpB,IAAAA;AAC5B,UAAIA,QAAAA,GAAuC,QAAO;AAClD,YAAMf,KAAQe,GAAMyB,YAAYhD,SAAAA,EAAWQ,MAAML,CAAAA;AACjD,aAAOK,KAAQA,GAAM,CAAA,EAAGyC,QAAQ,UAAU,EAAA,IAAM;IAClD,EA2L8B1B,EAAAA,MAAWoB,KAEzBpB,cAAiBkB,GAAYlC;EAG3C;AAEA,MAAA,CAAKmC,IAAO;AACV,UAAM3B,KAAS,GAAA6B,EAAAA,UAAoBrB,EAAAA,wBAA6BoB,EAAAA;AAChE,WAAA,UAAIjD,MACFkB,EAAKG,EAAAA,GAAAA,SAGAA;EACT;AAEA,MAAIE,EAAIwB,IAAa,WAAA,KAAgBZ,EAAWY,GAAYS,SAAAA,GAAY;AACtE,UAAMC,KAAUvC,GACVwC,KAAoB,CAAA;AAQ1B,QAPAxC,IAAQG,CAAAA,OAAAA;AACNqC,MAAAA,GAAQC,KAAKtC,EAAAA;IACf,GAEA2B,KAAQD,GAAYS,UAAU3B,EAAAA,GAC9BX,IAAOuC,IAAAA,CAEFT,IAAO;AACV,YAAM3B,MAAOqC,GAAQE,SAAS,IAAI,OAAO,MAAMF,GAAQL,KAAK,MAAA;AAE5D,aADAK,GAAQE,SAAS,GAAA,UACb5D,MACFkB,EAAKG,EAAAA,GACE2B,MAEF3B;IACT;EACF;AACA,SAAO2B;AACT;AAAA,SAQgBa,EAAgBxB,GAAcb,IAAAA;AAC5C,QAAMX,KAAsBT,OAAO0D,iBAAiBtC,IAAsB,EACxEc,gBAAgB,EACdT,OAAOQ,GACP0B,UAAAA,KAAU,GAEZC,YAAY,EACVC,MAAAA;AAEE,WADAC,KAAKf,WAAAA,MACEe;EACT,EAAA,GAEFC,KAAK,EACHtC,MAAMsC,IAAAA;AACJ,WAAA,WAAIA,KAEAD,KAAKrD,SAASuD,WACblC,MAAMD,QAAQiC,KAAKrD,IAAAA,KAASqD,KAAKrD,KAAKwD,SAASD,OAAAA,IAAAA,MAEhDF,KAAKI,UAAAA,WAGH/C,EAAI2C,MAAM,SAAA,KAAA,OACLA,KAAKI,SAEPJ,QAEJ/B,EAAWgC,EAAAA,KAAAA,SAAQrB,EAAaoB,MAAMC,IAAAA,IAAK,KAK9CD,KAAKI,UADHrC,EAAQkC,EAAAA,IACK,MAAM,CAAA,GAAIA,EAAAA,IAChBpD,EAAcoD,EAAAA,IACR,MAAM/D,OAAOmE,OAAO,CAAA,GAAIJ,EAAAA,IAExBA,IAGnBD,SAXIhD,EAAAA,GAAQgD,KAAK5B,cAAAA,8BAA4C6B,EAAAA,GAAAA,GAClDD;EAUX,EAAA,EAAA,CAAA,GAAA,EAIEV,WAAEA,GAAAA,IAAc3C;AAKtB,SAJIsB,EAAWqB,EAAAA,MACb3C,GAAK2C,YAAYd,EAAOc,IAAW3C,EAAAA,IAG9BA;AACT;AAQgB,SAAA2D,EAAyBnC,GAAcb,IAAAA;AACrD,QAAMX,KAAOgD,EAAUxB,GAAMb,EAAAA;AAC7B,SAAOpB,OAAOwC,eAAe/B,IAAM,YAAY,EAC7CgB,MAAMlB,IAAAA;AAWJ,WAVIwB,EAAW+B,KAAKV,SAAAA,KAClBtC,EACE,GACEgD,KAAK5B,cAAAA;EAC0FmC,KAAKC,UACpGR,IAAAA,CAAAA,EAAAA,GAINA,KAAKV,YAAYd,EAAO/B,IAAIuD,IAAAA,GAE9BA;EAAA,EAAA,CAAA;AAEJ;AAAA,SA+BgBS,EAGdtC,GAAcuC,IAAWC,IAAAA;AAEzB,QAAMC,KA5BF,SAAkCtD,IAAAA;AACtC,UAAMuD,KAAc,CAAA;AAIpB,WAHA3E,OAAO4E,oBAAoBxD,EAAAA,EAAKyD,QAASC,CAAAA,OAAAA;AACvCH,MAAAA,GAAYG,EAAAA,IAAkB9E,OAAO+E,yBAAyB3D,IAAK0D,EAAAA;IAAG,CAAA,GAEjE9E,OAAO0D,iBAAiB,CAAE,GAAEiB,EAAAA;EACrC,EAsBqBH,EAAAA;AAKnB,MAFAE,GAAKxC,iBAAiBD,GAAAA,CAEjBtB,EAAc8D,EAAAA,EACjB,QAAOC;AAET,QAAA,EAAMtB,WAAEA,GAAAA,IAAuBqB,IAATO,KAAIC,EAAKR,IAAK3E,CAAAA;AAKpC,MAAIiC,EAAWqB,EAAAA,GAAY;AACzB,QAAA,EAAMA,WAAW8B,GAAAA,IAAkBR;AAE/BQ,IAAAA,OACFA,KAAAA,UArOJC,MADqD5E,KAsO1B2E,IArOFE,eAAAA,WAAUD,KAAAA,KAAI5E,KAwOrCmE,GAAKtB,YAAYd,EACf4C,KACI,SAAmBzD,IAAAA;AACjB,aAEEyD,GAAe5D,KAAKwC,MAAMrC,EAAAA,KAAU2B,GAAU9B,KAAKwC,MAAMrC,EAAAA;IAE7D,IACA2B,IACJsB,EAAAA;EAEJ;AApPc,MAAuCnE,IAAK4E;AAsP1D,SAAOnF,OAAOmE,OAAOO,IAAMM,EAAAA;AAC7B;AAEM,SAAUK,EAAOC,GAAAA;AACrB,SAAOA,EAAOnC,QAAQ,eAAe,IAAA;AACvC;AAAA,IC1ZaoC,IAAMA,MAAenB,EAAmB,OAAO,CAAA,CAAA;AD0Z5D,ICxZaoB,IAAOA,MAClBpB,EAAmB,YAAY,EAC7B3D,MAAMgF,SAAAA,CAAAA;ADsZV,ICnZaC,IAAOA,MAClBtB,EAAgB,WAAW,EACzB3D,MAAMuD,QAAAA,CAAAA;ADiZV,IC9YasB,IAASA,MACpBlB,EAAmB,UAAU,EAC3B3D,MAAMkF,OAAAA,CAAAA;AD4YV,ICzYaC,IAASA,MACpBxB,EAAmB,UAAU,EAC3B3D,MAAMe,OAAAA,CAAAA;ADuYV,ICpYaqE,IAAQA,MACnBzB,EAAqB,SAAS,EAC5B3D,MAAMqB,MAAAA,CAAAA;ADkYV,IC/XagE,IAASA,MACpB1B,EAAmB,UAAU,EAC3B3D,MAAMT,OAAAA,CAAAA;AD6XV,IC1Xa+F,IAAUA,MACrBtC,EAAU,WAAW,EACnBhD,MAAMe,QACN4B,UAAU3B,GAAAA;AACR,QAAMuE,KAAMzE,EAAUE,CAAAA;AAItB,SAAA,UAHIuE,MACFlF,EAAK,cAAcW,CAAAA,qBAAAA,GAEduE;AACT,EAAA,CAAA;ADiXJ,IC9WaC,IAASA,MACpBxC,EAAe,UAAU,EACvBL,UAAU3B,GAAAA;AACR,QAAMuE,KAAuB,YAAA,OAAVvE;AAInB,SAAA,UAHIuE,MACFlF,EAAK,2BAA2BW,CAAAA,GAAAA,GAE3BuE;AACT,EAAA,CAAA;ADsWJ,ICnWaE,IAAWA,MACtBlG,OAAOwC,eACL,EACE/B,MAAM,MACN2C,UAAU3B,GAAAA;AACR,QAAMuE,KAAgB,SAAVvE;AAIZ,SAAA,UAHIuE,MACFlF,EAAsC,iCAAA,GAEjCkF;AACT,EAAA,GAEF,kBACA,EAAEvE,OAAO,WAAA,CAAA;ACpEW,SAAA0E,EACtBC,GACAC,KAAU,4BAAA;AAEV,MAA2B,cAAA,OAAhBD,EACT,OAAA,IAAUE,UACR,2DAAA;AAIJ,SAAO7C,EAAU2C,EAAYnE,QAAQ,0BAA0B,EAC7DxB,MAAM,MACN2C,UAA+B3B,IAAAA;AAC7B,UAAMmB,KAAQwD,EAAY3E,EAAAA;AAE1B,WADKmB,MAAO9B,EAAAA,GAAQgD,KAAK5B,cAAAA,MAAoBmE,EAAAA,EAAAA,GACtCzD;EACT,EAAA,CAAA;AAEJ;AClBwB,SAAA2D,EACtBC,GAAAA;AAEA,MAAA,CAAK3E,EAAQ2E,CAAAA,EACX,OAAA,IAAUF,UACR,0DAAA;AAGJ,QAAMrF,KAAM,mCAAmCuF,EAC5CxD,IAAKyD,CAAAA,OAAyB,YAAA,OAANA,KAAiBA,GAAEvG,SAAAA,IAAauG,EAAAA,EACxDxD,KAAK,MAAA,CAAA,MACFyD,KAA+B,EACnCtD,UAAU3B,IAAAA;AACR,UAAMmB,KAAAA,OAAQ4D,EAAIG,QAAQlF,EAAAA;AAE1B,WADKmB,MAAO9B,EAAKG,EAAAA,GACV2B;EACT,EAAA;AAEF,MAAA,OAAI4D,EAAIG,QAAQ,IAAA,GAAc;AAC5B,UAAMlG,KAAO+F,EAAII,OACf,CAACC,IAAKJ,OAAAA;AACJ,UAAIA,QAAAA,IAA+B;AACjC,cAAMK,KAAUL,GAAUvD;AAAAA,eAC1B2D,GAAIF,QAAQG,EAAAA,KAAkBD,GAAItD,KAAKuD,EAAAA;MACzC;AACA,aAAOD;IAAAA,GAET,CAAA,CAAA;AAGEpG,IAAAA,GAAK+C,SAAS,MAChBkD,GAAKjG,OAAOA;EAEhB;AAEA,SAAOgD,EAAkB,SAASiD,EAAAA;AACpC;AC3BwB,SAAAK,EAItBP,GAAAA;AACA,MAAA,CAAK3E,EAAQ2E,CAAAA,EACX,OAAA,IAAUF,UACR,yDAAA;AAIJ,MAAIU,KAAAA,OACAC,KAAAA,OAEAC,KAA0B,CAAA;AAG9B,WAASC,KAAI,GAAGA,KAAIX,EAAIhD,QAAQ2D,MAAK,GAAG;AACtC,UAAM1G,KAAO+F,EAAIW,EAAAA;AACjB,QAAIhF,EAAiB1B,EAAAA,GAAO;AAI1B,UAHIsB,EAAWtB,GAAK2C,SAAAA,MAClB4D,KAAAA,OAEEhF,EAAgBvB,IAAM,OAAA,KAAYA,GAAKA,MAAM;AAC/CyG,QAAAA,KAAeA,GAAaE,OAAO3G,GAAKA,IAAAA;AACxC;MACF;AACA,UAAIuB,EAAgBvB,IAAM,UAAA,GAAa;AACrCwG,QAAAA,KAAAA;AACA;MACF;AACA,UAAA,SAAIxG,GAAKA,QAAAA,CAAkBA,GAAKA,MAAM;AACpCK,UAAK,0DAAA;AACL;MACF;AACAoG,MAAAA,KAAeA,GAAaE,OAAO3G,GAAKA,IAAAA;IAC1C,MACEyG,CAAAA,GAAa3D,KAAK9C,EAAAA;EAEtB;AAGAyG,EAAAA,KAAeA,GAAaG,OAAO,CAACC,IAAGH,OAAMD,GAAaP,QAAQW,EAAAA,MAAOH,EAAAA;AAEzE,QAAMI,KAAAA,UACJN,MAAyBC,GAAa1D,SAAS,IAAI0D,KAAe;AAEpE,SAQOzD,EAAU,aARZuD,KAQyB,EAC5BvG,MAAM8G,IACNnE,UAAU3B,IAAAA;AACR,UAAM+F,KAAgB,CAAA,GAChB5E,KAAQ4D,EAAIpE,KAAM3B,CAAAA,OAAAA;AACtB,YAAMuF,KAAMtD,EAAajC,IAAMgB,IAAAA,IAAO;AAItC,aAHmB,YAAA,OAARuE,MACTwB,GAAIjE,KAAKyC,EAAAA,GAAAA,SAEJA;IAAQ,CAAA;AAWjB,WARKpD,MACH9B,EAEI,wDAAA0G,GAAIhE,MAAAA;EACqB6B,EAAOmC,GAAIvE,KAAK,IAAA,CAAA,CAAA,EAAA,GAIxCL;EACT,EAAA,IA1B8B,EAC5BnC,MAAM8G,GAAAA,CAAAA;AA2BZ;ACvFwB,SAAAE,EAA4ChH,GAAAA;AAClE,SAAOgD,EAAuB,WAAW,EACvChD,MAAMqB,OACNsB,UAAUsE,IAAAA;AACR,QAAIC,KAA4B;AAChC,UAAM/E,KAAQ8E,GAAOE,MAAOnG,CAAAA,QAC1BkG,KAAUjF,EAAajC,GAAMgB,IAAAA,IAAO,GAAA,SAC7BkG,GAAAA;AAKT,WAHK/E,MACH9B,EAAAA;EAA2CuE,EAAOsC,EAAAA,CAAAA,EAAAA,GAE7C/E;EACT,EAAA,CAAA;AAEJ;ACfwB,SAAAiF,EACtBC,GAAAA;AAEA,SAAOrE,EAAwB,cAAc,EAC3ChD,MAAMqH,EAAAA,CAAAA;AAEV;ACNwB,SAAAC,EAA6CtH,GAAAA;AACnE,SAAOgD,EAAqC,YAAY,EACtDhD,MAAMT,QACNoD,UAAUhC,IAAAA;AACR,QAAIuG,KAA4B;AAChC,UAAM/E,KAAQ5C,OAAOgI,KAAK5G,EAAAA,EAAKwG,MAAO9C,CAAAA,QACpC6C,KAAUjF,EAAajC,GAAMW,GAAI0D,EAAAA,GAAAA,IAAM,GAAA,SAChC6C,GAAAA;AAMT,WAHK/E,MACH9B,EAAAA;EAA4CuE,EAAOsC,EAAAA,CAAAA,EAAAA,GAE9C/E;EACT,EAAA,CAAA;AAEJ;AChBwB,SAAAqF,EAAwB7G,GAAAA;AAG9C,QAAM4G,KAAOhI,OAAOgI,KAAK5G,CAAAA,GACnB8G,KAAeF,GAAKX,OAAQvC,CAAAA,OAAAA;AAAG,QAAAqD;AAAK,WAAA,EAAA,UAACA,KAAE/G,EAAY0D,EAAAA,MAAAA,WAAIqD,MAAAA,CAAhBA,GAAkBpF;EAAQ,CAAA,GAEjEtC,KAAOgD,EAAO,SAAS,EAC3BhD,MAAMT,QACNoD,UAAwD3B,IAAAA;AACtD,QAAA,CAAKd,EAAcc,EAAAA,EACjB,QAAA;AAEF,UAAM2G,KAAYpI,OAAOgI,KAAKvG,EAAAA;AAG9B,QACEyG,GAAa1E,SAAS,KACtB0E,GAAa9F,KAAMiG,CAAAA,OAAAA,OAAQD,GAAUzB,QAAQ0B,EAAAA,CAAAA,GAC7C;AACA,YAAMC,KAAUJ,GAAab,OAC1BgB,CAAAA,OAAAA,OAAQD,GAAUzB,QAAQ0B,EAAAA,CAAAA;AAY7B,aATEvH,EADqB,MAAnBwH,GAAQ9E,SAAAA,8BACyB8E,GAAQ,CAAA,CAAA,sBAGT,gCAAAA,GAAQrF,KACtC,MAAA,CAAA,oBAAA,GAAA;IAMR;AAEA,WAAOmF,GAAUR,MAAO9C,CAAAA,OAAAA;AACtB,UAAA,OAAIkD,GAAKrB,QAAQ7B,EAAAA,EACf,QAAA,SAAKhB,KAA8ByE,sBAEnCzH,EACE,gDAAgDgE,EAAAA,8BAAiCkD,GAAK/E,KACpF,MAAA,CAAA,IAAA,GAAA;AAKN,YACML,KAAQF,EADAtB,EAAY0D,EAAAA,GACOrD,GAAMqD,EAAAA,GAAAA,IAAM;AAI7C,aAHqB,YAAA,OAAVlC,MACT9B,EAAiB,YAAAgE,EAAAA;GAAqCO,EAAOzC,EAAAA,CAAAA,EAAAA,GAAAA,SAExDA;IAAU,CAAA;EAErB,EAAA,CAAA;AAeF,SAZA5C,OAAOwC,eAAe/B,IAAM,qBAAqB,EAC/CkD,UAAAA,MACAlC,OAAAA,MAAO,CAAA,GAGTzB,OAAOwC,eAAe/B,IAAM,SAAS,EACnCoD,MAAAA;AAEE,WADAC,KAAKyE,oBAAAA,MAAoB;EAE3B,EAAA,CAAA,GAGK9H;AACT;AAAA,IAAA,IAAA,CAAA,QAAA,YAAA,QAAA;AAAA,IC7BM+H,KAA8BC,SAAAA,IAElC,MAAA;EAOE,WAAA,MAAWlD;AACT,WAAOA,EAAAA;EACT;EACA,WAAA,OAAWC;AACT,WAAOA,EAAAA,EAAOzB,IAAID,KAAK4E,SAASlD,IAAAA;EAClC;EACA,WAAA,OAAWE;AAET,WAAA,WAAI5B,KAAK4E,SAAShD,OACTA,EAAAA,IAEFA,EAAAA,EAAO3B,IAAID,KAAK4E,SAAShD,IAAAA;EAClC;EACA,WAAA,SAAWJ;AACT,WAAOA,EAAAA,EAASvB,IAAID,KAAK4E,SAASpD,MAAAA;EACpC;EACA,WAAA,SAAWM;AACT,WAAOA,EAAAA,EAAS7B,IAAID,KAAK4E,SAAS9C,MAAAA;EACpC;EACA,WAAA,QAAWC;AACT,WAAOA,EAAAA,EAAQ9B,IAAID,KAAK4E,SAAS7C,KAAAA;EACnC;EACA,WAAA,SAAWC;AACT,WAAOA,EAAAA,EAAS/B,IAAID,KAAK4E,SAAS5C,MAAAA;EACpC;EACA,WAAA,UAAWC;AACT,WAAOA,EAAAA,EAAUhC,IAAID,KAAK4E,SAAS3C,OAAAA;EACrC;EACA,WAAA,SAAWE;AACT,WAAOA,EAAAA;EACT;EAEA,WAAA,WAAWC;AACT,WAAOA,EAAAA;EACT;EAUA,OAAA,OAAuBzB,IAAAA;AAIrB,QAHA3D,EACgM,8LAAA,GAE5Le,EAAQ4C,EAAAA,EAEV,QADAA,GAAMI,QAAS8D,CAAAA,OAAM7E,KAAK8E,OAAOD,EAAAA,CAAAA,GAEnC7E;AAEA,UAAA,EAAM7B,MAAEA,IAAI4G,UAAEA,KAAAA,OAAgBC,QAAEA,KAAAA,MAAS,IAAmBrE,IAATsE,KAAI9D,EAAKR,IAAK3E,CAAAA;AAEjE,QAAIqB,EAAI2C,MAAM7B,EAAAA,EACZ,OAAM,IAAIqE,UAAAA,2BAAqCrE,EAAAA,mBAAAA;AAGjD,UAAA,EAAMxB,MAAEA,GAAAA,IAASsI;AACjB,QAAI/G,EAAavB,EAAAA,EAOf,QAAA,OAFOsI,GAAKtI,MAGHT,OAAOwC,eAAesB,MAAsB7B,IADjD6G,KACuD,EACvDjF,KAAKA,MAAMU,EAAStC,IAAMxB,IAAMsI,EAAAA,EAAAA,IAGqB,EACvDtH,SAASuH,IAAAA;AACP,YAAM1B,KAAI/C,EAAStC,IAAMxB,IAAMsI,EAAAA;AAI/B,aAHIzB,GAAElE,cACJkE,GAAElE,YAAYkE,GAAElE,UAAUX,KAAK6E,IAAAA,GAAM0B,EAAAA,IAEhC1B;IACT,EAAA,CAAA;AAIJ,QAAI2B;AAgCJ,WA9BEA,KADEH,KACW,EACXjF,MAAAA;AACE,YAAMqF,KAAclJ,OAAOmE,OAAO,CAAE,GAAE4E,EAAAA;AACtC,aAAIF,KACKzE,EAAmBnC,IAAMiH,EAAAA,IAE3BzF,EAAUxB,IAAMiH,EAAAA;IACzB,GACAC,YAAAA,KAAY,IAGD,EACX1H,SAASuH,IAAAA;AACP,YAAME,KAAclJ,OAAOmE,OAAO,CAAE,GAAE4E,EAAAA;AACtC,UAAIlC;AAUJ,aAREA,KADEgC,KACIzE,EAAmBnC,IAAMiH,EAAAA,IAEzBzF,EAAUxB,IAAMiH,EAAAA,GAGpBA,GAAY9F,cACdyD,GAAIzD,YAAY8F,GAAY9F,UAAUX,KAAKoE,IAAAA,GAAQmC,EAAAA,IAE9CnC;IACT,GACAsC,YAAAA,KAAY,GAITnJ,OAAOwC,eAAesB,MAAsB7B,IAAMgH,EAAAA;EAC3D;AAAA,GA1HOP,WAAsC,CAAA,GAAED,EAExCW,mBAAAA,QAAgBX,EAEhB9I,SAASA,GAAM8I,EAsCNtC,SAASA,GAAMsC,EACflC,QAAQA,GAAKkC,EACbZ,aAAaA,GAAUY,EACvB1B,YAAYA,GAAS0B,EACrBhB,UAAUA,GAAOgB,EACjBV,WAAWA,GAAQU,EACnBR,QAAQA,GAAKQ,EA4EtBY,QAAQ,EACbR,UAAQA,CAAOpH,IAAUhB,OAAAA,SAChBiC,EAAmBjC,IAAMgB,IAAAA,IAAO,GAEzCgC,QAAMA,CACJxB,IACAb,IACAkI,KAAAA,UAEOA,KAAYlF,EAAmBnC,IAAMb,EAAAA,IAAOqC,EAAUxB,IAAMb,EAAAA,EAAAA,GAEtEqH,IA1I8B;AA6InC,SAASc,EAAYC,ICtLOC,EAC1BjE,MAAMA,MAAAA;AAAe,GACrBE,MAAAA,MACAJ,QAAQ,IACRM,QAAQ,GACRC,OAAOA,MAAM,CAAA,GACbC,QAAQA,OAAO,CAAE,IACjBC,SAAS,EAAA,GAAA;AD+K0D2D,MAAAA;AACnE,UAAAA,KAAO,cAAclB,EAAAA;IAGnB,WAAA,mBAAWY;AACT,aAAAO,EAAY,CAAA,GAAA7F,KAAK4E,QAAAA;IACnB;IAEA,WAAA,iBAA4BjC,IAAAA;AAS1B3C,WAAK4E,WAAAA,UARDjC,KAQSkD,EAAA,CAAA,GAAA,SAJTlD,KAIiBA,KAHE+C,CAAAA,IAJL,CAAA;IAQpB;EAAA,GAhBOd,WAAQiB,EAAA,CAAA,GAAmCH,CAAAA,GAAIE;AAkB1D;AAEqB,IAAAE,IAAA,cAA+BL,EAAAA,EAAAA;AAAAA;", "names": ["o", "config", "silent", "logLevel", "_excluded", "Obj<PERSON><PERSON><PERSON>", "Object", "prototype", "toString", "hasOwn", "hasOwnProperty", "FN_MATCH_REGEXP", "getType", "fn", "_fn$type", "type", "match", "isPlainObject", "_isPlainObject", "noop", "warn", "hasConsole", "console", "msg", "level", "has", "obj", "prop", "call", "isInteger", "Number", "value", "isFinite", "Math", "floor", "isArray", "Array", "isFunction", "isVueTypeDef", "name", "_vueTypes_name", "isComplexType", "some", "k", "bindTo", "ctx", "defineProperty", "bind", "validateType", "typeToCheck", "valid", "expectedType", "namePrefix", "required", "map", "join", "constructor", "replace", "validator", "old<PERSON>arn", "warnLog", "push", "length", "toType", "defineProperties", "writable", "isRequired", "get", "this", "def", "Boolean", "includes", "default", "assign", "toValidableType", "JSON", "stringify", "fromType", "source", "props", "copy", "descriptors", "getOwnPropertyNames", "for<PERSON>ach", "key", "getOwnPropertyDescriptor", "rest", "_objectWithoutPropertiesLoose", "prevValidator", "_fn$__original", "__original", "indent", "string", "any", "func", "Function", "bool", "String", "number", "array", "object", "integer", "res", "symbol", "nullable", "custom", "validatorFn", "warnMsg", "TypeError", "oneOf", "arr", "v", "base", "indexOf", "reduce", "ret", "constr", "oneOfType", "hasCustomValidators", "hasNullable", "nativeChecks", "i", "concat", "filter", "t", "typeProp", "err", "arrayOf", "values", "vResult", "every", "instanceOf", "instanceConstructor", "objectOf", "keys", "shape", "requiredKeys", "_obj$key", "valueKeys", "req", "missing", "_vueTypes_isLoose", "BaseVueTypes", "_BaseVueTypes", "defaults", "p", "extend", "validate", "getter", "opts", "args", "descriptor", "typeOptions", "enumerable", "sensibleDefault<PERSON>", "utils", "validable", "createTypes", "defs", "typeDefaults", "_Class", "_extends", "VueTypes"]}