import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/.pnpm/quill-magic-url@4.2.0/node_modules/quill-magic-url/dist/index.js
var require_dist = __commonJS({
  "node_modules/.pnpm/quill-magic-url@4.2.0/node_modules/quill-magic-url/dist/index.js"(exports, module) {
    !function(t, e) {
      if ("object" == typeof exports && "object" == typeof module) module.exports = e();
      else if ("function" == typeof define && define.amd) define([], e);
      else {
        var r = e();
        for (var n in r) ("object" == typeof exports ? exports : t)[n] = r[n];
      }
    }(window, function() {
      return function(t) {
        var e = {};
        function r(n) {
          if (e[n]) return e[n].exports;
          var o = e[n] = { i: n, l: false, exports: {} };
          return t[n].call(o.exports, o, o.exports, r), o.l = true, o.exports;
        }
        return r.m = t, r.c = e, r.d = function(t2, e2, n) {
          r.o(t2, e2) || Object.defineProperty(t2, e2, { enumerable: true, get: n });
        }, r.r = function(t2) {
          "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(t2, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(t2, "__esModule", { value: true });
        }, r.t = function(t2, e2) {
          if (1 & e2 && (t2 = r(t2)), 8 & e2) return t2;
          if (4 & e2 && "object" == typeof t2 && t2 && t2.__esModule) return t2;
          var n = /* @__PURE__ */ Object.create(null);
          if (r.r(n), Object.defineProperty(n, "default", { enumerable: true, value: t2 }), 2 & e2 && "string" != typeof t2) for (var o in t2) r.d(n, o, function(e3) {
            return t2[e3];
          }.bind(null, o));
          return n;
        }, r.n = function(t2) {
          var e2 = t2 && t2.__esModule ? function() {
            return t2.default;
          } : function() {
            return t2;
          };
          return r.d(e2, "a", e2), e2;
        }, r.o = function(t2, e2) {
          return Object.prototype.hasOwnProperty.call(t2, e2);
        }, r.p = "", r(r.s = 17);
      }([function(t, e, r) {
        "use strict";
        var n = r(7), o = "function" == typeof Symbol && "symbol" == typeof Symbol("foo"), i = Object.prototype.toString, s = Array.prototype.concat, a = Object.defineProperty, u = r(25)(), p = a && u, c = function(t2, e2, r2, n2) {
          var o2;
          (!(e2 in t2) || "function" == typeof (o2 = n2) && "[object Function]" === i.call(o2) && n2()) && (p ? a(t2, e2, { configurable: true, enumerable: false, value: r2, writable: true }) : t2[e2] = r2);
        }, l = function(t2, e2) {
          var r2 = arguments.length > 2 ? arguments[2] : {}, i2 = n(e2);
          o && (i2 = s.call(i2, Object.getOwnPropertySymbols(e2)));
          for (var a2 = 0; a2 < i2.length; a2 += 1) c(t2, i2[a2], e2[i2[a2]], r2[i2[a2]]);
        };
        l.supportsDescriptors = !!p, t.exports = l;
      }, function(t, e, r) {
        "use strict";
        var n = r(9);
        t.exports = function() {
          return n() && !!Symbol.toStringTag;
        };
      }, function(t, e, r) {
        "use strict";
        var n = SyntaxError, o = Function, i = TypeError, s = function(t2) {
          try {
            return o('"use strict"; return (' + t2 + ").constructor;")();
          } catch (t3) {
          }
        }, a = Object.getOwnPropertyDescriptor;
        if (a) try {
          a({}, "");
        } catch (t2) {
          a = null;
        }
        var u = function() {
          throw new i();
        }, p = a ? function() {
          try {
            return u;
          } catch (t2) {
            try {
              return a(arguments, "callee").get;
            } catch (t3) {
              return u;
            }
          }
        }() : u, c = r(21)(), l = Object.getPrototypeOf || function(t2) {
          return t2.__proto__;
        }, f = {}, h = "undefined" == typeof Uint8Array ? void 0 : l(Uint8Array), y = { "%AggregateError%": "undefined" == typeof AggregateError ? void 0 : AggregateError, "%Array%": Array, "%ArrayBuffer%": "undefined" == typeof ArrayBuffer ? void 0 : ArrayBuffer, "%ArrayIteratorPrototype%": c ? l([][Symbol.iterator]()) : void 0, "%AsyncFromSyncIteratorPrototype%": void 0, "%AsyncFunction%": f, "%AsyncGenerator%": f, "%AsyncGeneratorFunction%": f, "%AsyncIteratorPrototype%": f, "%Atomics%": "undefined" == typeof Atomics ? void 0 : Atomics, "%BigInt%": "undefined" == typeof BigInt ? void 0 : BigInt, "%Boolean%": Boolean, "%DataView%": "undefined" == typeof DataView ? void 0 : DataView, "%Date%": Date, "%decodeURI%": decodeURI, "%decodeURIComponent%": decodeURIComponent, "%encodeURI%": encodeURI, "%encodeURIComponent%": encodeURIComponent, "%Error%": Error, "%eval%": eval, "%EvalError%": EvalError, "%Float32Array%": "undefined" == typeof Float32Array ? void 0 : Float32Array, "%Float64Array%": "undefined" == typeof Float64Array ? void 0 : Float64Array, "%FinalizationRegistry%": "undefined" == typeof FinalizationRegistry ? void 0 : FinalizationRegistry, "%Function%": o, "%GeneratorFunction%": f, "%Int8Array%": "undefined" == typeof Int8Array ? void 0 : Int8Array, "%Int16Array%": "undefined" == typeof Int16Array ? void 0 : Int16Array, "%Int32Array%": "undefined" == typeof Int32Array ? void 0 : Int32Array, "%isFinite%": isFinite, "%isNaN%": isNaN, "%IteratorPrototype%": c ? l(l([][Symbol.iterator]())) : void 0, "%JSON%": "object" == typeof JSON ? JSON : void 0, "%Map%": "undefined" == typeof Map ? void 0 : Map, "%MapIteratorPrototype%": "undefined" != typeof Map && c ? l((/* @__PURE__ */ new Map())[Symbol.iterator]()) : void 0, "%Math%": Math, "%Number%": Number, "%Object%": Object, "%parseFloat%": parseFloat, "%parseInt%": parseInt, "%Promise%": "undefined" == typeof Promise ? void 0 : Promise, "%Proxy%": "undefined" == typeof Proxy ? void 0 : Proxy, "%RangeError%": RangeError, "%ReferenceError%": ReferenceError, "%Reflect%": "undefined" == typeof Reflect ? void 0 : Reflect, "%RegExp%": RegExp, "%Set%": "undefined" == typeof Set ? void 0 : Set, "%SetIteratorPrototype%": "undefined" != typeof Set && c ? l((/* @__PURE__ */ new Set())[Symbol.iterator]()) : void 0, "%SharedArrayBuffer%": "undefined" == typeof SharedArrayBuffer ? void 0 : SharedArrayBuffer, "%String%": String, "%StringIteratorPrototype%": c ? l(""[Symbol.iterator]()) : void 0, "%Symbol%": c ? Symbol : void 0, "%SyntaxError%": n, "%ThrowTypeError%": p, "%TypedArray%": h, "%TypeError%": i, "%Uint8Array%": "undefined" == typeof Uint8Array ? void 0 : Uint8Array, "%Uint8ClampedArray%": "undefined" == typeof Uint8ClampedArray ? void 0 : Uint8ClampedArray, "%Uint16Array%": "undefined" == typeof Uint16Array ? void 0 : Uint16Array, "%Uint32Array%": "undefined" == typeof Uint32Array ? void 0 : Uint32Array, "%URIError%": URIError, "%WeakMap%": "undefined" == typeof WeakMap ? void 0 : WeakMap, "%WeakRef%": "undefined" == typeof WeakRef ? void 0 : WeakRef, "%WeakSet%": "undefined" == typeof WeakSet ? void 0 : WeakSet }, g = { "%ArrayBufferPrototype%": ["ArrayBuffer", "prototype"], "%ArrayPrototype%": ["Array", "prototype"], "%ArrayProto_entries%": ["Array", "prototype", "entries"], "%ArrayProto_forEach%": ["Array", "prototype", "forEach"], "%ArrayProto_keys%": ["Array", "prototype", "keys"], "%ArrayProto_values%": ["Array", "prototype", "values"], "%AsyncFunctionPrototype%": ["AsyncFunction", "prototype"], "%AsyncGenerator%": ["AsyncGeneratorFunction", "prototype"], "%AsyncGeneratorPrototype%": ["AsyncGeneratorFunction", "prototype", "prototype"], "%BooleanPrototype%": ["Boolean", "prototype"], "%DataViewPrototype%": ["DataView", "prototype"], "%DatePrototype%": ["Date", "prototype"], "%ErrorPrototype%": ["Error", "prototype"], "%EvalErrorPrototype%": ["EvalError", "prototype"], "%Float32ArrayPrototype%": ["Float32Array", "prototype"], "%Float64ArrayPrototype%": ["Float64Array", "prototype"], "%FunctionPrototype%": ["Function", "prototype"], "%Generator%": ["GeneratorFunction", "prototype"], "%GeneratorPrototype%": ["GeneratorFunction", "prototype", "prototype"], "%Int8ArrayPrototype%": ["Int8Array", "prototype"], "%Int16ArrayPrototype%": ["Int16Array", "prototype"], "%Int32ArrayPrototype%": ["Int32Array", "prototype"], "%JSONParse%": ["JSON", "parse"], "%JSONStringify%": ["JSON", "stringify"], "%MapPrototype%": ["Map", "prototype"], "%NumberPrototype%": ["Number", "prototype"], "%ObjectPrototype%": ["Object", "prototype"], "%ObjProto_toString%": ["Object", "prototype", "toString"], "%ObjProto_valueOf%": ["Object", "prototype", "valueOf"], "%PromisePrototype%": ["Promise", "prototype"], "%PromiseProto_then%": ["Promise", "prototype", "then"], "%Promise_all%": ["Promise", "all"], "%Promise_reject%": ["Promise", "reject"], "%Promise_resolve%": ["Promise", "resolve"], "%RangeErrorPrototype%": ["RangeError", "prototype"], "%ReferenceErrorPrototype%": ["ReferenceError", "prototype"], "%RegExpPrototype%": ["RegExp", "prototype"], "%SetPrototype%": ["Set", "prototype"], "%SharedArrayBufferPrototype%": ["SharedArrayBuffer", "prototype"], "%StringPrototype%": ["String", "prototype"], "%SymbolPrototype%": ["Symbol", "prototype"], "%SyntaxErrorPrototype%": ["SyntaxError", "prototype"], "%TypedArrayPrototype%": ["TypedArray", "prototype"], "%TypeErrorPrototype%": ["TypeError", "prototype"], "%Uint8ArrayPrototype%": ["Uint8Array", "prototype"], "%Uint8ClampedArrayPrototype%": ["Uint8ClampedArray", "prototype"], "%Uint16ArrayPrototype%": ["Uint16Array", "prototype"], "%Uint32ArrayPrototype%": ["Uint32Array", "prototype"], "%URIErrorPrototype%": ["URIError", "prototype"], "%WeakMapPrototype%": ["WeakMap", "prototype"], "%WeakSetPrototype%": ["WeakSet", "prototype"] }, d = r(3), b = r(23), m = d.call(Function.call, Array.prototype.concat), v = d.call(Function.apply, Array.prototype.splice), x = d.call(Function.call, String.prototype.replace), w = d.call(Function.call, String.prototype.slice), j = d.call(Function.call, RegExp.prototype.exec), O = /[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g, A = /\\(\\)?/g, P = function(t2) {
          var e2 = w(t2, 0, 1), r2 = w(t2, -1);
          if ("%" === e2 && "%" !== r2) throw new n("invalid intrinsic syntax, expected closing `%`");
          if ("%" === r2 && "%" !== e2) throw new n("invalid intrinsic syntax, expected opening `%`");
          var o2 = [];
          return x(t2, O, function(t3, e3, r3, n2) {
            o2[o2.length] = r3 ? x(n2, A, "$1") : e3 || t3;
          }), o2;
        }, S = function(t2, e2) {
          var r2, o2 = t2;
          if (b(g, o2) && (o2 = "%" + (r2 = g[o2])[0] + "%"), b(y, o2)) {
            var a2 = y[o2];
            if (a2 === f && (a2 = function t3(e3) {
              var r3;
              if ("%AsyncFunction%" === e3) r3 = s("async function () {}");
              else if ("%GeneratorFunction%" === e3) r3 = s("function* () {}");
              else if ("%AsyncGeneratorFunction%" === e3) r3 = s("async function* () {}");
              else if ("%AsyncGenerator%" === e3) {
                var n2 = t3("%AsyncGeneratorFunction%");
                n2 && (r3 = n2.prototype);
              } else if ("%AsyncIteratorPrototype%" === e3) {
                var o3 = t3("%AsyncGenerator%");
                o3 && (r3 = l(o3.prototype));
              }
              return y[e3] = r3, r3;
            }(o2)), void 0 === a2 && !e2) throw new i("intrinsic " + t2 + " exists, but is not available. Please file an issue!");
            return { alias: r2, name: o2, value: a2 };
          }
          throw new n("intrinsic " + t2 + " does not exist!");
        };
        t.exports = function(t2, e2) {
          if ("string" != typeof t2 || 0 === t2.length) throw new i("intrinsic name must be a non-empty string");
          if (arguments.length > 1 && "boolean" != typeof e2) throw new i('"allowMissing" argument must be a boolean');
          if (null === j(/^%?[^%]*%?$/g, t2)) throw new n("`%` may not be present anywhere but at the beginning and end of the intrinsic name");
          var r2 = P(t2), o2 = r2.length > 0 ? r2[0] : "", s2 = S("%" + o2 + "%", e2), u2 = s2.name, p2 = s2.value, c2 = false, l2 = s2.alias;
          l2 && (o2 = l2[0], v(r2, m([0, 1], l2)));
          for (var f2 = 1, h2 = true; f2 < r2.length; f2 += 1) {
            var g2 = r2[f2], d2 = w(g2, 0, 1), x2 = w(g2, -1);
            if (('"' === d2 || "'" === d2 || "`" === d2 || '"' === x2 || "'" === x2 || "`" === x2) && d2 !== x2) throw new n("property names with quotes must have matching quotes");
            if ("constructor" !== g2 && h2 || (c2 = true), b(y, u2 = "%" + (o2 += "." + g2) + "%")) p2 = y[u2];
            else if (null != p2) {
              if (!(g2 in p2)) {
                if (!e2) throw new i("base intrinsic for " + t2 + " exists, but the property is not available.");
                return;
              }
              if (a && f2 + 1 >= r2.length) {
                var O2 = a(p2, g2);
                p2 = (h2 = !!O2) && "get" in O2 && !("originalValue" in O2.get) ? O2.get : p2[g2];
              } else h2 = b(p2, g2), p2 = p2[g2];
              h2 && !c2 && (y[u2] = p2);
            }
          }
          return p2;
        };
      }, function(t, e, r) {
        "use strict";
        var n = r(22);
        t.exports = Function.prototype.bind || n;
      }, function(t, e, r) {
        "use strict";
        var n = r(3), o = r(2), i = o("%Function.prototype.apply%"), s = o("%Function.prototype.call%"), a = o("%Reflect.apply%", true) || n.call(s, i), u = o("%Object.getOwnPropertyDescriptor%", true), p = o("%Object.defineProperty%", true), c = o("%Math.max%");
        if (p) try {
          p({}, "a", { value: 1 });
        } catch (t2) {
          p = null;
        }
        t.exports = function(t2) {
          var e2 = a(n, s, arguments);
          if (u && p) {
            var r2 = u(e2, "length");
            r2.configurable && p(e2, "length", { value: 1 + c(0, t2.length - (arguments.length - 1)) });
          }
          return e2;
        };
        var l = function() {
          return a(n, i, arguments);
        };
        p ? p(t.exports, "apply", { value: l }) : t.exports.apply = l;
      }, function(t, e, r) {
        var n = r(18), o = r(6), i = r(15), s = r(32), a = String.fromCharCode(0), u = function(t2) {
          Array.isArray(t2) ? this.ops = t2 : null != t2 && Array.isArray(t2.ops) ? this.ops = t2.ops : this.ops = [];
        };
        u.prototype.insert = function(t2, e2) {
          var r2 = {};
          return 0 === t2.length ? this : (r2.insert = t2, null != e2 && "object" == typeof e2 && Object.keys(e2).length > 0 && (r2.attributes = e2), this.push(r2));
        }, u.prototype.delete = function(t2) {
          return t2 <= 0 ? this : this.push({ delete: t2 });
        }, u.prototype.retain = function(t2, e2) {
          if (t2 <= 0) return this;
          var r2 = { retain: t2 };
          return null != e2 && "object" == typeof e2 && Object.keys(e2).length > 0 && (r2.attributes = e2), this.push(r2);
        }, u.prototype.push = function(t2) {
          var e2 = this.ops.length, r2 = this.ops[e2 - 1];
          if (t2 = i(true, {}, t2), "object" == typeof r2) {
            if ("number" == typeof t2.delete && "number" == typeof r2.delete) return this.ops[e2 - 1] = { delete: r2.delete + t2.delete }, this;
            if ("number" == typeof r2.delete && null != t2.insert && (e2 -= 1, "object" != typeof (r2 = this.ops[e2 - 1]))) return this.ops.unshift(t2), this;
            if (o(t2.attributes, r2.attributes)) {
              if ("string" == typeof t2.insert && "string" == typeof r2.insert) return this.ops[e2 - 1] = { insert: r2.insert + t2.insert }, "object" == typeof t2.attributes && (this.ops[e2 - 1].attributes = t2.attributes), this;
              if ("number" == typeof t2.retain && "number" == typeof r2.retain) return this.ops[e2 - 1] = { retain: r2.retain + t2.retain }, "object" == typeof t2.attributes && (this.ops[e2 - 1].attributes = t2.attributes), this;
            }
          }
          return e2 === this.ops.length ? this.ops.push(t2) : this.ops.splice(e2, 0, t2), this;
        }, u.prototype.chop = function() {
          var t2 = this.ops[this.ops.length - 1];
          return t2 && t2.retain && !t2.attributes && this.ops.pop(), this;
        }, u.prototype.filter = function(t2) {
          return this.ops.filter(t2);
        }, u.prototype.forEach = function(t2) {
          this.ops.forEach(t2);
        }, u.prototype.map = function(t2) {
          return this.ops.map(t2);
        }, u.prototype.partition = function(t2) {
          var e2 = [], r2 = [];
          return this.forEach(function(n2) {
            (t2(n2) ? e2 : r2).push(n2);
          }), [e2, r2];
        }, u.prototype.reduce = function(t2, e2) {
          return this.ops.reduce(t2, e2);
        }, u.prototype.changeLength = function() {
          return this.reduce(function(t2, e2) {
            return e2.insert ? t2 + s.length(e2) : e2.delete ? t2 - e2.delete : t2;
          }, 0);
        }, u.prototype.length = function() {
          return this.reduce(function(t2, e2) {
            return t2 + s.length(e2);
          }, 0);
        }, u.prototype.slice = function(t2, e2) {
          t2 = t2 || 0, "number" != typeof e2 && (e2 = 1 / 0);
          for (var r2 = [], n2 = s.iterator(this.ops), o2 = 0; o2 < e2 && n2.hasNext(); ) {
            var i2;
            o2 < t2 ? i2 = n2.next(t2 - o2) : (i2 = n2.next(e2 - o2), r2.push(i2)), o2 += s.length(i2);
          }
          return new u(r2);
        }, u.prototype.compose = function(t2) {
          var e2 = s.iterator(this.ops), r2 = s.iterator(t2.ops), n2 = [], i2 = r2.peek();
          if (null != i2 && "number" == typeof i2.retain && null == i2.attributes) {
            for (var a2 = i2.retain; "insert" === e2.peekType() && e2.peekLength() <= a2; ) a2 -= e2.peekLength(), n2.push(e2.next());
            i2.retain - a2 > 0 && r2.next(i2.retain - a2);
          }
          for (var p = new u(n2); e2.hasNext() || r2.hasNext(); ) if ("insert" === r2.peekType()) p.push(r2.next());
          else if ("delete" === e2.peekType()) p.push(e2.next());
          else {
            var c = Math.min(e2.peekLength(), r2.peekLength()), l = e2.next(c), f = r2.next(c);
            if ("number" == typeof f.retain) {
              var h = {};
              "number" == typeof l.retain ? h.retain = c : h.insert = l.insert;
              var y = s.attributes.compose(l.attributes, f.attributes, "number" == typeof l.retain);
              if (y && (h.attributes = y), p.push(h), !r2.hasNext() && o(p.ops[p.ops.length - 1], h)) {
                var g = new u(e2.rest());
                return p.concat(g).chop();
              }
            } else "number" == typeof f.delete && "number" == typeof l.retain && p.push(f);
          }
          return p.chop();
        }, u.prototype.concat = function(t2) {
          var e2 = new u(this.ops.slice());
          return t2.ops.length > 0 && (e2.push(t2.ops[0]), e2.ops = e2.ops.concat(t2.ops.slice(1))), e2;
        }, u.prototype.diff = function(t2, e2) {
          if (this.ops === t2.ops) return new u();
          var r2 = [this, t2].map(function(e3) {
            return e3.map(function(r3) {
              if (null != r3.insert) return "string" == typeof r3.insert ? r3.insert : a;
              throw new Error("diff() called " + (e3 === t2 ? "on" : "with") + " non-document");
            }).join("");
          }), i2 = new u(), p = n(r2[0], r2[1], e2), c = s.iterator(this.ops), l = s.iterator(t2.ops);
          return p.forEach(function(t3) {
            for (var e3 = t3[1].length; e3 > 0; ) {
              var r3 = 0;
              switch (t3[0]) {
                case n.INSERT:
                  r3 = Math.min(l.peekLength(), e3), i2.push(l.next(r3));
                  break;
                case n.DELETE:
                  r3 = Math.min(e3, c.peekLength()), c.next(r3), i2.delete(r3);
                  break;
                case n.EQUAL:
                  r3 = Math.min(c.peekLength(), l.peekLength(), e3);
                  var a2 = c.next(r3), u2 = l.next(r3);
                  o(a2.insert, u2.insert) ? i2.retain(r3, s.attributes.diff(a2.attributes, u2.attributes)) : i2.push(u2).delete(r3);
              }
              e3 -= r3;
            }
          }), i2.chop();
        }, u.prototype.eachLine = function(t2, e2) {
          e2 = e2 || "\n";
          for (var r2 = s.iterator(this.ops), n2 = new u(), o2 = 0; r2.hasNext(); ) {
            if ("insert" !== r2.peekType()) return;
            var i2 = r2.peek(), a2 = s.length(i2) - r2.peekLength(), p = "string" == typeof i2.insert ? i2.insert.indexOf(e2, a2) - a2 : -1;
            if (p < 0) n2.push(r2.next());
            else if (p > 0) n2.push(r2.next(p));
            else {
              if (false === t2(n2, r2.next(1).attributes || {}, o2)) return;
              o2 += 1, n2 = new u();
            }
          }
          n2.length() > 0 && t2(n2, {}, o2);
        }, u.prototype.transform = function(t2, e2) {
          if (e2 = !!e2, "number" == typeof t2) return this.transformPosition(t2, e2);
          for (var r2 = s.iterator(this.ops), n2 = s.iterator(t2.ops), o2 = new u(); r2.hasNext() || n2.hasNext(); ) if ("insert" !== r2.peekType() || !e2 && "insert" === n2.peekType()) if ("insert" === n2.peekType()) o2.push(n2.next());
          else {
            var i2 = Math.min(r2.peekLength(), n2.peekLength()), a2 = r2.next(i2), p = n2.next(i2);
            if (a2.delete) continue;
            p.delete ? o2.push(p) : o2.retain(i2, s.attributes.transform(a2.attributes, p.attributes, e2));
          }
          else o2.retain(s.length(r2.next()));
          return o2.chop();
        }, u.prototype.transformPosition = function(t2, e2) {
          e2 = !!e2;
          for (var r2 = s.iterator(this.ops), n2 = 0; r2.hasNext() && n2 <= t2; ) {
            var o2 = r2.peekLength(), i2 = r2.peekType();
            r2.next(), "delete" !== i2 ? ("insert" === i2 && (n2 < t2 || !e2) && (t2 += o2), n2 += o2) : t2 -= Math.min(o2, t2 - n2);
          }
          return t2;
        }, t.exports = u;
      }, function(t, e, r) {
        var n = r(7), o = r(20), i = r(24), s = r(27), a = r(28), u = r(31), p = Date.prototype.getTime;
        function c(t2, e2, r2) {
          var h = r2 || {};
          return !!(h.strict ? i(t2, e2) : t2 === e2) || (!t2 || !e2 || "object" != typeof t2 && "object" != typeof e2 ? h.strict ? i(t2, e2) : t2 == e2 : function(t3, e3, r3) {
            var i2, h2;
            if (typeof t3 != typeof e3) return false;
            if (l(t3) || l(e3)) return false;
            if (t3.prototype !== e3.prototype) return false;
            if (o(t3) !== o(e3)) return false;
            var y = s(t3), g = s(e3);
            if (y !== g) return false;
            if (y || g) return t3.source === e3.source && a(t3) === a(e3);
            if (u(t3) && u(e3)) return p.call(t3) === p.call(e3);
            var d = f(t3), b = f(e3);
            if (d !== b) return false;
            if (d || b) {
              if (t3.length !== e3.length) return false;
              for (i2 = 0; i2 < t3.length; i2++) if (t3[i2] !== e3[i2]) return false;
              return true;
            }
            if (typeof t3 != typeof e3) return false;
            try {
              var m = n(t3), v = n(e3);
            } catch (t4) {
              return false;
            }
            if (m.length !== v.length) return false;
            for (m.sort(), v.sort(), i2 = m.length - 1; i2 >= 0; i2--) if (m[i2] != v[i2]) return false;
            for (i2 = m.length - 1; i2 >= 0; i2--) if (h2 = m[i2], !c(t3[h2], e3[h2], r3)) return false;
            return true;
          }(t2, e2, h));
        }
        function l(t2) {
          return null == t2;
        }
        function f(t2) {
          return !(!t2 || "object" != typeof t2 || "number" != typeof t2.length) && ("function" == typeof t2.copy && "function" == typeof t2.slice && !(t2.length > 0 && "number" != typeof t2[0]));
        }
        t.exports = c;
      }, function(t, e, r) {
        "use strict";
        var n = Array.prototype.slice, o = r(8), i = Object.keys, s = i ? function(t2) {
          return i(t2);
        } : r(19), a = Object.keys;
        s.shim = function() {
          Object.keys ? function() {
            var t2 = Object.keys(arguments);
            return t2 && t2.length === arguments.length;
          }(1, 2) || (Object.keys = function(t2) {
            return o(t2) ? a(n.call(t2)) : a(t2);
          }) : Object.keys = s;
          return Object.keys || s;
        }, t.exports = s;
      }, function(t, e, r) {
        "use strict";
        var n = Object.prototype.toString;
        t.exports = function(t2) {
          var e2 = n.call(t2), r2 = "[object Arguments]" === e2;
          return r2 || (r2 = "[object Array]" !== e2 && null !== t2 && "object" == typeof t2 && "number" == typeof t2.length && t2.length >= 0 && "[object Function]" === n.call(t2.callee)), r2;
        };
      }, function(t, e, r) {
        "use strict";
        t.exports = function() {
          if ("function" != typeof Symbol || "function" != typeof Object.getOwnPropertySymbols) return false;
          if ("symbol" == typeof Symbol.iterator) return true;
          var t2 = {}, e2 = Symbol("test"), r2 = Object(e2);
          if ("string" == typeof e2) return false;
          if ("[object Symbol]" !== Object.prototype.toString.call(e2)) return false;
          if ("[object Symbol]" !== Object.prototype.toString.call(r2)) return false;
          for (e2 in t2[e2] = 42, t2) return false;
          if ("function" == typeof Object.keys && 0 !== Object.keys(t2).length) return false;
          if ("function" == typeof Object.getOwnPropertyNames && 0 !== Object.getOwnPropertyNames(t2).length) return false;
          var n = Object.getOwnPropertySymbols(t2);
          if (1 !== n.length || n[0] !== e2) return false;
          if (!Object.prototype.propertyIsEnumerable.call(t2, e2)) return false;
          if ("function" == typeof Object.getOwnPropertyDescriptor) {
            var o = Object.getOwnPropertyDescriptor(t2, e2);
            if (42 !== o.value || true !== o.enumerable) return false;
          }
          return true;
        };
      }, function(t, e, r) {
        "use strict";
        var n = r(2), o = r(4), i = o(n("String.prototype.indexOf"));
        t.exports = function(t2, e2) {
          var r2 = n(t2, !!e2);
          return "function" == typeof r2 && i(t2, ".prototype.") > -1 ? o(r2) : r2;
        };
      }, function(t, e, r) {
        "use strict";
        var n = function(t2) {
          return t2 != t2;
        };
        t.exports = function(t2, e2) {
          return 0 === t2 && 0 === e2 ? 1 / t2 == 1 / e2 : t2 === e2 || !(!n(t2) || !n(e2));
        };
      }, function(t, e, r) {
        "use strict";
        var n = r(11);
        t.exports = function() {
          return "function" == typeof Object.is ? Object.is : n;
        };
      }, function(t, e, r) {
        "use strict";
        var n = r(29).functionsHaveConfigurableNames(), o = Object, i = TypeError;
        t.exports = function() {
          if (null != this && this !== o(this)) throw new i("RegExp.prototype.flags getter called on non-object");
          var t2 = "";
          return this.hasIndices && (t2 += "d"), this.global && (t2 += "g"), this.ignoreCase && (t2 += "i"), this.multiline && (t2 += "m"), this.dotAll && (t2 += "s"), this.unicode && (t2 += "u"), this.sticky && (t2 += "y"), t2;
        }, n && Object.defineProperty && Object.defineProperty(t.exports, "name", { value: "get flags" });
      }, function(t, e, r) {
        "use strict";
        var n = r(13), o = r(0).supportsDescriptors, i = Object.getOwnPropertyDescriptor;
        t.exports = function() {
          if (o && "gim" === /a/gim.flags) {
            var t2 = i(RegExp.prototype, "flags");
            if (t2 && "function" == typeof t2.get && "boolean" == typeof RegExp.prototype.dotAll && "boolean" == typeof RegExp.prototype.hasIndices) {
              var e2 = "", r2 = {};
              if (Object.defineProperty(r2, "hasIndices", { get: function() {
                e2 += "d";
              } }), Object.defineProperty(r2, "sticky", { get: function() {
                e2 += "y";
              } }), "dy" === e2) return t2.get;
            }
          }
          return n;
        };
      }, function(t, e, r) {
        "use strict";
        var n = Object.prototype.hasOwnProperty, o = Object.prototype.toString, i = Object.defineProperty, s = Object.getOwnPropertyDescriptor, a = function(t2) {
          return "function" == typeof Array.isArray ? Array.isArray(t2) : "[object Array]" === o.call(t2);
        }, u = function(t2) {
          if (!t2 || "[object Object]" !== o.call(t2)) return false;
          var e2, r2 = n.call(t2, "constructor"), i2 = t2.constructor && t2.constructor.prototype && n.call(t2.constructor.prototype, "isPrototypeOf");
          if (t2.constructor && !r2 && !i2) return false;
          for (e2 in t2) ;
          return void 0 === e2 || n.call(t2, e2);
        }, p = function(t2, e2) {
          i && "__proto__" === e2.name ? i(t2, e2.name, { enumerable: true, configurable: true, value: e2.newValue, writable: true }) : t2[e2.name] = e2.newValue;
        }, c = function(t2, e2) {
          if ("__proto__" === e2) {
            if (!n.call(t2, e2)) return;
            if (s) return s(t2, e2).value;
          }
          return t2[e2];
        };
        t.exports = function t2() {
          var e2, r2, n2, o2, i2, s2, l = arguments[0], f = 1, h = arguments.length, y = false;
          for ("boolean" == typeof l && (y = l, l = arguments[1] || {}, f = 2), (null == l || "object" != typeof l && "function" != typeof l) && (l = {}); f < h; ++f) if (null != (e2 = arguments[f])) for (r2 in e2) n2 = c(l, r2), l !== (o2 = c(e2, r2)) && (y && o2 && (u(o2) || (i2 = a(o2))) ? (i2 ? (i2 = false, s2 = n2 && a(n2) ? n2 : []) : s2 = n2 && u(n2) ? n2 : {}, p(l, { name: r2, newValue: t2(y, s2, o2) })) : void 0 !== o2 && p(l, { name: r2, newValue: o2 }));
          return l;
        };
      }, function(t, e, r) {
        "use strict";
        const n = "undefined" == typeof URL ? r(33).URL : URL, o = (t2, e2) => e2.some((e3) => e3 instanceof RegExp ? e3.test(t2) : e3 === t2), i = (t2, e2) => {
          if (e2 = { defaultProtocol: "http:", normalizeProtocol: true, forceHttp: false, forceHttps: false, stripAuthentication: true, stripHash: false, stripWWW: true, removeQueryParameters: [/^utm_\w+/i], removeTrailingSlash: true, removeDirectoryIndex: false, sortQueryParameters: true, ...e2 }, Reflect.has(e2, "normalizeHttps")) throw new Error("options.normalizeHttps is renamed to options.forceHttp");
          if (Reflect.has(e2, "normalizeHttp")) throw new Error("options.normalizeHttp is renamed to options.forceHttps");
          if (Reflect.has(e2, "stripFragment")) throw new Error("options.stripFragment is renamed to options.stripHash");
          if (t2 = t2.trim(), /^data:/i.test(t2)) return ((t3, { stripHash: e3 }) => {
            const r3 = t3.match(/^data:([^,]*?),([^#]*?)(?:#(.*))?$/);
            if (!r3) throw new Error("Invalid URL: " + t3);
            const n2 = r3[1].split(";"), o2 = r3[2], i3 = e3 ? "" : r3[3];
            let s = false;
            "base64" === n2[n2.length - 1] && (n2.pop(), s = true);
            const a = (n2.shift() || "").toLowerCase(), u = [...n2.map((t4) => {
              let [e4, r4 = ""] = t4.split("=").map((t5) => t5.trim());
              return "charset" === e4 && (r4 = r4.toLowerCase(), "us-ascii" === r4) ? "" : `${e4}${r4 ? "=" + r4 : ""}`;
            }).filter(Boolean)];
            return s && u.push("base64"), (0 !== u.length || a && "text/plain" !== a) && u.unshift(a), `data:${u.join(";")},${s ? o2.trim() : o2}${i3 ? "#" + i3 : ""}`;
          })(t2, e2);
          const r2 = t2.startsWith("//");
          !r2 && /^\.*\//.test(t2) || (t2 = t2.replace(/^(?!(?:\w+:)?\/\/)|^\/\//, e2.defaultProtocol));
          const i2 = new n(t2);
          if (e2.forceHttp && e2.forceHttps) throw new Error("The `forceHttp` and `forceHttps` options cannot be used together");
          if (e2.forceHttp && "https:" === i2.protocol && (i2.protocol = "http:"), e2.forceHttps && "http:" === i2.protocol && (i2.protocol = "https:"), e2.stripAuthentication && (i2.username = "", i2.password = ""), e2.stripHash && (i2.hash = ""), i2.pathname && (i2.pathname = i2.pathname.replace(/((?!:).|^)\/{2,}/g, (t3, e3) => /^(?!\/)/g.test(e3) ? e3 + "/" : "/")), i2.pathname && (i2.pathname = decodeURI(i2.pathname)), true === e2.removeDirectoryIndex && (e2.removeDirectoryIndex = [/^index\.[a-z]+$/]), Array.isArray(e2.removeDirectoryIndex) && e2.removeDirectoryIndex.length > 0) {
            let t3 = i2.pathname.split("/");
            const r3 = t3[t3.length - 1];
            o(r3, e2.removeDirectoryIndex) && (t3 = t3.slice(0, t3.length - 1), i2.pathname = t3.slice(1).join("/") + "/");
          }
          if (i2.hostname && (i2.hostname = i2.hostname.replace(/\.$/, ""), e2.stripWWW && /^www\.([a-z\-\d]{2,63})\.([a-z.]{2,5})$/.test(i2.hostname) && (i2.hostname = i2.hostname.replace(/^www\./, ""))), Array.isArray(e2.removeQueryParameters)) for (const t3 of [...i2.searchParams.keys()]) o(t3, e2.removeQueryParameters) && i2.searchParams.delete(t3);
          return e2.sortQueryParameters && i2.searchParams.sort(), e2.removeTrailingSlash && (i2.pathname = i2.pathname.replace(/\/$/, "")), t2 = i2.toString(), !e2.removeTrailingSlash && "/" !== i2.pathname || "" !== i2.hash || (t2 = t2.replace(/\/$/, "")), r2 && !e2.normalizeProtocol && (t2 = t2.replace(/^http:\/\//, "//")), e2.stripProtocol && (t2 = t2.replace(/^(?:https?:)?\/\//, "")), t2;
        };
        t.exports = i, t.exports.default = i;
      }, function(t, e, r) {
        "use strict";
        r.r(e), r.d(e, "default", function() {
          return y;
        });
        var n = r(5), o = r.n(n), i = r(16), s = r.n(i);
        function a(t2, e2) {
          return function(t3) {
            if (Array.isArray(t3)) return t3;
          }(t2) || function(t3, e3) {
            var r2 = null == t3 ? null : "undefined" != typeof Symbol && t3[Symbol.iterator] || t3["@@iterator"];
            if (null == r2) return;
            var n2, o2, i2 = [], s2 = true, a2 = false;
            try {
              for (r2 = r2.call(t3); !(s2 = (n2 = r2.next()).done) && (i2.push(n2.value), !e3 || i2.length !== e3); s2 = true) ;
            } catch (t4) {
              a2 = true, o2 = t4;
            } finally {
              try {
                s2 || null == r2.return || r2.return();
              } finally {
                if (a2) throw o2;
              }
            }
            return i2;
          }(t2, e2) || function(t3, e3) {
            if (!t3) return;
            if ("string" == typeof t3) return u(t3, e3);
            var r2 = Object.prototype.toString.call(t3).slice(8, -1);
            "Object" === r2 && t3.constructor && (r2 = t3.constructor.name);
            if ("Map" === r2 || "Set" === r2) return Array.from(t3);
            if ("Arguments" === r2 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r2)) return u(t3, e3);
          }(t2, e2) || function() {
            throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
          }();
        }
        function u(t2, e2) {
          (null == e2 || e2 > t2.length) && (e2 = t2.length);
          for (var r2 = 0, n2 = new Array(e2); r2 < e2; r2++) n2[r2] = t2[r2];
          return n2;
        }
        function p(t2, e2) {
          var r2 = Object.keys(t2);
          if (Object.getOwnPropertySymbols) {
            var n2 = Object.getOwnPropertySymbols(t2);
            e2 && (n2 = n2.filter(function(e3) {
              return Object.getOwnPropertyDescriptor(t2, e3).enumerable;
            })), r2.push.apply(r2, n2);
          }
          return r2;
        }
        function c(t2) {
          for (var e2 = 1; e2 < arguments.length; e2++) {
            var r2 = null != arguments[e2] ? arguments[e2] : {};
            e2 % 2 ? p(Object(r2), true).forEach(function(e3) {
              l(t2, e3, r2[e3]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t2, Object.getOwnPropertyDescriptors(r2)) : p(Object(r2)).forEach(function(e3) {
              Object.defineProperty(t2, e3, Object.getOwnPropertyDescriptor(r2, e3));
            });
          }
          return t2;
        }
        function l(t2, e2, r2) {
          return e2 in t2 ? Object.defineProperty(t2, e2, { value: r2, enumerable: true, configurable: true, writable: true }) : t2[e2] = r2, t2;
        }
        function f(t2, e2) {
          for (var r2 = 0; r2 < e2.length; r2++) {
            var n2 = e2[r2];
            n2.enumerable = n2.enumerable || false, n2.configurable = true, "value" in n2 && (n2.writable = true), Object.defineProperty(t2, n2.key, n2);
          }
        }
        var h = { globalRegularExpression: /(https?:\/\/|www\.)[\w-\.]+\.[\w-\.]+(\/([\S]+)?)?/gi, urlRegularExpression: /(https?:\/\/|www\.)[\w-\.]+\.[\w-\.]+(\/([\S]+)?)?/gi, globalMailRegularExpression: /([\w-\.]+@[\w-\.]+\.[\w-\.]+)/gi, mailRegularExpression: /([\w-\.]+@[\w-\.]+\.[\w-\.]+)/gi, normalizeRegularExpression: /(https?:\/\/|www\.)[\S]+/i, normalizeUrlOptions: { stripWWW: false } }, y = function() {
          function t2(e3, r3) {
            var n3 = this;
            !function(t3, e4) {
              if (!(t3 instanceof e4)) throw new TypeError("Cannot call a class as a function");
            }(this, t2), this.quill = e3, r3 = r3 || {}, this.options = c(c({}, h), r3), this.urlNormalizer = function(t3) {
              return n3.normalize(t3);
            }, this.mailNormalizer = function(t3) {
              return "mailto:".concat(t3);
            }, this.registerTypeListener(), this.registerPasteListener(), this.registerBlurListener();
          }
          var e2, r2, n2;
          return e2 = t2, (r2 = [{ key: "registerPasteListener", value: function() {
            var t3 = this;
            this.quill.clipboard.addMatcher("A", function(t4, e3) {
              var r3, n3 = t4.getAttribute("href"), o2 = null === (r3 = e3.ops[0]) || void 0 === r3 ? void 0 : r3.attributes;
              return null != (null == o2 ? void 0 : o2.link) && (o2.link = n3), e3;
            }), this.quill.clipboard.addMatcher(Node.TEXT_NODE, function(e3, r3) {
              if ("string" == typeof e3.data) {
                var n3 = t3.options.globalRegularExpression, i2 = t3.options.globalMailRegularExpression;
                n3.lastIndex = 0, i2.lastIndex = 0;
                for (var s2 = new o.a(), a2 = 0, u2 = n3.exec(e3.data), p2 = i2.exec(e3.data), c2 = function(t4, r4, n4) {
                  var o2 = e3.data.substring(a2, t4.index);
                  s2.insert(o2);
                  var i3 = t4[0];
                  return s2.insert(i3, { link: n4(i3) }), a2 = r4.lastIndex, r4.exec(e3.data);
                }; null !== u2 || null !== p2; ) if (null === u2) p2 = c2(p2, i2, t3.mailNormalizer);
                else if (null === p2) u2 = c2(u2, n3, t3.urlNormalizer);
                else if (p2.index <= u2.index) {
                  for (; null !== u2 && u2.index < i2.lastIndex; ) u2 = n3.exec(e3.data);
                  p2 = c2(p2, i2, t3.mailNormalizer);
                } else {
                  for (; null !== p2 && p2.index < n3.lastIndex; ) p2 = i2.exec(e3.data);
                  u2 = c2(u2, n3, t3.urlNormalizer);
                }
                if (a2 > 0) {
                  var l2 = e3.data.substring(a2);
                  s2.insert(l2), r3.ops = s2.ops;
                }
                return r3;
              }
            });
          } }, { key: "registerTypeListener", value: function() {
            var t3 = this;
            this.quill.on("text-change", function(e3) {
              var r3 = e3.ops;
              if (!(!r3 || r3.length < 1 || r3.length > 2)) {
                var n3 = r3[r3.length - 1];
                n3.insert && "string" == typeof n3.insert && n3.insert.match(/\s/) && t3.checkTextForUrl(!!n3.insert.match(/ |\t/));
              }
            });
          } }, { key: "registerBlurListener", value: function() {
            var t3 = this;
            this.quill.root.addEventListener("blur", function() {
              t3.checkTextForUrl();
            });
          } }, { key: "checkTextForUrl", value: function() {
            var t3 = arguments.length > 0 && void 0 !== arguments[0] && arguments[0], e3 = this.quill.getSelection();
            if (e3) {
              var r3 = this.quill.getLeaf(e3.index), n3 = a(r3, 1), o2 = n3[0], i2 = this.quill.getIndex(o2);
              if (o2.text) {
                var s2 = e3.index - i2, u2 = o2.text.slice(0, s2);
                if (u2 && "a" !== o2.parent.domNode.localName) {
                  var p2 = o2.text[s2];
                  if (null == p2 || !p2.match(/\S/)) {
                    var c2 = t3 ? /\s\s$/ : /\s$/;
                    if (!u2.match(c2)) {
                      var l2 = u2.match(this.options.urlRegularExpression), f2 = u2.match(this.options.mailRegularExpression);
                      l2 ? this.handleMatches(i2, u2, l2, this.urlNormalizer) : f2 && this.handleMatches(i2, u2, f2, this.mailNormalizer);
                    }
                  }
                }
              }
            }
          } }, { key: "handleMatches", value: function(t3, e3, r3, n3) {
            var o2 = r3.pop(), i2 = e3.lastIndexOf(o2);
            e3.split(o2).pop().match(/\S/) || this.updateText(t3 + i2, o2.trim(), n3);
          } }, { key: "updateText", value: function(t3, e3, r3) {
            var n3 = new o.a().retain(t3).retain(e3.length, { link: r3(e3) });
            this.quill.updateContents(n3);
          } }, { key: "normalize", value: function(t3) {
            if (this.options.normalizeRegularExpression.test(t3)) try {
              return s()(t3, this.options.normalizeUrlOptions);
            } catch (t4) {
              console.error(t4);
            }
            return t3;
          } }]) && f(e2.prototype, r2), n2 && f(e2, n2), Object.defineProperty(e2, "prototype", { writable: false }), t2;
        }();
        null != window && window.Quill && window.Quill.register("modules/magicUrl", y);
      }, function(t, e) {
        function r(t2, e2, s2) {
          if (t2 == e2) return t2 ? [[0, t2]] : [];
          (s2 < 0 || t2.length < s2) && (s2 = null);
          var u = o(t2, e2), p = t2.substring(0, u);
          u = i(t2 = t2.substring(u), e2 = e2.substring(u));
          var c = t2.substring(t2.length - u), l = function(t3, e3) {
            var s3;
            if (!t3) return [[1, e3]];
            if (!e3) return [[-1, t3]];
            var a2 = t3.length > e3.length ? t3 : e3, u2 = t3.length > e3.length ? e3 : t3, p2 = a2.indexOf(u2);
            if (-1 != p2) return s3 = [[1, a2.substring(0, p2)], [0, u2], [1, a2.substring(p2 + u2.length)]], t3.length > e3.length && (s3[0][0] = s3[2][0] = -1), s3;
            if (1 == u2.length) return [[-1, t3], [1, e3]];
            var c2 = function(t4, e4) {
              var r2 = t4.length > e4.length ? t4 : e4, n2 = t4.length > e4.length ? e4 : t4;
              if (r2.length < 4 || 2 * n2.length < r2.length) return null;
              function s4(t5, e5, r3) {
                for (var n3, s5, a4, u4, p4 = t5.substring(r3, r3 + Math.floor(t5.length / 4)), c4 = -1, l4 = ""; -1 != (c4 = e5.indexOf(p4, c4 + 1)); ) {
                  var f3 = o(t5.substring(r3), e5.substring(c4)), h3 = i(t5.substring(0, r3), e5.substring(0, c4));
                  l4.length < h3 + f3 && (l4 = e5.substring(c4 - h3, c4) + e5.substring(c4, c4 + f3), n3 = t5.substring(0, r3 - h3), s5 = t5.substring(r3 + f3), a4 = e5.substring(0, c4 - h3), u4 = e5.substring(c4 + f3));
                }
                return 2 * l4.length >= t5.length ? [n3, s5, a4, u4, l4] : null;
              }
              var a3, u3, p3, c3, l3, f2 = s4(r2, n2, Math.ceil(r2.length / 4)), h2 = s4(r2, n2, Math.ceil(r2.length / 2));
              if (!f2 && !h2) return null;
              a3 = h2 ? f2 && f2[4].length > h2[4].length ? f2 : h2 : f2;
              t4.length > e4.length ? (u3 = a3[0], p3 = a3[1], c3 = a3[2], l3 = a3[3]) : (c3 = a3[0], l3 = a3[1], u3 = a3[2], p3 = a3[3]);
              var y2 = a3[4];
              return [u3, p3, c3, l3, y2];
            }(t3, e3);
            if (c2) {
              var l2 = c2[0], f = c2[1], h = c2[2], y = c2[3], g = c2[4], d = r(l2, h), b = r(f, y);
              return d.concat([[0, g]], b);
            }
            return function(t4, e4) {
              for (var r2 = t4.length, o2 = e4.length, i2 = Math.ceil((r2 + o2) / 2), s4 = i2, a3 = 2 * i2, u3 = new Array(a3), p3 = new Array(a3), c3 = 0; c3 < a3; c3++) u3[c3] = -1, p3[c3] = -1;
              u3[s4 + 1] = 0, p3[s4 + 1] = 0;
              for (var l3 = r2 - o2, f2 = l3 % 2 != 0, h2 = 0, y2 = 0, g2 = 0, d2 = 0, b2 = 0; b2 < i2; b2++) {
                for (var m = -b2 + h2; m <= b2 - y2; m += 2) {
                  for (var v = s4 + m, x = (P = m == -b2 || m != b2 && u3[v - 1] < u3[v + 1] ? u3[v + 1] : u3[v - 1] + 1) - m; P < r2 && x < o2 && t4.charAt(P) == e4.charAt(x); ) P++, x++;
                  if (u3[v] = P, P > r2) y2 += 2;
                  else if (x > o2) h2 += 2;
                  else if (f2) {
                    if ((O = s4 + l3 - m) >= 0 && O < a3 && -1 != p3[O]) {
                      var w = r2 - p3[O];
                      if (P >= w) return n(t4, e4, P, x);
                    }
                  }
                }
                for (var j = -b2 + g2; j <= b2 - d2; j += 2) {
                  for (var O = s4 + j, A = (w = j == -b2 || j != b2 && p3[O - 1] < p3[O + 1] ? p3[O + 1] : p3[O - 1] + 1) - j; w < r2 && A < o2 && t4.charAt(r2 - w - 1) == e4.charAt(o2 - A - 1); ) w++, A++;
                  if (p3[O] = w, w > r2) d2 += 2;
                  else if (A > o2) g2 += 2;
                  else if (!f2) {
                    if ((v = s4 + l3 - j) >= 0 && v < a3 && -1 != u3[v]) {
                      var P = u3[v];
                      x = s4 + P - v;
                      if (P >= (w = r2 - w)) return n(t4, e4, P, x);
                    }
                  }
                }
              }
              return [[-1, t4], [1, e4]];
            }(t3, e3);
          }(t2 = t2.substring(0, t2.length - u), e2 = e2.substring(0, e2.length - u));
          return p && l.unshift([0, p]), c && l.push([0, c]), function t3(e3) {
            e3.push([0, ""]);
            var r2, n2 = 0, s3 = 0, a2 = 0, u2 = "", p2 = "";
            for (; n2 < e3.length; ) switch (e3[n2][0]) {
              case 1:
                a2++, p2 += e3[n2][1], n2++;
                break;
              case -1:
                s3++, u2 += e3[n2][1], n2++;
                break;
              case 0:
                s3 + a2 > 1 ? (0 !== s3 && 0 !== a2 && (0 !== (r2 = o(p2, u2)) && (n2 - s3 - a2 > 0 && 0 == e3[n2 - s3 - a2 - 1][0] ? e3[n2 - s3 - a2 - 1][1] += p2.substring(0, r2) : (e3.splice(0, 0, [0, p2.substring(0, r2)]), n2++), p2 = p2.substring(r2), u2 = u2.substring(r2)), 0 !== (r2 = i(p2, u2)) && (e3[n2][1] = p2.substring(p2.length - r2) + e3[n2][1], p2 = p2.substring(0, p2.length - r2), u2 = u2.substring(0, u2.length - r2))), 0 === s3 ? e3.splice(n2 - a2, s3 + a2, [1, p2]) : 0 === a2 ? e3.splice(n2 - s3, s3 + a2, [-1, u2]) : e3.splice(n2 - s3 - a2, s3 + a2, [-1, u2], [1, p2]), n2 = n2 - s3 - a2 + (s3 ? 1 : 0) + (a2 ? 1 : 0) + 1) : 0 !== n2 && 0 == e3[n2 - 1][0] ? (e3[n2 - 1][1] += e3[n2][1], e3.splice(n2, 1)) : n2++, a2 = 0, s3 = 0, u2 = "", p2 = "";
            }
            "" === e3[e3.length - 1][1] && e3.pop();
            var c2 = false;
            n2 = 1;
            for (; n2 < e3.length - 1; ) 0 == e3[n2 - 1][0] && 0 == e3[n2 + 1][0] && (e3[n2][1].substring(e3[n2][1].length - e3[n2 - 1][1].length) == e3[n2 - 1][1] ? (e3[n2][1] = e3[n2 - 1][1] + e3[n2][1].substring(0, e3[n2][1].length - e3[n2 - 1][1].length), e3[n2 + 1][1] = e3[n2 - 1][1] + e3[n2 + 1][1], e3.splice(n2 - 1, 1), c2 = true) : e3[n2][1].substring(0, e3[n2 + 1][1].length) == e3[n2 + 1][1] && (e3[n2 - 1][1] += e3[n2 + 1][1], e3[n2][1] = e3[n2][1].substring(e3[n2 + 1][1].length) + e3[n2 + 1][1], e3.splice(n2 + 1, 1), c2 = true)), n2++;
            c2 && t3(e3);
          }(l), null != s2 && (l = function(t3, e3) {
            var r2 = function(t4, e4) {
              if (0 === e4) return [0, t4];
              for (var r3 = 0, n3 = 0; n3 < t4.length; n3++) {
                var o3 = t4[n3];
                if (-1 === o3[0] || 0 === o3[0]) {
                  var i3 = r3 + o3[1].length;
                  if (e4 === i3) return [n3 + 1, t4];
                  if (e4 < i3) {
                    t4 = t4.slice();
                    var s4 = e4 - r3, a2 = [o3[0], o3[1].slice(0, s4)], u3 = [o3[0], o3[1].slice(s4)];
                    return t4.splice(n3, 1, a2, u3), [n3 + 1, t4];
                  }
                  r3 = i3;
                }
              }
              throw new Error("cursor_pos is out of bounds!");
            }(t3, e3), n2 = r2[1], o2 = r2[0], i2 = n2[o2], s3 = n2[o2 + 1];
            if (null == i2) return t3;
            if (0 !== i2[0]) return t3;
            if (null != s3 && i2[1] + s3[1] === s3[1] + i2[1]) return n2.splice(o2, 2, s3, i2), a(n2, o2, 2);
            if (null != s3 && 0 === s3[1].indexOf(i2[1])) {
              n2.splice(o2, 2, [s3[0], i2[1]], [0, i2[1]]);
              var u2 = s3[1].slice(i2[1].length);
              return u2.length > 0 && n2.splice(o2 + 2, 0, [s3[0], u2]), a(n2, o2, 3);
            }
            return t3;
          }(l, s2)), l = function(t3) {
            for (var e3 = false, r2 = function(t4) {
              return t4.charCodeAt(0) >= 56320 && t4.charCodeAt(0) <= 57343;
            }, n2 = 2; n2 < t3.length; n2 += 1) 0 === t3[n2 - 2][0] && ((o2 = t3[n2 - 2][1]).charCodeAt(o2.length - 1) >= 55296 && o2.charCodeAt(o2.length - 1) <= 56319) && -1 === t3[n2 - 1][0] && r2(t3[n2 - 1][1]) && 1 === t3[n2][0] && r2(t3[n2][1]) && (e3 = true, t3[n2 - 1][1] = t3[n2 - 2][1].slice(-1) + t3[n2 - 1][1], t3[n2][1] = t3[n2 - 2][1].slice(-1) + t3[n2][1], t3[n2 - 2][1] = t3[n2 - 2][1].slice(0, -1));
            var o2;
            if (!e3) return t3;
            var i2 = [];
            for (n2 = 0; n2 < t3.length; n2 += 1) t3[n2][1].length > 0 && i2.push(t3[n2]);
            return i2;
          }(l);
        }
        function n(t2, e2, n2, o2) {
          var i2 = t2.substring(0, n2), s2 = e2.substring(0, o2), a2 = t2.substring(n2), u = e2.substring(o2), p = r(i2, s2), c = r(a2, u);
          return p.concat(c);
        }
        function o(t2, e2) {
          if (!t2 || !e2 || t2.charAt(0) != e2.charAt(0)) return 0;
          for (var r2 = 0, n2 = Math.min(t2.length, e2.length), o2 = n2, i2 = 0; r2 < o2; ) t2.substring(i2, o2) == e2.substring(i2, o2) ? i2 = r2 = o2 : n2 = o2, o2 = Math.floor((n2 - r2) / 2 + r2);
          return o2;
        }
        function i(t2, e2) {
          if (!t2 || !e2 || t2.charAt(t2.length - 1) != e2.charAt(e2.length - 1)) return 0;
          for (var r2 = 0, n2 = Math.min(t2.length, e2.length), o2 = n2, i2 = 0; r2 < o2; ) t2.substring(t2.length - o2, t2.length - i2) == e2.substring(e2.length - o2, e2.length - i2) ? i2 = r2 = o2 : n2 = o2, o2 = Math.floor((n2 - r2) / 2 + r2);
          return o2;
        }
        var s = r;
        function a(t2, e2, r2) {
          for (var n2 = e2 + r2 - 1; n2 >= 0 && n2 >= e2 - 1; n2--) if (n2 + 1 < t2.length) {
            var o2 = t2[n2], i2 = t2[n2 + 1];
            o2[0] === i2[1] && t2.splice(n2, 2, [o2[0], o2[1] + i2[1]]);
          }
          return t2;
        }
        s.INSERT = 1, s.DELETE = -1, s.EQUAL = 0, t.exports = s;
      }, function(t, e, r) {
        "use strict";
        var n;
        if (!Object.keys) {
          var o = Object.prototype.hasOwnProperty, i = Object.prototype.toString, s = r(8), a = Object.prototype.propertyIsEnumerable, u = !a.call({ toString: null }, "toString"), p = a.call(function() {
          }, "prototype"), c = ["toString", "toLocaleString", "valueOf", "hasOwnProperty", "isPrototypeOf", "propertyIsEnumerable", "constructor"], l = function(t2) {
            var e2 = t2.constructor;
            return e2 && e2.prototype === t2;
          }, f = { $applicationCache: true, $console: true, $external: true, $frame: true, $frameElement: true, $frames: true, $innerHeight: true, $innerWidth: true, $onmozfullscreenchange: true, $onmozfullscreenerror: true, $outerHeight: true, $outerWidth: true, $pageXOffset: true, $pageYOffset: true, $parent: true, $scrollLeft: true, $scrollTop: true, $scrollX: true, $scrollY: true, $self: true, $webkitIndexedDB: true, $webkitStorageInfo: true, $window: true }, h = function() {
            if ("undefined" == typeof window) return false;
            for (var t2 in window) try {
              if (!f["$" + t2] && o.call(window, t2) && null !== window[t2] && "object" == typeof window[t2]) try {
                l(window[t2]);
              } catch (t3) {
                return true;
              }
            } catch (t3) {
              return true;
            }
            return false;
          }();
          n = function(t2) {
            var e2 = null !== t2 && "object" == typeof t2, r2 = "[object Function]" === i.call(t2), n2 = s(t2), a2 = e2 && "[object String]" === i.call(t2), f2 = [];
            if (!e2 && !r2 && !n2) throw new TypeError("Object.keys called on a non-object");
            var y = p && r2;
            if (a2 && t2.length > 0 && !o.call(t2, 0)) for (var g = 0; g < t2.length; ++g) f2.push(String(g));
            if (n2 && t2.length > 0) for (var d = 0; d < t2.length; ++d) f2.push(String(d));
            else for (var b in t2) y && "prototype" === b || !o.call(t2, b) || f2.push(String(b));
            if (u) for (var m = function(t3) {
              if ("undefined" == typeof window || !h) return l(t3);
              try {
                return l(t3);
              } catch (t4) {
                return false;
              }
            }(t2), v = 0; v < c.length; ++v) m && "constructor" === c[v] || !o.call(t2, c[v]) || f2.push(c[v]);
            return f2;
          };
        }
        t.exports = n;
      }, function(t, e, r) {
        "use strict";
        var n = r(1)(), o = r(10)("Object.prototype.toString"), i = function(t2) {
          return !(n && t2 && "object" == typeof t2 && Symbol.toStringTag in t2) && "[object Arguments]" === o(t2);
        }, s = function(t2) {
          return !!i(t2) || null !== t2 && "object" == typeof t2 && "number" == typeof t2.length && t2.length >= 0 && "[object Array]" !== o(t2) && "[object Function]" === o(t2.callee);
        }, a = function() {
          return i(arguments);
        }();
        i.isLegacyArguments = s, t.exports = a ? i : s;
      }, function(t, e, r) {
        "use strict";
        var n = "undefined" != typeof Symbol && Symbol, o = r(9);
        t.exports = function() {
          return "function" == typeof n && ("function" == typeof Symbol && ("symbol" == typeof n("foo") && ("symbol" == typeof Symbol("bar") && o())));
        };
      }, function(t, e, r) {
        "use strict";
        var n = "Function.prototype.bind called on incompatible ", o = Array.prototype.slice, i = Object.prototype.toString;
        t.exports = function(t2) {
          var e2 = this;
          if ("function" != typeof e2 || "[object Function]" !== i.call(e2)) throw new TypeError(n + e2);
          for (var r2, s = o.call(arguments, 1), a = function() {
            if (this instanceof r2) {
              var n2 = e2.apply(this, s.concat(o.call(arguments)));
              return Object(n2) === n2 ? n2 : this;
            }
            return e2.apply(t2, s.concat(o.call(arguments)));
          }, u = Math.max(0, e2.length - s.length), p = [], c = 0; c < u; c++) p.push("$" + c);
          if (r2 = Function("binder", "return function (" + p.join(",") + "){ return binder.apply(this,arguments); }")(a), e2.prototype) {
            var l = function() {
            };
            l.prototype = e2.prototype, r2.prototype = new l(), l.prototype = null;
          }
          return r2;
        };
      }, function(t, e, r) {
        "use strict";
        var n = r(3);
        t.exports = n.call(Function.call, Object.prototype.hasOwnProperty);
      }, function(t, e, r) {
        "use strict";
        var n = r(0), o = r(4), i = r(11), s = r(12), a = r(26), u = o(s(), Object);
        n(u, { getPolyfill: s, implementation: i, shim: a }), t.exports = u;
      }, function(t, e, r) {
        "use strict";
        var n = r(2)("%Object.defineProperty%", true), o = function() {
          if (n) try {
            return n({}, "a", { value: 1 }), true;
          } catch (t2) {
            return false;
          }
          return false;
        };
        o.hasArrayLengthDefineBug = function() {
          if (!o()) return null;
          try {
            return 1 !== n([], "length", { value: 1 }).length;
          } catch (t2) {
            return true;
          }
        }, t.exports = o;
      }, function(t, e, r) {
        "use strict";
        var n = r(12), o = r(0);
        t.exports = function() {
          var t2 = n();
          return o(Object, { is: t2 }, { is: function() {
            return Object.is !== t2;
          } }), t2;
        };
      }, function(t, e, r) {
        "use strict";
        var n, o, i, s, a = r(10), u = r(1)();
        if (u) {
          n = a("Object.prototype.hasOwnProperty"), o = a("RegExp.prototype.exec"), i = {};
          var p = function() {
            throw i;
          };
          s = { toString: p, valueOf: p }, "symbol" == typeof Symbol.toPrimitive && (s[Symbol.toPrimitive] = p);
        }
        var c = a("Object.prototype.toString"), l = Object.getOwnPropertyDescriptor;
        t.exports = u ? function(t2) {
          if (!t2 || "object" != typeof t2) return false;
          var e2 = l(t2, "lastIndex");
          if (!(e2 && n(e2, "value"))) return false;
          try {
            o(t2, s);
          } catch (t3) {
            return t3 === i;
          }
        } : function(t2) {
          return !(!t2 || "object" != typeof t2 && "function" != typeof t2) && "[object RegExp]" === c(t2);
        };
      }, function(t, e, r) {
        "use strict";
        var n = r(0), o = r(4), i = r(13), s = r(14), a = r(30), u = o(s());
        n(u, { getPolyfill: s, implementation: i, shim: a }), t.exports = u;
      }, function(t, e, r) {
        "use strict";
        var n = function() {
          return "string" == typeof function() {
          }.name;
        }, o = Object.getOwnPropertyDescriptor;
        if (o) try {
          o([], "length");
        } catch (t2) {
          o = null;
        }
        n.functionsHaveConfigurableNames = function() {
          if (!n() || !o) return false;
          var t2 = o(function() {
          }, "name");
          return !!t2 && !!t2.configurable;
        };
        var i = Function.prototype.bind;
        n.boundFunctionsHaveNames = function() {
          return n() && "function" == typeof i && "" !== function() {
          }.bind().name;
        }, t.exports = n;
      }, function(t, e, r) {
        "use strict";
        var n = r(0).supportsDescriptors, o = r(14), i = Object.getOwnPropertyDescriptor, s = Object.defineProperty, a = TypeError, u = Object.getPrototypeOf, p = /a/;
        t.exports = function() {
          if (!n || !u) throw new a("RegExp.prototype.flags requires a true ES5 environment that supports property descriptors");
          var t2 = o(), e2 = u(p), r2 = i(e2, "flags");
          return r2 && r2.get === t2 || s(e2, "flags", { configurable: true, enumerable: false, get: t2 }), t2;
        };
      }, function(t, e, r) {
        "use strict";
        var n = Date.prototype.getDay, o = Object.prototype.toString, i = r(1)();
        t.exports = function(t2) {
          return "object" == typeof t2 && null !== t2 && (i ? function(t3) {
            try {
              return n.call(t3), true;
            } catch (t4) {
              return false;
            }
          }(t2) : "[object Date]" === o.call(t2));
        };
      }, function(t, e, r) {
        var n = r(6), o = r(15), i = { attributes: { compose: function(t2, e2, r2) {
          "object" != typeof t2 && (t2 = {}), "object" != typeof e2 && (e2 = {});
          var n2 = o(true, {}, e2);
          for (var i2 in r2 || (n2 = Object.keys(n2).reduce(function(t3, e3) {
            return null != n2[e3] && (t3[e3] = n2[e3]), t3;
          }, {})), t2) void 0 !== t2[i2] && void 0 === e2[i2] && (n2[i2] = t2[i2]);
          return Object.keys(n2).length > 0 ? n2 : void 0;
        }, diff: function(t2, e2) {
          "object" != typeof t2 && (t2 = {}), "object" != typeof e2 && (e2 = {});
          var r2 = Object.keys(t2).concat(Object.keys(e2)).reduce(function(r3, o2) {
            return n(t2[o2], e2[o2]) || (r3[o2] = void 0 === e2[o2] ? null : e2[o2]), r3;
          }, {});
          return Object.keys(r2).length > 0 ? r2 : void 0;
        }, transform: function(t2, e2, r2) {
          if ("object" != typeof t2) return e2;
          if ("object" == typeof e2) {
            if (!r2) return e2;
            var n2 = Object.keys(e2).reduce(function(r3, n3) {
              return void 0 === t2[n3] && (r3[n3] = e2[n3]), r3;
            }, {});
            return Object.keys(n2).length > 0 ? n2 : void 0;
          }
        } }, iterator: function(t2) {
          return new s(t2);
        }, length: function(t2) {
          return "number" == typeof t2.delete ? t2.delete : "number" == typeof t2.retain ? t2.retain : "string" == typeof t2.insert ? t2.insert.length : 1;
        } };
        function s(t2) {
          this.ops = t2, this.index = 0, this.offset = 0;
        }
        s.prototype.hasNext = function() {
          return this.peekLength() < 1 / 0;
        }, s.prototype.next = function(t2) {
          t2 || (t2 = 1 / 0);
          var e2 = this.ops[this.index];
          if (e2) {
            var r2 = this.offset, n2 = i.length(e2);
            if (t2 >= n2 - r2 ? (t2 = n2 - r2, this.index += 1, this.offset = 0) : this.offset += t2, "number" == typeof e2.delete) return { delete: t2 };
            var o2 = {};
            return e2.attributes && (o2.attributes = e2.attributes), "number" == typeof e2.retain ? o2.retain = t2 : "string" == typeof e2.insert ? o2.insert = e2.insert.substr(r2, t2) : o2.insert = e2.insert, o2;
          }
          return { retain: 1 / 0 };
        }, s.prototype.peek = function() {
          return this.ops[this.index];
        }, s.prototype.peekLength = function() {
          return this.ops[this.index] ? i.length(this.ops[this.index]) - this.offset : 1 / 0;
        }, s.prototype.peekType = function() {
          return this.ops[this.index] ? "number" == typeof this.ops[this.index].delete ? "delete" : "number" == typeof this.ops[this.index].retain ? "retain" : "insert" : "retain";
        }, s.prototype.rest = function() {
          if (this.hasNext()) {
            if (0 === this.offset) return this.ops.slice(this.index);
            var t2 = this.offset, e2 = this.index, r2 = this.next(), n2 = this.ops.slice(this.index);
            return this.offset = t2, this.index = e2, [r2].concat(n2);
          }
          return [];
        }, t.exports = i;
      }, function(t, e, r) {
        "use strict";
        var n = r(34), o = r(37);
        function i() {
          this.protocol = null, this.slashes = null, this.auth = null, this.host = null, this.port = null, this.hostname = null, this.hash = null, this.search = null, this.query = null, this.pathname = null, this.path = null, this.href = null;
        }
        e.parse = v, e.resolve = function(t2, e2) {
          return v(t2, false, true).resolve(e2);
        }, e.resolveObject = function(t2, e2) {
          return t2 ? v(t2, false, true).resolveObject(e2) : e2;
        }, e.format = function(t2) {
          o.isString(t2) && (t2 = v(t2));
          return t2 instanceof i ? t2.format() : i.prototype.format.call(t2);
        }, e.Url = i;
        var s = /^([a-z0-9.+-]+:)/i, a = /:[0-9]*$/, u = /^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/, p = ["{", "}", "|", "\\", "^", "`"].concat(["<", ">", '"', "`", " ", "\r", "\n", "	"]), c = ["'"].concat(p), l = ["%", "/", "?", ";", "#"].concat(c), f = ["/", "?", "#"], h = /^[+a-z0-9A-Z_-]{0,63}$/, y = /^([+a-z0-9A-Z_-]{0,63})(.*)$/, g = { javascript: true, "javascript:": true }, d = { javascript: true, "javascript:": true }, b = { http: true, https: true, ftp: true, gopher: true, file: true, "http:": true, "https:": true, "ftp:": true, "gopher:": true, "file:": true }, m = r(38);
        function v(t2, e2, r2) {
          if (t2 && o.isObject(t2) && t2 instanceof i) return t2;
          var n2 = new i();
          return n2.parse(t2, e2, r2), n2;
        }
        i.prototype.parse = function(t2, e2, r2) {
          if (!o.isString(t2)) throw new TypeError("Parameter 'url' must be a string, not " + typeof t2);
          var i2 = t2.indexOf("?"), a2 = -1 !== i2 && i2 < t2.indexOf("#") ? "?" : "#", p2 = t2.split(a2);
          p2[0] = p2[0].replace(/\\/g, "/");
          var v2 = t2 = p2.join(a2);
          if (v2 = v2.trim(), !r2 && 1 === t2.split("#").length) {
            var x = u.exec(v2);
            if (x) return this.path = v2, this.href = v2, this.pathname = x[1], x[2] ? (this.search = x[2], this.query = e2 ? m.parse(this.search.substr(1)) : this.search.substr(1)) : e2 && (this.search = "", this.query = {}), this;
          }
          var w = s.exec(v2);
          if (w) {
            var j = (w = w[0]).toLowerCase();
            this.protocol = j, v2 = v2.substr(w.length);
          }
          if (r2 || w || v2.match(/^\/\/[^@\/]+@[^@\/]+/)) {
            var O = "//" === v2.substr(0, 2);
            !O || w && d[w] || (v2 = v2.substr(2), this.slashes = true);
          }
          if (!d[w] && (O || w && !b[w])) {
            for (var A, P, S = -1, E = 0; E < f.length; E++) {
              -1 !== (k = v2.indexOf(f[E])) && (-1 === S || k < S) && (S = k);
            }
            -1 !== (P = -1 === S ? v2.lastIndexOf("@") : v2.lastIndexOf("@", S)) && (A = v2.slice(0, P), v2 = v2.slice(P + 1), this.auth = decodeURIComponent(A)), S = -1;
            for (E = 0; E < l.length; E++) {
              var k;
              -1 !== (k = v2.indexOf(l[E])) && (-1 === S || k < S) && (S = k);
            }
            -1 === S && (S = v2.length), this.host = v2.slice(0, S), v2 = v2.slice(S), this.parseHost(), this.hostname = this.hostname || "";
            var I = "[" === this.hostname[0] && "]" === this.hostname[this.hostname.length - 1];
            if (!I) for (var R = this.hostname.split(/\./), F = (E = 0, R.length); E < F; E++) {
              var U = R[E];
              if (U && !U.match(h)) {
                for (var N = "", T = 0, C = U.length; T < C; T++) U.charCodeAt(T) > 127 ? N += "x" : N += U[T];
                if (!N.match(h)) {
                  var $ = R.slice(0, E), M = R.slice(E + 1), L = U.match(y);
                  L && ($.push(L[1]), M.unshift(L[2])), M.length && (v2 = "/" + M.join(".") + v2), this.hostname = $.join(".");
                  break;
                }
              }
            }
            this.hostname.length > 255 ? this.hostname = "" : this.hostname = this.hostname.toLowerCase(), I || (this.hostname = n.toASCII(this.hostname));
            var D = this.port ? ":" + this.port : "", _ = this.hostname || "";
            this.host = _ + D, this.href += this.host, I && (this.hostname = this.hostname.substr(1, this.hostname.length - 2), "/" !== v2[0] && (v2 = "/" + v2));
          }
          if (!g[j]) for (E = 0, F = c.length; E < F; E++) {
            var q = c[E];
            if (-1 !== v2.indexOf(q)) {
              var z = encodeURIComponent(q);
              z === q && (z = escape(q)), v2 = v2.split(q).join(z);
            }
          }
          var H = v2.indexOf("#");
          -1 !== H && (this.hash = v2.substr(H), v2 = v2.slice(0, H));
          var W = v2.indexOf("?");
          if (-1 !== W ? (this.search = v2.substr(W), this.query = v2.substr(W + 1), e2 && (this.query = m.parse(this.query)), v2 = v2.slice(0, W)) : e2 && (this.search = "", this.query = {}), v2 && (this.pathname = v2), b[j] && this.hostname && !this.pathname && (this.pathname = "/"), this.pathname || this.search) {
            D = this.pathname || "";
            var B = this.search || "";
            this.path = D + B;
          }
          return this.href = this.format(), this;
        }, i.prototype.format = function() {
          var t2 = this.auth || "";
          t2 && (t2 = (t2 = encodeURIComponent(t2)).replace(/%3A/i, ":"), t2 += "@");
          var e2 = this.protocol || "", r2 = this.pathname || "", n2 = this.hash || "", i2 = false, s2 = "";
          this.host ? i2 = t2 + this.host : this.hostname && (i2 = t2 + (-1 === this.hostname.indexOf(":") ? this.hostname : "[" + this.hostname + "]"), this.port && (i2 += ":" + this.port)), this.query && o.isObject(this.query) && Object.keys(this.query).length && (s2 = m.stringify(this.query));
          var a2 = this.search || s2 && "?" + s2 || "";
          return e2 && ":" !== e2.substr(-1) && (e2 += ":"), this.slashes || (!e2 || b[e2]) && false !== i2 ? (i2 = "//" + (i2 || ""), r2 && "/" !== r2.charAt(0) && (r2 = "/" + r2)) : i2 || (i2 = ""), n2 && "#" !== n2.charAt(0) && (n2 = "#" + n2), a2 && "?" !== a2.charAt(0) && (a2 = "?" + a2), e2 + i2 + (r2 = r2.replace(/[?#]/g, function(t3) {
            return encodeURIComponent(t3);
          })) + (a2 = a2.replace("#", "%23")) + n2;
        }, i.prototype.resolve = function(t2) {
          return this.resolveObject(v(t2, false, true)).format();
        }, i.prototype.resolveObject = function(t2) {
          if (o.isString(t2)) {
            var e2 = new i();
            e2.parse(t2, false, true), t2 = e2;
          }
          for (var r2 = new i(), n2 = Object.keys(this), s2 = 0; s2 < n2.length; s2++) {
            var a2 = n2[s2];
            r2[a2] = this[a2];
          }
          if (r2.hash = t2.hash, "" === t2.href) return r2.href = r2.format(), r2;
          if (t2.slashes && !t2.protocol) {
            for (var u2 = Object.keys(t2), p2 = 0; p2 < u2.length; p2++) {
              var c2 = u2[p2];
              "protocol" !== c2 && (r2[c2] = t2[c2]);
            }
            return b[r2.protocol] && r2.hostname && !r2.pathname && (r2.path = r2.pathname = "/"), r2.href = r2.format(), r2;
          }
          if (t2.protocol && t2.protocol !== r2.protocol) {
            if (!b[t2.protocol]) {
              for (var l2 = Object.keys(t2), f2 = 0; f2 < l2.length; f2++) {
                var h2 = l2[f2];
                r2[h2] = t2[h2];
              }
              return r2.href = r2.format(), r2;
            }
            if (r2.protocol = t2.protocol, t2.host || d[t2.protocol]) r2.pathname = t2.pathname;
            else {
              for (var y2 = (t2.pathname || "").split("/"); y2.length && !(t2.host = y2.shift()); ) ;
              t2.host || (t2.host = ""), t2.hostname || (t2.hostname = ""), "" !== y2[0] && y2.unshift(""), y2.length < 2 && y2.unshift(""), r2.pathname = y2.join("/");
            }
            if (r2.search = t2.search, r2.query = t2.query, r2.host = t2.host || "", r2.auth = t2.auth, r2.hostname = t2.hostname || t2.host, r2.port = t2.port, r2.pathname || r2.search) {
              var g2 = r2.pathname || "", m2 = r2.search || "";
              r2.path = g2 + m2;
            }
            return r2.slashes = r2.slashes || t2.slashes, r2.href = r2.format(), r2;
          }
          var v2 = r2.pathname && "/" === r2.pathname.charAt(0), x = t2.host || t2.pathname && "/" === t2.pathname.charAt(0), w = x || v2 || r2.host && t2.pathname, j = w, O = r2.pathname && r2.pathname.split("/") || [], A = (y2 = t2.pathname && t2.pathname.split("/") || [], r2.protocol && !b[r2.protocol]);
          if (A && (r2.hostname = "", r2.port = null, r2.host && ("" === O[0] ? O[0] = r2.host : O.unshift(r2.host)), r2.host = "", t2.protocol && (t2.hostname = null, t2.port = null, t2.host && ("" === y2[0] ? y2[0] = t2.host : y2.unshift(t2.host)), t2.host = null), w = w && ("" === y2[0] || "" === O[0])), x) r2.host = t2.host || "" === t2.host ? t2.host : r2.host, r2.hostname = t2.hostname || "" === t2.hostname ? t2.hostname : r2.hostname, r2.search = t2.search, r2.query = t2.query, O = y2;
          else if (y2.length) O || (O = []), O.pop(), O = O.concat(y2), r2.search = t2.search, r2.query = t2.query;
          else if (!o.isNullOrUndefined(t2.search)) {
            if (A) r2.hostname = r2.host = O.shift(), (I = !!(r2.host && r2.host.indexOf("@") > 0) && r2.host.split("@")) && (r2.auth = I.shift(), r2.host = r2.hostname = I.shift());
            return r2.search = t2.search, r2.query = t2.query, o.isNull(r2.pathname) && o.isNull(r2.search) || (r2.path = (r2.pathname ? r2.pathname : "") + (r2.search ? r2.search : "")), r2.href = r2.format(), r2;
          }
          if (!O.length) return r2.pathname = null, r2.search ? r2.path = "/" + r2.search : r2.path = null, r2.href = r2.format(), r2;
          for (var P = O.slice(-1)[0], S = (r2.host || t2.host || O.length > 1) && ("." === P || ".." === P) || "" === P, E = 0, k = O.length; k >= 0; k--) "." === (P = O[k]) ? O.splice(k, 1) : ".." === P ? (O.splice(k, 1), E++) : E && (O.splice(k, 1), E--);
          if (!w && !j) for (; E--; E) O.unshift("..");
          !w || "" === O[0] || O[0] && "/" === O[0].charAt(0) || O.unshift(""), S && "/" !== O.join("/").substr(-1) && O.push("");
          var I, R = "" === O[0] || O[0] && "/" === O[0].charAt(0);
          A && (r2.hostname = r2.host = R ? "" : O.length ? O.shift() : "", (I = !!(r2.host && r2.host.indexOf("@") > 0) && r2.host.split("@")) && (r2.auth = I.shift(), r2.host = r2.hostname = I.shift()));
          return (w = w || r2.host && O.length) && !R && O.unshift(""), O.length ? r2.pathname = O.join("/") : (r2.pathname = null, r2.path = null), o.isNull(r2.pathname) && o.isNull(r2.search) || (r2.path = (r2.pathname ? r2.pathname : "") + (r2.search ? r2.search : "")), r2.auth = t2.auth || r2.auth, r2.slashes = r2.slashes || t2.slashes, r2.href = r2.format(), r2;
        }, i.prototype.parseHost = function() {
          var t2 = this.host, e2 = a.exec(t2);
          e2 && (":" !== (e2 = e2[0]) && (this.port = e2.substr(1)), t2 = t2.substr(0, t2.length - e2.length)), t2 && (this.hostname = t2);
        };
      }, function(t, e, r) {
        (function(t2, n) {
          var o;
          !function(i) {
            e && e.nodeType, t2 && t2.nodeType;
            var s = "object" == typeof n && n;
            s.global !== s && s.window !== s && s.self;
            var a, u = 2147483647, p = /^xn--/, c = /[^\x20-\x7E]/, l = /[\x2E\u3002\uFF0E\uFF61]/g, f = { overflow: "Overflow: input needs wider integers to process", "not-basic": "Illegal input >= 0x80 (not a basic code point)", "invalid-input": "Invalid input" }, h = Math.floor, y = String.fromCharCode;
            function g(t3) {
              throw new RangeError(f[t3]);
            }
            function d(t3, e2) {
              for (var r2 = t3.length, n2 = []; r2--; ) n2[r2] = e2(t3[r2]);
              return n2;
            }
            function b(t3, e2) {
              var r2 = t3.split("@"), n2 = "";
              return r2.length > 1 && (n2 = r2[0] + "@", t3 = r2[1]), n2 + d((t3 = t3.replace(l, ".")).split("."), e2).join(".");
            }
            function m(t3) {
              for (var e2, r2, n2 = [], o2 = 0, i2 = t3.length; o2 < i2; ) (e2 = t3.charCodeAt(o2++)) >= 55296 && e2 <= 56319 && o2 < i2 ? 56320 == (64512 & (r2 = t3.charCodeAt(o2++))) ? n2.push(((1023 & e2) << 10) + (1023 & r2) + 65536) : (n2.push(e2), o2--) : n2.push(e2);
              return n2;
            }
            function v(t3) {
              return d(t3, function(t4) {
                var e2 = "";
                return t4 > 65535 && (e2 += y((t4 -= 65536) >>> 10 & 1023 | 55296), t4 = 56320 | 1023 & t4), e2 += y(t4);
              }).join("");
            }
            function x(t3, e2) {
              return t3 + 22 + 75 * (t3 < 26) - ((0 != e2) << 5);
            }
            function w(t3, e2, r2) {
              var n2 = 0;
              for (t3 = r2 ? h(t3 / 700) : t3 >> 1, t3 += h(t3 / e2); t3 > 455; n2 += 36) t3 = h(t3 / 35);
              return h(n2 + 36 * t3 / (t3 + 38));
            }
            function j(t3) {
              var e2, r2, n2, o2, i2, s2, a2, p2, c2, l2, f2, y2 = [], d2 = t3.length, b2 = 0, m2 = 128, x2 = 72;
              for ((r2 = t3.lastIndexOf("-")) < 0 && (r2 = 0), n2 = 0; n2 < r2; ++n2) t3.charCodeAt(n2) >= 128 && g("not-basic"), y2.push(t3.charCodeAt(n2));
              for (o2 = r2 > 0 ? r2 + 1 : 0; o2 < d2; ) {
                for (i2 = b2, s2 = 1, a2 = 36; o2 >= d2 && g("invalid-input"), ((p2 = (f2 = t3.charCodeAt(o2++)) - 48 < 10 ? f2 - 22 : f2 - 65 < 26 ? f2 - 65 : f2 - 97 < 26 ? f2 - 97 : 36) >= 36 || p2 > h((u - b2) / s2)) && g("overflow"), b2 += p2 * s2, !(p2 < (c2 = a2 <= x2 ? 1 : a2 >= x2 + 26 ? 26 : a2 - x2)); a2 += 36) s2 > h(u / (l2 = 36 - c2)) && g("overflow"), s2 *= l2;
                x2 = w(b2 - i2, e2 = y2.length + 1, 0 == i2), h(b2 / e2) > u - m2 && g("overflow"), m2 += h(b2 / e2), b2 %= e2, y2.splice(b2++, 0, m2);
              }
              return v(y2);
            }
            function O(t3) {
              var e2, r2, n2, o2, i2, s2, a2, p2, c2, l2, f2, d2, b2, v2, j2, O2 = [];
              for (d2 = (t3 = m(t3)).length, e2 = 128, r2 = 0, i2 = 72, s2 = 0; s2 < d2; ++s2) (f2 = t3[s2]) < 128 && O2.push(y(f2));
              for (n2 = o2 = O2.length, o2 && O2.push("-"); n2 < d2; ) {
                for (a2 = u, s2 = 0; s2 < d2; ++s2) (f2 = t3[s2]) >= e2 && f2 < a2 && (a2 = f2);
                for (a2 - e2 > h((u - r2) / (b2 = n2 + 1)) && g("overflow"), r2 += (a2 - e2) * b2, e2 = a2, s2 = 0; s2 < d2; ++s2) if ((f2 = t3[s2]) < e2 && ++r2 > u && g("overflow"), f2 == e2) {
                  for (p2 = r2, c2 = 36; !(p2 < (l2 = c2 <= i2 ? 1 : c2 >= i2 + 26 ? 26 : c2 - i2)); c2 += 36) j2 = p2 - l2, v2 = 36 - l2, O2.push(y(x(l2 + j2 % v2, 0))), p2 = h(j2 / v2);
                  O2.push(y(x(p2, 0))), i2 = w(r2, b2, n2 == o2), r2 = 0, ++n2;
                }
                ++r2, ++e2;
              }
              return O2.join("");
            }
            a = { version: "1.4.1", ucs2: { decode: m, encode: v }, decode: j, encode: O, toASCII: function(t3) {
              return b(t3, function(t4) {
                return c.test(t4) ? "xn--" + O(t4) : t4;
              });
            }, toUnicode: function(t3) {
              return b(t3, function(t4) {
                return p.test(t4) ? j(t4.slice(4).toLowerCase()) : t4;
              });
            } }, void 0 === (o = function() {
              return a;
            }.call(e, r, e, t2)) || (t2.exports = o);
          }();
        }).call(this, r(35)(t), r(36));
      }, function(t, e) {
        t.exports = function(t2) {
          return t2.webpackPolyfill || (t2.deprecate = function() {
          }, t2.paths = [], t2.children || (t2.children = []), Object.defineProperty(t2, "loaded", { enumerable: true, get: function() {
            return t2.l;
          } }), Object.defineProperty(t2, "id", { enumerable: true, get: function() {
            return t2.i;
          } }), t2.webpackPolyfill = 1), t2;
        };
      }, function(t, e) {
        var r;
        r = /* @__PURE__ */ function() {
          return this;
        }();
        try {
          r = r || new Function("return this")();
        } catch (t2) {
          "object" == typeof window && (r = window);
        }
        t.exports = r;
      }, function(t, e, r) {
        "use strict";
        t.exports = { isString: function(t2) {
          return "string" == typeof t2;
        }, isObject: function(t2) {
          return "object" == typeof t2 && null !== t2;
        }, isNull: function(t2) {
          return null === t2;
        }, isNullOrUndefined: function(t2) {
          return null == t2;
        } };
      }, function(t, e, r) {
        "use strict";
        e.decode = e.parse = r(39), e.encode = e.stringify = r(40);
      }, function(t, e, r) {
        "use strict";
        function n(t2, e2) {
          return Object.prototype.hasOwnProperty.call(t2, e2);
        }
        t.exports = function(t2, e2, r2, i) {
          e2 = e2 || "&", r2 = r2 || "=";
          var s = {};
          if ("string" != typeof t2 || 0 === t2.length) return s;
          var a = /\+/g;
          t2 = t2.split(e2);
          var u = 1e3;
          i && "number" == typeof i.maxKeys && (u = i.maxKeys);
          var p = t2.length;
          u > 0 && p > u && (p = u);
          for (var c = 0; c < p; ++c) {
            var l, f, h, y, g = t2[c].replace(a, "%20"), d = g.indexOf(r2);
            d >= 0 ? (l = g.substr(0, d), f = g.substr(d + 1)) : (l = g, f = ""), h = decodeURIComponent(l), y = decodeURIComponent(f), n(s, h) ? o(s[h]) ? s[h].push(y) : s[h] = [s[h], y] : s[h] = y;
          }
          return s;
        };
        var o = Array.isArray || function(t2) {
          return "[object Array]" === Object.prototype.toString.call(t2);
        };
      }, function(t, e, r) {
        "use strict";
        var n = function(t2) {
          switch (typeof t2) {
            case "string":
              return t2;
            case "boolean":
              return t2 ? "true" : "false";
            case "number":
              return isFinite(t2) ? t2 : "";
            default:
              return "";
          }
        };
        t.exports = function(t2, e2, r2, a) {
          return e2 = e2 || "&", r2 = r2 || "=", null === t2 && (t2 = void 0), "object" == typeof t2 ? i(s(t2), function(s2) {
            var a2 = encodeURIComponent(n(s2)) + r2;
            return o(t2[s2]) ? i(t2[s2], function(t3) {
              return a2 + encodeURIComponent(n(t3));
            }).join(e2) : a2 + encodeURIComponent(n(t2[s2]));
          }).join(e2) : a ? encodeURIComponent(n(a)) + r2 + encodeURIComponent(n(t2)) : "";
        };
        var o = Array.isArray || function(t2) {
          return "[object Array]" === Object.prototype.toString.call(t2);
        };
        function i(t2, e2) {
          if (t2.map) return t2.map(e2);
          for (var r2 = [], n2 = 0; n2 < t2.length; n2++) r2.push(e2(t2[n2], n2));
          return r2;
        }
        var s = Object.keys || function(t2) {
          var e2 = [];
          for (var r2 in t2) Object.prototype.hasOwnProperty.call(t2, r2) && e2.push(r2);
          return e2;
        };
      }]);
    });
  }
});
export default require_dist();
/*! Bundled license information:

quill-magic-url/dist/index.js:
  (*! https://mths.be/punycode v1.4.1 by @mathias *)
*/
//# sourceMappingURL=quill-magic-url.js.map
