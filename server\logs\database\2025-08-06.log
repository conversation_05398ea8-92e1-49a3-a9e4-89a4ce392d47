2025-08-06T10:05:15.738+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [177 ms] [default] [hotgo] [rows:1  ] SELECT version();
2025-08-06T10:05:15.745+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [  6 ms] [default] [hotgo] [rows:39 ] SELECT c.relname FROM pg_class c INNER JOIN pg_namespace n ON c.relnamespace = n.oid WHERE n.nspname = 'public' AND c.relkind IN ('r', 'p') AND c.relpartbound IS NULL ORDER BY c.relname
2025-08-06T10:05:15.766+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [ 21 ms] [default] [hotgo] [rows:17 ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_sys_config' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:05:15.770+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [  4 ms] [default] [hotgo] [rows:8  ] SELECT "key","value","type" FROM "hg_sys_config" WHERE "group"='wechat'
2025-08-06T10:05:15.770+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [  0 ms] [default] [hotgo] [rows:14 ] SELECT "key","value","type" FROM "hg_sys_config" WHERE "group"='pay'
2025-08-06T10:05:15.771+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [  1 ms] [default] [hotgo] [rows:35 ] SELECT "key","value","type" FROM "hg_sys_config" WHERE "group"='upload'
2025-08-06T10:05:15.771+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [  0 ms] [default] [hotgo] [rows:15 ] SELECT "key","value","type" FROM "hg_sys_config" WHERE "group"='sms'
2025-08-06T10:05:15.778+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/admin/member.go:855: [  6 ms] [default] [hotgo] [rows:13 ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_admin_role' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:05:15.780+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/admin/member.go:855: [  2 ms] [default] [hotgo] [rows:1  ] SELECT "id" FROM "hg_admin_role" WHERE "key"='super' LIMIT 1
2025-08-06T10:05:15.788+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/admin/member.go:866: [  8 ms] [default] [hotgo] [rows:28 ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_admin_member' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:05:15.789+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/admin/member.go:866: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id" FROM "hg_admin_member" WHERE "role_id"='1'
2025-08-06T10:05:15.802+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/cron.go:41: [  6 ms] [default] [hotgo] [rows:13 ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_sys_cron' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:05:15.806+08:00 [DEBU] {54a418c5430b59186f69f132f67aef2f} E:/NaviHotgo/server/internal/library/addons/install.go:33: [  4 ms] [default] [hotgo] [rows:6  ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_sys_addons_install' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:05:15.808+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/cron.go:41: [  1 ms] [default] [hotgo] [rows:0  ] SELECT "id","group_id","title","name","params","pattern","policy","count","sort","remark","status","created_at","updated_at" FROM "hg_sys_cron" WHERE "status"=1 ORDER BY "sort" asc,"id" desc
2025-08-06T10:05:15.861+08:00 [DEBU] {54a418c5430b59186f69f132f67aef2f} E:/NaviHotgo/server/internal/library/addons/install.go:33: [ 54 ms] [default] [hotgo] [rows:0  ] SELECT "id","version","status","created_at","updated_at" FROM "hg_sys_addons_install" WHERE "name"='hgexample' LIMIT 1
2025-08-06T10:05:15.862+08:00 [DEBU] {54a418c5430b59186f69f132f67aef2f} E:/NaviHotgo/server/internal/library/addons/install.go:33: [  0 ms] [default] [hotgo] [rows:0  ] SELECT "id","version","status","created_at","updated_at" FROM "hg_sys_addons_install" WHERE "name"='hgexample' LIMIT 1
2025-08-06T10:05:15.935+08:00 [DEBU] {a81b90dc430b59187869f132aad6ecf7} E:/NaviHotgo/server/internal/library/casbin/enforcer.go:116: [  6 ms] [default] [hotgo] [rows:2  ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_admin_role_menu' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:05:15.940+08:00 [DEBU] {a8a6fadc430b59187969f132782e7da5} E:/NaviHotgo/server/internal/library/casbin/enforcer.go:116: [  5 ms] [default] [hotgo] [rows:35 ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_admin_menu' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:05:15.945+08:00 [DEBU] {34c64edd430b59187a69f13280f6308e} E:/NaviHotgo/server/internal/library/casbin/enforcer.go:116: [  4 ms] [default] [hotgo] [rows:305] SELECT r.key,m.permissions FROM "hg_admin_role" r LEFT JOIN "hg_admin_role_menu" rm ON (r.id=rm.role_id) LEFT JOIN "hg_admin_menu" m ON (rm.menu_id=m.id) WHERE ("r"."status"=1) AND ("m"."status"=1) AND (m.permissions != '') AND (r.key != 'super')
2025-08-06T10:05:15.951+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/blacklist.go:133: [  5 ms] [default] [hotgo] [rows:6  ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_sys_blacklist' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:05:15.952+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/blacklist.go:135: [  1 ms] [default] [hotgo] [rows:4  ] SELECT "ip" FROM "hg_sys_blacklist" WHERE "status"=1
2025-08-06T10:05:20.822+08:00 [DEBU] {d096e1ff440b59187c69f1320826c31c} E:/NaviHotgo/server/internal/logic/tcpserver/server_handle.go:37: [  6 ms] [default] [hotgo] [rows:17 ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_sys_serve_license' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:05:20.824+08:00 [DEBU] {d096e1ff440b59187c69f1320826c31c} E:/NaviHotgo/server/internal/logic/tcpserver/server_handle.go:37: [  2 ms] [default] [hotgo] [rows:1  ] SELECT "id","group","name","appid","secret_key","remote_addr","online_limit","login_times","last_login_at","last_active_at","routes","allowed_ips","end_at","remark","status","created_at","updated_at" FROM "hg_sys_serve_license" WHERE "appid"='1002' LIMIT 1
2025-08-06T10:05:20.826+08:00 [DEBU] {d096e1ff440b59187c69f1320826c31c} E:/NaviHotgo/server/internal/logic/tcpserver/server_handle.go:124: [  1 ms] [default] [hotgo] [rows:1  ] UPDATE "hg_sys_serve_license" SET "remote_addr"='127.0.0.1:54061',"login_times"=300,"last_login_at"='2025-08-06 10:05:20',"last_active_at"='2025-08-06 10:05:20',"updated_at"='2025-08-06 10:05:20' WHERE "id"=1
2025-08-06T10:05:54.733+08:00 [DEBU] {cc5989e14c0b59188069f132c33e427c} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [ 65 ms] [default] [hotgo] [rows:9  ] SELECT "key","value","type" FROM "hg_sys_config" WHERE "group"='basic'
2025-08-06T10:05:55.217+08:00 [DEBU] {1c2151024d0b59188169f1327f7b2a42} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [  1 ms] [default] [hotgo] [rows:10 ] SELECT "key","value","type" FROM "hg_sys_config" WHERE "group"='login'
2025-08-06T10:05:55.246+08:00 [DEBU] {183800044d0b59188269f1327ec0e55f} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [  1 ms] [default] [hotgo] [rows:10 ] SELECT "key","value","type" FROM "hg_sys_config" WHERE "group"='login'
2025-08-06T10:05:55.826+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [ 10 ms] [default] [hotgo] [rows:23 ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_sys_log' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:05:55.883+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [ 57 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('cc5989e14c0b59188069f132c33e427c','GET','admin','/admin/site/config','{}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4"],"Enter-Time":["2025-08-06 10:05:54"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',68,1754445954,1,'2025-08-06 10:05:54','2025-08-06 10:05:54')  RETURNING "id"
2025-08-06T10:05:55.896+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  1 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('1c2151024d0b59188169f1327f7b2a42','GET','admin','/admin/site/loginConfig','{}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4"],"Enter-Time":["2025-08-06 10:05:55"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',1,1754445955,1,'2025-08-06 10:05:55','2025-08-06 10:05:55')  RETURNING "id"
2025-08-06T10:05:55.908+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  1 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('183800044d0b59188269f1327ec0e55f','GET','admin','/admin/site/captcha','{}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4"],"Enter-Time":["2025-08-06 10:05:55"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',2,1754445955,1,'2025-08-06 10:05:55','2025-08-06 10:05:55')  RETURNING "id"
2025-08-06T10:06:03.120+08:00 [DEBU] {24515ed94e0b59188369f132e92a2496} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [  1 ms] [default] [hotgo] [rows:10 ] SELECT "key","value","type" FROM "hg_sys_config" WHERE "group"='login'
2025-08-06T10:06:03.925+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  2 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('24515ed94e0b59188369f132e92a2496','GET','admin','/admin/site/captcha','{}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4"],"Enter-Time":["2025-08-06 10:06:03"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',1,1754445963,1,'2025-08-06 10:06:03','2025-08-06 10:06:03')  RETURNING "id"
2025-08-06T10:06:09.088+08:00 [DEBU] {d05b153d500b59188469f13241c379e2} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [  1 ms] [default] [hotgo] [rows:10 ] SELECT "key","value","type" FROM "hg_sys_config" WHERE "group"='login'
2025-08-06T10:06:09.091+08:00 [DEBU] {d05b153d500b59188469f13241c379e2} E:/NaviHotgo/server/internal/logic/admin/site.go:152: [  3 ms] [default] [hotgo] [rows:1  ] SELECT "id","dept_id","role_id","real_name","username","password_hash","salt","password_reset_token","integral","balance","avatar","sex","qq","email","mobile","birthday","city_id","address","pid","level","tree","invite_code","cash","last_active_at","remark","status","created_at","updated_at" FROM "hg_admin_member" WHERE "username"='admin' LIMIT 1
2025-08-06T10:06:09.092+08:00 [DEBU] {d05b153d500b59188469f13241c379e2} E:/NaviHotgo/server/internal/logic/admin/site.go:302: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","key","status" FROM "hg_admin_role" WHERE "id"=1 LIMIT 1
2025-08-06T10:06:09.097+08:00 [DEBU] {d05b153d500b59188469f13241c379e2} E:/NaviHotgo/server/internal/logic/admin/site.go:317: [  5 ms] [default] [hotgo] [rows:14 ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_admin_dept' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:06:09.098+08:00 [DEBU] {d05b153d500b59188469f13241c379e2} E:/NaviHotgo/server/internal/logic/admin/site.go:317: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id","type","status" FROM "hg_admin_dept" WHERE "id"=100 LIMIT 1
2025-08-06T10:06:09.100+08:00 [DEBU] {d05b153d500b59188469f13241c379e2} E:/NaviHotgo/server/internal/logic/admin/menu.go:368: [  1 ms] [default] [hotgo] [rows:151] SELECT "permissions" FROM "hg_admin_menu" WHERE ("status"=1) AND ("permissions" != '')
2025-08-06T10:06:09.348+08:00 [DEBU] {08e3964c500b59188569f1329f35c7fe} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [  1 ms] [default] [hotgo] [rows:9  ] SELECT "key","value","type" FROM "hg_sys_config" WHERE "group"='basic'
2025-08-06T10:06:09.426+08:00 [DEBU] {d07f2d51500b59188669f1320d9ae5bf} E:/NaviHotgo/server/internal/logic/admin/site.go:341: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id","dept_id","role_id","real_name","username","password_hash","salt","password_reset_token","integral","balance","avatar","sex","qq","email","mobile","birthday","city_id","address","pid","level","tree","invite_code","cash","last_active_at","remark","status","created_at","updated_at" FROM "hg_admin_member" WHERE "id"=1 LIMIT 1
2025-08-06T10:06:09.426+08:00 [DEBU] {d07f2d51500b59188669f1320d9ae5bf} E:/NaviHotgo/server/internal/logic/admin/site.go:302: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","key","status" FROM "hg_admin_role" WHERE "id"=1 LIMIT 1
2025-08-06T10:06:09.426+08:00 [DEBU] {d07f2d51500b59188669f1320d9ae5bf} E:/NaviHotgo/server/internal/logic/admin/site.go:317: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","type","status" FROM "hg_admin_dept" WHERE "id"=100 LIMIT 1
2025-08-06T10:06:09.431+08:00 [DEBU] {d07f2d51500b59188669f1320d9ae5bf} E:/NaviHotgo/server/internal/logic/admin/menu.go:315: [  4 ms] [default] [hotgo] [rows:65 ] SELECT "id","pid","level","tree","title","name","path","icon","type","redirect","permissions","permission_name","component","always_show","active_menu","is_root","is_frame","frame_src","keep_alive","hidden","affix","sort","remark","status","extra_icon","show_parent","roles","frame_loading","transition_name","transition_enter","transition_leave","hidden_tag","dynamic_level","updated_at","created_at" FROM "hg_admin_menu" WHERE ("status"=1) AND ("type" IN (1,2)) ORDER BY "sort","id" desc
2025-08-06T10:06:09.433+08:00 [DEBU] {d07f2d51500b59188669f1320d9ae5bf} E:/NaviHotgo/server/internal/logic/hook/last_active.go:88: [  1 ms] [default] [hotgo] [rows:1  ] UPDATE "hg_admin_member" SET "last_active_at"='2025-08-06 10:06:09',"updated_at"='2025-08-06 10:06:09' WHERE "id"=1
2025-08-06T10:06:09.941+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  2 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('d05b153d500b59188469f13241c379e2','POST','admin','/admin/site/accountLogin','{}','{"cid":"TzyvaU8vT1OvboaqQBGK","code":"trc8","password":"******","username":"admin"}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Content-Length":["101"],"Content-Type":["application/json"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4"],"Enter-Time":["2025-08-06 10:06:09"],"Origin":["http://localhost:8848"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',14,1754445969,1,'2025-08-06 10:06:09','2025-08-06 10:06:09')  RETURNING "id"
2025-08-06T10:06:09.954+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  1 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","timestamp","status","created_at","updated_at") VALUES('08e3964c500b59188569f1329f35c7fe','GET','admin','/admin/site/config','{}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4; multiple-tabs=true"],"Enter-Time":["2025-08-06 10:06:09"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',1754445969,1,'2025-08-06 10:06:09','2025-08-06 10:06:09')  RETURNING "id"
2025-08-06T10:06:09.966+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  1 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","app_id","member_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('d07f2d51500b59188669f1320d9ae5bf','admin',1,'GET','admin','/admin/role/dynamic','{}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4; multiple-tabs=true"],"Enter-Time":["2025-08-06 10:06:09"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',7,1754445969,1,'2025-08-06 10:06:09','2025-08-06 10:06:09')  RETURNING "id"
2025-08-06T10:06:10.828+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/login_log.go:181: [  6 ms] [default] [hotgo] [rows:14 ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_sys_login_log' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:06:10.834+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/login_log.go:181: [  6 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_login_log"("req_id","member_id","username","response","login_at","login_ip","user_agent","status","created_at","updated_at") VALUES('d05b153d500b59188469f13241c379e2',1,'admin','{"address":"莲花街001号","avatar":"https://gmycos.facms.cn/hotgo/attachment/2023-02-09/cqdq8er9nfkchdopav.png                                                                            ","balance":99289.78,"birthday":"2006-01-02","cityId":410172,"deptName":"","email":"<EMAIL>","expires":604800,"id":1,"integral":88,"mobile":"15303830571","permissions":["dashboard","/dept/list","/post/list","/role/list","/member/list","/dept/option","/loginLog/view","/loginLog/list","/serveLog/view","/serveLog/list","/member/edit","/member/view","/member/delete","/member/status","/dept/edit","/dept/delete","/dept/status","/post/edit","/post/delete","/post/status","/upload/file","/loginLog/delete","/loginLog/export","/serveLog/delete","/serveLog/export","/notice/maxSort","/notice/delete","/notice/status","/notice/switch","/notice/editNotify","/notice/editNotice","/notice/editLetter","/member/addIntegral","/genCodes/build","/genCodes/edit","/addons/upgrade","/addons/uninstall","/curdDemo/view","/curdDemo/edit","/curdDemo/maxSort","/curdDemo/delete","/curdDemo/status","/curdDemo/switch","/curdDemo/export","/hgexample/tenantOrder/view","/hgexample/tenantOrder/edit","/notice/edit","/hgexample/table/maxSort","/hgexample/table/export","/hgexample/table/delete","/hgexample/table/status","/hgexample/table/switch","/hgexample/table/edit","/creditsLog/export","/order/list","/order/acceptRefund","/order/applyRefund","/order/delete","/cash/apply","/cash/payment","/cash/view","/member/addBalance","/member/resetPwd","/config/get","/config/update","/dictType/tree","/dictData/list","/config/typeSelect","/dictData/edit","/dictData/delete","/dictType/edit","/dictType/delete","/cron/list","/cronGroup/select","/cronGroup/list","/cron/edit","/cron/delete","/cron/status","/cron/onlineExec","/cronGroup/edit","/cronGroup/delete","/blacklist/list","/blacklist/edit","/blacklist/status","/blacklist/delete","/member/updatePwd","/member/updateMobile","/member/updateEmail","/serveLicense/list","/serveLicense/edit","/serveLicense/delete","/serveLicense/status","/serveLicense/export","/serveLicense/assignRouter","/member/updateProfile","/member/updateCash","/role/updatePermissions","/role/dataScope/edit","/role/edit","/role/delete","/menu/edit","/menu/delete","/log/delete","/smsLog/delete","/monitor/userOffline","/provinces/edit","/provinces/delete","/genCodes/view","/genCodes/preview","/testCategory/view","/testCategory/edit","/testCategory/maxSort","/testCategory/delete","/testCategory/status","/normalTreeDemo/view","/normalTreeDemo/edit","/normalTreeDemo/maxSort","/normalTreeDemo/delete","/normalTreeDemo/status","/normalTreeDemo/export","/normalTreeDemo/treeOption","/optionTreeDemo/view","/optionTreeDemo/edit","/optionTreeDemo/maxSort","/optionTreeDemo/delete","/optionTreeDemo/status","/optionTreeDemo/export","/optionTreeDemo/treeOption","/hgexample/tenantOrder/delete","/hgexample/tenantOrder/export","/console/stat","dashboard_workplace","/menu/list","/role/dataScope/select","/role/getPermissions","/log/list","/log/view","/smsLog/list","/monitor/userOnlineList","/notice/list","/attachment/list","/provinces/list","/provinces/tree","/provinces/childrenList","/provinces/uniqueId","/genCodes/list","/genCodes/selects","/genCodes/tableSelect","/hgexample/table/list","/hgexample/table/view","/notice/messageList","dashboard_monitor","/addons/selects","/addons/list","/hgexample/config/get","/hgexample/config/update","/order/create","/order/option","/creditsLog/list","/creditsLog/option","/cash/list","/config/getCash","/hgexample/table/tree","/hgexample/treeTable/select","/hgexample/comp/importExcel","/curdDemo/list","/testCategory/list","/normalTreeDemo/list","/optionTreeDemo/list","/hgexample/tenantOrder/list"],"qq":"133814250","realName":"孟帅","roleName":"","roles":["super"],"sex":1,"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwicGlkIjowLCJkZXB0SWQiOjEwMCwiZGVwdFR5cGUiOiJjb21wYW55Iiwicm9sZUlkIjoxLCJyb2xlS2V5Ijoic3VwZXIiLCJ1c2VybmFtZSI6ImFkbWluIiwicmVhbE5hbWUiOiLlrZ_luIUiLCJhdmF0YXIiOiJodHRwczovL2dteWNvcy5mYWNtcy5jbi9ob3Rnby9hdHRhY2htZW50LzIwMjMtMDItMDkvY3FkcThlcjluZmtjaGRvcGF2LnBuZyAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiLCJlbWFpbCI6IjEzMzgxNDI1MEBxcS5jb20iLCJtb2JpbGUiOiIxNTMwMzgzMDU3MSIsImFwcCI6ImFkbWluIiwibG9naW5BdCI6IjIwMjUtMDgtMDYgMTA6MDY6MDkifQ.9QRME8F6Dus4rVqnuqeVcEb3d0DV8vRza2gCt4fB16U","tokenTimeout":1755050769000,"username":"admin"}','2025-08-06 10:06:09','::1','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',1,'2025-08-06 10:06:10','2025-08-06 10:06:10')  RETURNING "id"
2025-08-06T10:06:40.512+08:00 [DEBU] {047a5f8a570b59188769f132f743a812} E:/NaviHotgo/server/internal/logic/admin/site.go:341: [ 63 ms] [default] [hotgo] [rows:1  ] SELECT "id","dept_id","role_id","real_name","username","password_hash","salt","password_reset_token","integral","balance","avatar","sex","qq","email","mobile","birthday","city_id","address","pid","level","tree","invite_code","cash","last_active_at","remark","status","created_at","updated_at" FROM "hg_admin_member" WHERE "id"=1 LIMIT 1
2025-08-06T10:06:40.513+08:00 [DEBU] {047a5f8a570b59188769f132f743a812} E:/NaviHotgo/server/internal/logic/admin/site.go:302: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id","key","status" FROM "hg_admin_role" WHERE "id"=1 LIMIT 1
2025-08-06T10:06:40.514+08:00 [DEBU] {047a5f8a570b59188769f132f743a812} E:/NaviHotgo/server/internal/logic/admin/site.go:317: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id","type","status" FROM "hg_admin_dept" WHERE "id"=100 LIMIT 1
2025-08-06T10:06:40.515+08:00 [DEBU] {047a5f8a570b59188769f132f743a812} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "key","value","type" FROM "hg_sys_config" WHERE "group"='system'
2025-08-06T10:06:40.518+08:00 [DEBU] {047a5f8a570b59188769f132f743a812} E:/NaviHotgo/server/internal/logic/hook/last_active.go:88: [  2 ms] [default] [hotgo] [rows:1  ] UPDATE "hg_admin_member" SET "last_active_at"='2025-08-06 10:06:40',"updated_at"='2025-08-06 10:06:40' WHERE "id"=1
2025-08-06T10:06:40.551+08:00 [DEBU] {c8e56590570b59188869f13258d1d2cc} E:/NaviHotgo/server/internal/logic/admin/site.go:341: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","dept_id","role_id","real_name","username","password_hash","salt","password_reset_token","integral","balance","avatar","sex","qq","email","mobile","birthday","city_id","address","pid","level","tree","invite_code","cash","last_active_at","remark","status","created_at","updated_at" FROM "hg_admin_member" WHERE "id"=1 LIMIT 1
2025-08-06T10:06:40.552+08:00 [DEBU] {c8e56590570b59188869f13258d1d2cc} E:/NaviHotgo/server/internal/logic/admin/site.go:302: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","key","status" FROM "hg_admin_role" WHERE "id"=1 LIMIT 1
2025-08-06T10:06:40.553+08:00 [DEBU] {c8e56590570b59188869f13258d1d2cc} E:/NaviHotgo/server/internal/logic/admin/site.go:317: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","type","status" FROM "hg_admin_dept" WHERE "id"=100 LIMIT 1
2025-08-06T10:06:40.555+08:00 [DEBU] {c8e56590570b59188869f13258d1d2cc} E:/NaviHotgo/server/internal/logic/sys/config.go:519: [  2 ms] [default] [hotgo] [rows:9  ] SELECT "id","group","name","type","key","value","default_value","sort","tip","is_default","status","form_type","options","rules","placeholder","created_at","updated_at" FROM "hg_sys_config" WHERE "group"='basic' ORDER BY "sort" ASC,"id" ASC
2025-08-06T10:06:40.998+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  2 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","app_id","member_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('047a5f8a570b59188769f132f743a812','admin',1,'GET','admin','/admin/config/groups','{}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4; multiple-tabs=true"],"Enter-Time":["2025-08-06 10:06:40"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',66,1754446000,1,'2025-08-06 10:06:40','2025-08-06 10:06:40')  RETURNING "id"
2025-08-06T10:06:41.011+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  1 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","app_id","member_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('c8e56590570b59188869f13258d1d2cc','admin',1,'GET','admin','/admin/config/items','{"group":["basic"]}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4; multiple-tabs=true"],"Enter-Time":["2025-08-06 10:06:40"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',6,1754446000,1,'2025-08-06 10:06:40','2025-08-06 10:06:40')  RETURNING "id"
2025-08-06T10:06:42.398+08:00 [DEBU] {50d07ffe570b59188969f132866b9dc5} E:/NaviHotgo/server/internal/logic/admin/site.go:341: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","dept_id","role_id","real_name","username","password_hash","salt","password_reset_token","integral","balance","avatar","sex","qq","email","mobile","birthday","city_id","address","pid","level","tree","invite_code","cash","last_active_at","remark","status","created_at","updated_at" FROM "hg_admin_member" WHERE "id"=1 LIMIT 1
2025-08-06T10:06:42.399+08:00 [DEBU] {50d07ffe570b59188969f132866b9dc5} E:/NaviHotgo/server/internal/logic/admin/site.go:302: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id","key","status" FROM "hg_admin_role" WHERE "id"=1 LIMIT 1
2025-08-06T10:06:42.400+08:00 [DEBU] {50d07ffe570b59188969f132866b9dc5} E:/NaviHotgo/server/internal/logic/admin/site.go:317: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","type","status" FROM "hg_admin_dept" WHERE "id"=100 LIMIT 1
2025-08-06T10:06:42.401+08:00 [DEBU] {50d07ffe570b59188969f132866b9dc5} E:/NaviHotgo/server/internal/logic/sys/config.go:519: [  1 ms] [default] [hotgo] [rows:0  ] SELECT "id","group","name","type","key","value","default_value","sort","tip","is_default","status","form_type","options","rules","placeholder","created_at","updated_at" FROM "hg_sys_config" WHERE "group"='email' ORDER BY "sort" ASC,"id" ASC
2025-08-06T10:06:43.024+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  2 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","app_id","member_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('50d07ffe570b59188969f132866b9dc5','admin',1,'GET','admin','/admin/config/items','{"group":["email"]}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4; multiple-tabs=true"],"Enter-Time":["2025-08-06 10:06:42"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',3,1754446002,1,'2025-08-06 10:06:42','2025-08-06 10:06:42')  RETURNING "id"
2025-08-06T10:06:43.359+08:00 [DEBU] {d471c137580b59188a69f1325dfd5dc7} E:/NaviHotgo/server/internal/logic/admin/site.go:341: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id","dept_id","role_id","real_name","username","password_hash","salt","password_reset_token","integral","balance","avatar","sex","qq","email","mobile","birthday","city_id","address","pid","level","tree","invite_code","cash","last_active_at","remark","status","created_at","updated_at" FROM "hg_admin_member" WHERE "id"=1 LIMIT 1
2025-08-06T10:06:43.360+08:00 [DEBU] {d471c137580b59188a69f1325dfd5dc7} E:/NaviHotgo/server/internal/logic/admin/site.go:302: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","key","status" FROM "hg_admin_role" WHERE "id"=1 LIMIT 1
2025-08-06T10:06:43.361+08:00 [DEBU] {d471c137580b59188a69f1325dfd5dc7} E:/NaviHotgo/server/internal/logic/admin/site.go:317: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","type","status" FROM "hg_admin_dept" WHERE "id"=100 LIMIT 1
2025-08-06T10:06:43.362+08:00 [DEBU] {d471c137580b59188a69f1325dfd5dc7} E:/NaviHotgo/server/internal/logic/sys/config.go:519: [  0 ms] [default] [hotgo] [rows:0  ] SELECT "id","group","name","type","key","value","default_value","sort","tip","is_default","status","form_type","options","rules","placeholder","created_at","updated_at" FROM "hg_sys_config" WHERE "group"='misc' ORDER BY "sort" ASC,"id" ASC
2025-08-06T10:06:44.039+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  2 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","app_id","member_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('d471c137580b59188a69f1325dfd5dc7','admin',1,'GET','admin','/admin/config/items','{"group":["misc"]}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4; multiple-tabs=true"],"Enter-Time":["2025-08-06 10:06:43"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',4,1754446003,1,'2025-08-06 10:06:43','2025-08-06 10:06:43')  RETURNING "id"
2025-08-06T10:06:44.125+08:00 [DEBU] {18856965580b59188b69f132150f0c97} E:/NaviHotgo/server/internal/logic/admin/site.go:341: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id","dept_id","role_id","real_name","username","password_hash","salt","password_reset_token","integral","balance","avatar","sex","qq","email","mobile","birthday","city_id","address","pid","level","tree","invite_code","cash","last_active_at","remark","status","created_at","updated_at" FROM "hg_admin_member" WHERE "id"=1 LIMIT 1
2025-08-06T10:06:44.126+08:00 [DEBU] {18856965580b59188b69f132150f0c97} E:/NaviHotgo/server/internal/logic/admin/site.go:302: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id","key","status" FROM "hg_admin_role" WHERE "id"=1 LIMIT 1
2025-08-06T10:06:44.126+08:00 [DEBU] {18856965580b59188b69f132150f0c97} E:/NaviHotgo/server/internal/logic/admin/site.go:317: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","type","status" FROM "hg_admin_dept" WHERE "id"=100 LIMIT 1
2025-08-06T10:06:44.127+08:00 [DEBU] {18856965580b59188b69f132150f0c97} E:/NaviHotgo/server/internal/logic/sys/config.go:519: [  1 ms] [default] [hotgo] [rows:9  ] SELECT "id","group","name","type","key","value","default_value","sort","tip","is_default","status","form_type","options","rules","placeholder","created_at","updated_at" FROM "hg_sys_config" WHERE "group"='basic' ORDER BY "sort" ASC,"id" ASC
2025-08-06T10:06:44.128+08:00 [DEBU] {18856965580b59188b69f132150f0c97} E:/NaviHotgo/server/internal/logic/hook/last_active.go:88: [  1 ms] [default] [hotgo] [rows:1  ] UPDATE "hg_admin_member" SET "last_active_at"='2025-08-06 10:06:44',"updated_at"='2025-08-06 10:06:44' WHERE "id"=1
2025-08-06T10:06:45.053+08:00 [DEBU] {8c105ed2420b59189cfe9b4b17c35705} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  2 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","app_id","member_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('18856965580b59188b69f132150f0c97','admin',1,'GET','admin','/admin/config/items','{"group":["basic"]}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4; multiple-tabs=true"],"Enter-Time":["2025-08-06 10:06:44"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',4,1754446004,1,'2025-08-06 10:06:44','2025-08-06 10:06:44')  RETURNING "id"
2025-08-06T10:09:29.389+08:00 [DEBU] {54a418c5430b59186f69f132f67aef2f} E:/NaviHotgo/server/internal/library/addons/install.go:33: [ 62 ms] [default] [hotgo] [rows:0  ] SELECT "id","version","status","created_at","updated_at" FROM "hg_sys_addons_install" WHERE "name"='hgexample' LIMIT 1
2025-08-06T10:12:12.087+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [ 57 ms] [default] [hotgo] [rows:1  ] SELECT version();
2025-08-06T10:12:12.089+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [  1 ms] [default] [hotgo] [rows:39 ] SELECT c.relname FROM pg_class c INNER JOIN pg_namespace n ON c.relnamespace = n.oid WHERE n.nspname = 'public' AND c.relkind IN ('r', 'p') AND c.relpartbound IS NULL ORDER BY c.relname
2025-08-06T10:12:12.097+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [  8 ms] [default] [hotgo] [rows:17 ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_sys_config' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:12:12.098+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [  1 ms] [default] [hotgo] [rows:8  ] SELECT "key","value","type" FROM "hg_sys_config" WHERE "group"='wechat'
2025-08-06T10:12:12.099+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [  0 ms] [default] [hotgo] [rows:14 ] SELECT "key","value","type" FROM "hg_sys_config" WHERE "group"='pay'
2025-08-06T10:12:12.099+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [  0 ms] [default] [hotgo] [rows:35 ] SELECT "key","value","type" FROM "hg_sys_config" WHERE "group"='upload'
2025-08-06T10:12:12.100+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [  0 ms] [default] [hotgo] [rows:15 ] SELECT "key","value","type" FROM "hg_sys_config" WHERE "group"='sms'
2025-08-06T10:12:12.104+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/admin/member.go:855: [  3 ms] [default] [hotgo] [rows:13 ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_admin_role' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:12:12.106+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/admin/member.go:855: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id" FROM "hg_admin_role" WHERE "key"='super' LIMIT 1
2025-08-06T10:12:12.111+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/admin/member.go:866: [  5 ms] [default] [hotgo] [rows:28 ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_admin_member' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:12:12.111+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/admin/member.go:866: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id" FROM "hg_admin_member" WHERE "role_id"='1'
2025-08-06T10:12:12.118+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/cron.go:41: [  4 ms] [default] [hotgo] [rows:13 ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_sys_cron' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:12:12.123+08:00 [DEBU] {2cb70fbea40b5918a83f1f10876bba4e} E:/NaviHotgo/server/internal/library/addons/install.go:33: [  5 ms] [default] [hotgo] [rows:6  ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_sys_addons_install' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:12:12.124+08:00 [DEBU] {2cb70fbea40b5918a83f1f10876bba4e} E:/NaviHotgo/server/internal/library/addons/install.go:33: [  1 ms] [default] [hotgo] [rows:0  ] SELECT "id","version","status","created_at","updated_at" FROM "hg_sys_addons_install" WHERE "name"='hgexample' LIMIT 1
2025-08-06T10:12:12.124+08:00 [DEBU] {2cb70fbea40b5918a83f1f10876bba4e} E:/NaviHotgo/server/internal/library/addons/install.go:33: [  0 ms] [default] [hotgo] [rows:0  ] SELECT "id","version","status","created_at","updated_at" FROM "hg_sys_addons_install" WHERE "name"='hgexample' LIMIT 1
2025-08-06T10:12:12.198+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/cron.go:41: [ 75 ms] [default] [hotgo] [rows:0  ] SELECT "id","group_id","title","name","params","pattern","policy","count","sort","remark","status","created_at","updated_at" FROM "hg_sys_cron" WHERE "status"=1 ORDER BY "sort" asc,"id" desc
2025-08-06T10:12:12.247+08:00 [DEBU] {c0e1a8caa40b5918b13f1f101be78551} E:/NaviHotgo/server/internal/library/casbin/enforcer.go:116: [  7 ms] [default] [hotgo] [rows:2  ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_admin_role_menu' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:12:12.252+08:00 [DEBU] {9cca15cba40b5918b23f1f100ae4b9d3} E:/NaviHotgo/server/internal/library/casbin/enforcer.go:116: [  5 ms] [default] [hotgo] [rows:35 ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_admin_menu' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:12:12.254+08:00 [DEBU] {5c3e6ecba40b5918b33f1f10779f011c} E:/NaviHotgo/server/internal/library/casbin/enforcer.go:116: [  2 ms] [default] [hotgo] [rows:305] SELECT r.key,m.permissions FROM "hg_admin_role" r LEFT JOIN "hg_admin_role_menu" rm ON (r.id=rm.role_id) LEFT JOIN "hg_admin_menu" m ON (rm.menu_id=m.id) WHERE ("r"."status"=1) AND ("m"."status"=1) AND (m.permissions != '') AND (r.key != 'super')
2025-08-06T10:12:12.259+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/blacklist.go:133: [  4 ms] [default] [hotgo] [rows:6  ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_sys_blacklist' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:12:12.260+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/blacklist.go:135: [  1 ms] [default] [hotgo] [rows:4  ] SELECT "ip" FROM "hg_sys_blacklist" WHERE "status"=1
2025-08-06T10:12:17.207+08:00 [DEBU] {341470f2a50b5918b53f1f108183bbad} E:/NaviHotgo/server/internal/logic/tcpserver/server_handle.go:37: [  4 ms] [default] [hotgo] [rows:17 ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_sys_serve_license' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:12:17.208+08:00 [DEBU] {341470f2a50b5918b53f1f108183bbad} E:/NaviHotgo/server/internal/logic/tcpserver/server_handle.go:37: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","group","name","appid","secret_key","remote_addr","online_limit","login_times","last_login_at","last_active_at","routes","allowed_ips","end_at","remark","status","created_at","updated_at" FROM "hg_sys_serve_license" WHERE "appid"='1002' LIMIT 1
2025-08-06T10:12:17.211+08:00 [DEBU] {341470f2a50b5918b53f1f108183bbad} E:/NaviHotgo/server/internal/logic/tcpserver/server_handle.go:124: [  2 ms] [default] [hotgo] [rows:1  ] UPDATE "hg_sys_serve_license" SET "remote_addr"='127.0.0.1:54625',"login_times"=301,"last_login_at"='2025-08-06 10:12:17',"last_active_at"='2025-08-06 10:12:17',"updated_at"='2025-08-06 10:12:17' WHERE "id"=1
2025-08-06T10:14:10.130+08:00 [DEBU] {38bfdc39c00b5918b93f1f100885c4cf} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [ 59 ms] [default] [hotgo] [rows:9  ] SELECT "key","value","type" FROM "hg_sys_config" WHERE "group"='basic'
2025-08-06T10:14:10.149+08:00 [DEBU] {04a9863ec00b5918ba3f1f10c7107de8} E:/NaviHotgo/server/internal/logic/admin/site.go:341: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id","dept_id","role_id","real_name","username","password_hash","salt","password_reset_token","integral","balance","avatar","sex","qq","email","mobile","birthday","city_id","address","pid","level","tree","invite_code","cash","last_active_at","remark","status","created_at","updated_at" FROM "hg_admin_member" WHERE "id"=1 LIMIT 1
2025-08-06T10:14:10.150+08:00 [DEBU] {04a9863ec00b5918ba3f1f10c7107de8} E:/NaviHotgo/server/internal/logic/admin/site.go:302: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id","key","status" FROM "hg_admin_role" WHERE "id"=1 LIMIT 1
2025-08-06T10:14:10.158+08:00 [DEBU] {04a9863ec00b5918ba3f1f10c7107de8} E:/NaviHotgo/server/internal/logic/admin/site.go:317: [  8 ms] [default] [hotgo] [rows:14 ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_admin_dept' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:14:10.158+08:00 [DEBU] {04a9863ec00b5918ba3f1f10c7107de8} E:/NaviHotgo/server/internal/logic/admin/site.go:317: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","type","status" FROM "hg_admin_dept" WHERE "id"=100 LIMIT 1
2025-08-06T10:14:10.161+08:00 [DEBU] {04a9863ec00b5918ba3f1f10c7107de8} E:/NaviHotgo/server/internal/logic/admin/menu.go:315: [  2 ms] [default] [hotgo] [rows:65 ] SELECT "id","pid","level","tree","title","name","path","icon","type","redirect","permissions","permission_name","component","always_show","active_menu","is_root","is_frame","frame_src","keep_alive","hidden","affix","sort","remark","status","extra_icon","show_parent","roles","frame_loading","transition_name","transition_enter","transition_leave","hidden_tag","dynamic_level","updated_at","created_at" FROM "hg_admin_menu" WHERE ("status"=1) AND ("type" IN (1,2)) ORDER BY "sort","id" desc
2025-08-06T10:14:10.164+08:00 [DEBU] {04a9863ec00b5918ba3f1f10c7107de8} E:/NaviHotgo/server/internal/logic/hook/last_active.go:88: [  2 ms] [default] [hotgo] [rows:1  ] UPDATE "hg_admin_member" SET "last_active_at"='2025-08-06 10:14:10',"updated_at"='2025-08-06 10:14:10' WHERE "id"=1
2025-08-06T10:14:11.122+08:00 [DEBU] {8c1f8578c00b5918bb3f1f105a1e80b9} E:/NaviHotgo/server/internal/logic/admin/site.go:341: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","dept_id","role_id","real_name","username","password_hash","salt","password_reset_token","integral","balance","avatar","sex","qq","email","mobile","birthday","city_id","address","pid","level","tree","invite_code","cash","last_active_at","remark","status","created_at","updated_at" FROM "hg_admin_member" WHERE "id"=1 LIMIT 1
2025-08-06T10:14:11.122+08:00 [DEBU] {8c1f8578c00b5918bb3f1f105a1e80b9} E:/NaviHotgo/server/internal/logic/admin/site.go:302: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","key","status" FROM "hg_admin_role" WHERE "id"=1 LIMIT 1
2025-08-06T10:14:11.123+08:00 [DEBU] {8c1f8578c00b5918bb3f1f105a1e80b9} E:/NaviHotgo/server/internal/logic/admin/site.go:317: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","type","status" FROM "hg_admin_dept" WHERE "id"=100 LIMIT 1
2025-08-06T10:14:11.124+08:00 [DEBU] {8c1f8578c00b5918bb3f1f105a1e80b9} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "key","value","type" FROM "hg_sys_config" WHERE "group"='system'
2025-08-06T10:14:11.165+08:00 [DEBU] {b4d4187bc00b5918bc3f1f107153d001} E:/NaviHotgo/server/internal/logic/admin/site.go:341: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id","dept_id","role_id","real_name","username","password_hash","salt","password_reset_token","integral","balance","avatar","sex","qq","email","mobile","birthday","city_id","address","pid","level","tree","invite_code","cash","last_active_at","remark","status","created_at","updated_at" FROM "hg_admin_member" WHERE "id"=1 LIMIT 1
2025-08-06T10:14:11.165+08:00 [DEBU] {b4d4187bc00b5918bc3f1f107153d001} E:/NaviHotgo/server/internal/logic/admin/site.go:302: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","key","status" FROM "hg_admin_role" WHERE "id"=1 LIMIT 1
2025-08-06T10:14:11.166+08:00 [DEBU] {b4d4187bc00b5918bc3f1f107153d001} E:/NaviHotgo/server/internal/logic/admin/site.go:317: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id","type","status" FROM "hg_admin_dept" WHERE "id"=100 LIMIT 1
2025-08-06T10:14:11.166+08:00 [DEBU] {b4d4187bc00b5918bc3f1f107153d001} E:/NaviHotgo/server/internal/logic/sys/config.go:519: [  0 ms] [default] [hotgo] [rows:9  ] SELECT "id","group","name","type","key","value","default_value","sort","tip","is_default","status","form_type","options","rules","placeholder","created_at","updated_at" FROM "hg_sys_config" WHERE "group"='basic' ORDER BY "sort" ASC,"id" ASC
2025-08-06T10:14:11.173+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  5 ms] [default] [hotgo] [rows:23 ] SELECT a.attname AS field, t.typname AS type,a.attnotnull as null, (case when d.contype = 'p' then 'pri' when d.contype = 'u' then 'uni' else '' end) as key ,ic.column_default as default_value,b.description as comment ,coalesce(character_maximum_length, numeric_precision, -1) as length ,numeric_scale as scale FROM pg_attribute a left join pg_class c on a.attrelid = c.oid left join pg_constraint d on d.conrelid = c.oid and a.attnum = d.conkey[1] left join pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid left join pg_type t ON a.atttypid = t.oid left join information_schema.columns ic on ic.column_name = a.attname and ic.table_name = c.relname WHERE c.relname = 'hg_sys_log' and a.attisdropped is false and a.attnum > 0 ORDER BY a.attnum
2025-08-06T10:14:11.176+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  2 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('38bfdc39c00b5918b93f1f100885c4cf','GET','admin','/admin/site/config','{}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4; multiple-tabs=true"],"Enter-Time":["2025-08-06 10:14:10"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',60,1754446450,1,'2025-08-06 10:14:10','2025-08-06 10:14:10')  RETURNING "id"
2025-08-06T10:14:11.189+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  1 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","app_id","member_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('04a9863ec00b5918ba3f1f10c7107de8','admin',1,'GET','admin','/admin/role/dynamic','{}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4; multiple-tabs=true"],"Enter-Time":["2025-08-06 10:14:10"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',13,1754446450,1,'2025-08-06 10:14:10','2025-08-06 10:14:10')  RETURNING "id"
2025-08-06T10:14:11.201+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  1 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","app_id","member_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('8c1f8578c00b5918bb3f1f105a1e80b9','admin',1,'GET','admin','/admin/config/groups','{}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4; multiple-tabs=true"],"Enter-Time":["2025-08-06 10:14:11"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',3,**********,1,'2025-08-06 10:14:11','2025-08-06 10:14:11')  RETURNING "id"
2025-08-06T10:14:12.216+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  3 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","app_id","member_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('b4d4187bc00b5918bc3f1f107153d001','admin',1,'GET','admin','/admin/config/items','{"group":["basic"]}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4; multiple-tabs=true"],"Enter-Time":["2025-08-06 10:14:11"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',2,**********,1,'2025-08-06 10:14:11','2025-08-06 10:14:11')  RETURNING "id"
2025-08-06T10:17:17.313+08:00 [DEBU] {00efb8cbeb0b5918bd3f1f10e1bade17} E:/NaviHotgo/server/internal/logic/tcpserver/server_handle.go:160: [112 ms] [default] [hotgo] [rows:1  ] UPDATE "hg_sys_serve_license" SET "last_active_at"='2025-08-06 10:17:17',"updated_at"='2025-08-06 10:17:17' WHERE "appid"='1002'
2025-08-06T10:19:30.919+08:00 [DEBU] {bc6e2ce40a0c5918c03f1f10ce6356dc} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [165 ms] [default] [hotgo] [rows:9  ] SELECT "key","value","type" FROM "hg_sys_config" WHERE "group"='basic'
2025-08-06T10:19:30.944+08:00 [DEBU] {44b15bef0a0c5918c13f1f102f5f7584} E:/NaviHotgo/server/internal/logic/admin/site.go:341: [  2 ms] [default] [hotgo] [rows:1  ] SELECT "id","dept_id","role_id","real_name","username","password_hash","salt","password_reset_token","integral","balance","avatar","sex","qq","email","mobile","birthday","city_id","address","pid","level","tree","invite_code","cash","last_active_at","remark","status","created_at","updated_at" FROM "hg_admin_member" WHERE "id"=1 LIMIT 1
2025-08-06T10:19:30.946+08:00 [DEBU] {44b15bef0a0c5918c13f1f102f5f7584} E:/NaviHotgo/server/internal/logic/admin/site.go:302: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id","key","status" FROM "hg_admin_role" WHERE "id"=1 LIMIT 1
2025-08-06T10:19:30.946+08:00 [DEBU] {44b15bef0a0c5918c13f1f102f5f7584} E:/NaviHotgo/server/internal/logic/admin/site.go:317: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","type","status" FROM "hg_admin_dept" WHERE "id"=100 LIMIT 1
2025-08-06T10:19:30.955+08:00 [DEBU] {44b15bef0a0c5918c13f1f102f5f7584} E:/NaviHotgo/server/internal/logic/admin/menu.go:315: [  7 ms] [default] [hotgo] [rows:65 ] SELECT "id","pid","level","tree","title","name","path","icon","type","redirect","permissions","permission_name","component","always_show","active_menu","is_root","is_frame","frame_src","keep_alive","hidden","affix","sort","remark","status","extra_icon","show_parent","roles","frame_loading","transition_name","transition_enter","transition_leave","hidden_tag","dynamic_level","updated_at","created_at" FROM "hg_admin_menu" WHERE ("status"=1) AND ("type" IN (1,2)) ORDER BY "sort","id" desc
2025-08-06T10:19:30.957+08:00 [DEBU] {44b15bef0a0c5918c13f1f102f5f7584} E:/NaviHotgo/server/internal/logic/hook/last_active.go:88: [  1 ms] [default] [hotgo] [rows:1  ] UPDATE "hg_admin_member" SET "last_active_at"='2025-08-06 10:19:30',"updated_at"='2025-08-06 10:19:30' WHERE "id"=1
2025-08-06T10:19:31.293+08:00 [DEBU] {e8cc31040b0c5918c23f1f1000253e35} E:/NaviHotgo/server/internal/logic/admin/site.go:341: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id","dept_id","role_id","real_name","username","password_hash","salt","password_reset_token","integral","balance","avatar","sex","qq","email","mobile","birthday","city_id","address","pid","level","tree","invite_code","cash","last_active_at","remark","status","created_at","updated_at" FROM "hg_admin_member" WHERE "id"=1 LIMIT 1
2025-08-06T10:19:31.294+08:00 [DEBU] {e8cc31040b0c5918c23f1f1000253e35} E:/NaviHotgo/server/internal/logic/admin/site.go:302: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","key","status" FROM "hg_admin_role" WHERE "id"=1 LIMIT 1
2025-08-06T10:19:31.294+08:00 [DEBU] {e8cc31040b0c5918c23f1f1000253e35} E:/NaviHotgo/server/internal/logic/admin/site.go:317: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","type","status" FROM "hg_admin_dept" WHERE "id"=100 LIMIT 1
2025-08-06T10:19:31.296+08:00 [DEBU] {e8cc31040b0c5918c23f1f1000253e35} E:/NaviHotgo/server/internal/logic/sys/config.go:212: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "key","value","type" FROM "hg_sys_config" WHERE "group"='system'
2025-08-06T10:19:31.326+08:00 [DEBU] {601727060b0c5918c33f1f107a306ca4} E:/NaviHotgo/server/internal/logic/admin/site.go:341: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id","dept_id","role_id","real_name","username","password_hash","salt","password_reset_token","integral","balance","avatar","sex","qq","email","mobile","birthday","city_id","address","pid","level","tree","invite_code","cash","last_active_at","remark","status","created_at","updated_at" FROM "hg_admin_member" WHERE "id"=1 LIMIT 1
2025-08-06T10:19:31.326+08:00 [DEBU] {601727060b0c5918c33f1f107a306ca4} E:/NaviHotgo/server/internal/logic/admin/site.go:302: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","key","status" FROM "hg_admin_role" WHERE "id"=1 LIMIT 1
2025-08-06T10:19:31.327+08:00 [DEBU] {601727060b0c5918c33f1f107a306ca4} E:/NaviHotgo/server/internal/logic/admin/site.go:317: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","type","status" FROM "hg_admin_dept" WHERE "id"=100 LIMIT 1
2025-08-06T10:19:31.328+08:00 [DEBU] {601727060b0c5918c33f1f107a306ca4} E:/NaviHotgo/server/internal/logic/sys/config.go:519: [  1 ms] [default] [hotgo] [rows:9  ] SELECT "id","group","name","type","key","value","default_value","sort","tip","is_default","status","form_type","options","rules","placeholder","created_at","updated_at" FROM "hg_sys_config" WHERE "group"='basic' ORDER BY "sort" ASC,"id" ASC
2025-08-06T10:19:31.424+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  2 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('bc6e2ce40a0c5918c03f1f10ce6356dc','GET','admin','/admin/site/config','{}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4; multiple-tabs=true"],"Enter-Time":["2025-08-06 10:19:30"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',166,1754446770,1,'2025-08-06 10:19:30','2025-08-06 10:19:30')  RETURNING "id"
2025-08-06T10:19:31.438+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  1 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","app_id","member_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('44b15bef0a0c5918c13f1f102f5f7584','admin',1,'GET','admin','/admin/role/dynamic','{}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4; multiple-tabs=true"],"Enter-Time":["2025-08-06 10:19:30"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',13,1754446770,1,'2025-08-06 10:19:30','2025-08-06 10:19:30')  RETURNING "id"
2025-08-06T10:19:32.451+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  2 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","app_id","member_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('e8cc31040b0c5918c23f1f1000253e35','admin',1,'GET','admin','/admin/config/groups','{}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4; multiple-tabs=true"],"Enter-Time":["2025-08-06 10:19:31"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',3,1754446771,1,'2025-08-06 10:19:31','2025-08-06 10:19:31')  RETURNING "id"
2025-08-06T10:19:32.465+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  2 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","app_id","member_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('601727060b0c5918c33f1f107a306ca4','admin',1,'GET','admin','/admin/config/items','{"group":["basic"]}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4; multiple-tabs=true"],"Enter-Time":["2025-08-06 10:19:31"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',4,1754446771,1,'2025-08-06 10:19:31','2025-08-06 10:19:31')  RETURNING "id"
2025-08-06T10:19:35.570+08:00 [DEBU] {b0572f030c0c5918c43f1f10d5728847} E:/NaviHotgo/server/internal/logic/admin/site.go:341: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","dept_id","role_id","real_name","username","password_hash","salt","password_reset_token","integral","balance","avatar","sex","qq","email","mobile","birthday","city_id","address","pid","level","tree","invite_code","cash","last_active_at","remark","status","created_at","updated_at" FROM "hg_admin_member" WHERE "id"=1 LIMIT 1
2025-08-06T10:19:35.572+08:00 [DEBU] {b0572f030c0c5918c43f1f10d5728847} E:/NaviHotgo/server/internal/logic/admin/site.go:302: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","key","status" FROM "hg_admin_role" WHERE "id"=1 LIMIT 1
2025-08-06T10:19:35.573+08:00 [DEBU] {b0572f030c0c5918c43f1f10d5728847} E:/NaviHotgo/server/internal/logic/admin/site.go:317: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","type","status" FROM "hg_admin_dept" WHERE "id"=100 LIMIT 1
2025-08-06T10:19:35.574+08:00 [DEBU] {b0572f030c0c5918c43f1f10d5728847} E:/NaviHotgo/server/internal/logic/sys/config.go:519: [  1 ms] [default] [hotgo] [rows:0  ] SELECT "id","group","name","type","key","value","default_value","sort","tip","is_default","status","form_type","options","rules","placeholder","created_at","updated_at" FROM "hg_sys_config" WHERE "group"='email' ORDER BY "sort" ASC,"id" ASC
2025-08-06T10:19:35.577+08:00 [DEBU] {b0572f030c0c5918c43f1f10d5728847} E:/NaviHotgo/server/internal/logic/hook/last_active.go:88: [  3 ms] [default] [hotgo] [rows:1  ] UPDATE "hg_admin_member" SET "last_active_at"='2025-08-06 10:19:35',"updated_at"='2025-08-06 10:19:35' WHERE "id"=1
2025-08-06T10:19:36.419+08:00 [DEBU] {0c68d6350c0c5918c53f1f10e0a598b5} E:/NaviHotgo/server/internal/logic/admin/site.go:341: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","dept_id","role_id","real_name","username","password_hash","salt","password_reset_token","integral","balance","avatar","sex","qq","email","mobile","birthday","city_id","address","pid","level","tree","invite_code","cash","last_active_at","remark","status","created_at","updated_at" FROM "hg_admin_member" WHERE "id"=1 LIMIT 1
2025-08-06T10:19:36.422+08:00 [DEBU] {0c68d6350c0c5918c53f1f10e0a598b5} E:/NaviHotgo/server/internal/logic/admin/site.go:302: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","key","status" FROM "hg_admin_role" WHERE "id"=1 LIMIT 1
2025-08-06T10:19:36.423+08:00 [DEBU] {0c68d6350c0c5918c53f1f10e0a598b5} E:/NaviHotgo/server/internal/logic/admin/site.go:317: [  0 ms] [default] [hotgo] [rows:1  ] SELECT "id","type","status" FROM "hg_admin_dept" WHERE "id"=100 LIMIT 1
2025-08-06T10:19:36.424+08:00 [DEBU] {0c68d6350c0c5918c53f1f10e0a598b5} E:/NaviHotgo/server/internal/logic/sys/config.go:519: [  0 ms] [default] [hotgo] [rows:0  ] SELECT "id","group","name","type","key","value","default_value","sort","tip","is_default","status","form_type","options","rules","placeholder","created_at","updated_at" FROM "hg_sys_config" WHERE "group"='misc' ORDER BY "sort" ASC,"id" ASC
2025-08-06T10:19:36.481+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  3 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","app_id","member_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('b0572f030c0c5918c43f1f10d5728847','admin',1,'GET','admin','/admin/config/items','{"group":["email"]}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4; multiple-tabs=true"],"Enter-Time":["2025-08-06 10:19:35"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',4,1754446775,1,'2025-08-06 10:19:35','2025-08-06 10:19:35')  RETURNING "id"
2025-08-06T10:19:37.339+08:00 [DEBU] {740b946c0c0c5918c63f1f103268cbbd} E:/NaviHotgo/server/internal/logic/admin/site.go:341: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id","dept_id","role_id","real_name","username","password_hash","salt","password_reset_token","integral","balance","avatar","sex","qq","email","mobile","birthday","city_id","address","pid","level","tree","invite_code","cash","last_active_at","remark","status","created_at","updated_at" FROM "hg_admin_member" WHERE "id"=1 LIMIT 1
2025-08-06T10:19:37.340+08:00 [DEBU] {740b946c0c0c5918c63f1f103268cbbd} E:/NaviHotgo/server/internal/logic/admin/site.go:302: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id","key","status" FROM "hg_admin_role" WHERE "id"=1 LIMIT 1
2025-08-06T10:19:37.341+08:00 [DEBU] {740b946c0c0c5918c63f1f103268cbbd} E:/NaviHotgo/server/internal/logic/admin/site.go:317: [  1 ms] [default] [hotgo] [rows:1  ] SELECT "id","type","status" FROM "hg_admin_dept" WHERE "id"=100 LIMIT 1
2025-08-06T10:19:37.342+08:00 [DEBU] {740b946c0c0c5918c63f1f103268cbbd} E:/NaviHotgo/server/internal/logic/sys/config.go:519: [  1 ms] [default] [hotgo] [rows:9  ] SELECT "id","group","name","type","key","value","default_value","sort","tip","is_default","status","form_type","options","rules","placeholder","created_at","updated_at" FROM "hg_sys_config" WHERE "group"='basic' ORDER BY "sort" ASC,"id" ASC
2025-08-06T10:19:37.499+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  3 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","app_id","member_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('0c68d6350c0c5918c53f1f10e0a598b5','admin',1,'GET','admin','/admin/config/items','{"group":["misc"]}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4; multiple-tabs=true"],"Enter-Time":["2025-08-06 10:19:36"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',5,1754446776,1,'2025-08-06 10:19:36','2025-08-06 10:19:36')  RETURNING "id"
2025-08-06T10:19:38.512+08:00 [DEBU] {e854eb3da40b5918e47bc0329002d02a} E:/NaviHotgo/server/internal/logic/sys/log.go:111: [  2 ms] [default] [hotgo] [rows:1  ] INSERT INTO "hg_sys_log"("req_id","app_id","member_id","method","module","url","get_data","post_data","header_data","ip","error_msg","error_data","user_agent","take_up_time","timestamp","status","created_at","updated_at") VALUES('740b946c0c0c5918c63f1f103268cbbd','admin',1,'GET','admin','/admin/config/items','{"group":["basic"]}','{}','{"Accept":["application/json, text/plain, */*"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"],"Connection":["close"],"Cookie":["gfsessionid=18477688e6cc58187f2a8c6c490e57f4; multiple-tabs=true"],"Enter-Time":["2025-08-06 10:19:37"],"Referer":["http://localhost:8848/"],"Sec-Ch-Ua":["\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\""],"Sec-Ch-Ua-Mobile":["?0"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Dest":["empty"],"Sec-Fetch-Mode":["cors"],"Sec-Fetch-Site":["same-origin"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"],"X-Requested-With":["XMLHttpRequest"]}','::1','操作成功','{}','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',4,1754446777,1,'2025-08-06 10:19:37','2025-08-06 10:19:37')  RETURNING "id"
