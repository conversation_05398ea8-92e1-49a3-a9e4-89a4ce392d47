import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/.pnpm/weixin-js-sdk@1.6.5/node_modules/weixin-js-sdk/index.js
var require_weixin_js_sdk = __commonJS({
  "node_modules/.pnpm/weixin-js-sdk@1.6.5/node_modules/weixin-js-sdk/index.js"(exports, module) {
    !function(e, n) {
      module.exports = n(e);
    }(typeof window === "object" && window, function(r, e) {
      if (!r) {
        console.warn("can't use weixin-js-sdk in server side");
        return;
      }
      var a, c, n, i, t, o, s, d, l, u, p, f, m, g, h, S, y, I, v, _, w, T;
      if (!r.jWeixin)
        return a = {
          config: "preVerifyJSAPI",
          onMenuShareTimeline: "menu:share:timeline",
          onMenuShareAppMessage: "menu:share:appmessage",
          onMenuShareQQ: "menu:share:qq",
          onMenuShareWeibo: "menu:share:weiboApp",
          onMenuShareQZone: "menu:share:QZone",
          previewImage: "imagePreview",
          getLocation: "geoLocation",
          openProductSpecificView: "openProductViewWithPid",
          addCard: "batchAddCard",
          openCard: "batchViewCard",
          chooseWXPay: "getBrandWCPayRequest",
          openEnterpriseRedPacket: "getRecevieBizHongBaoRequest",
          startSearchBeacons: "startMonitoringBeacons",
          stopSearchBeacons: "stopMonitoringBeacons",
          onSearchBeacons: "onBeaconsInRange",
          consumeAndShareCard: "consumedShareCard",
          openAddress: "editAddress"
        }, c = function() {
          var e2, n2 = {};
          for (e2 in a) n2[a[e2]] = e2;
          return n2;
        }(), n = r.document, i = n.title, t = navigator.userAgent.toLowerCase(), f = navigator.platform.toLowerCase(), o = !(!f.match("mac") && !f.match("win")), s = -1 != t.indexOf("wxdebugger"), d = -1 != t.indexOf("micromessenger"), l = -1 != t.indexOf("android"), u = -1 != t.indexOf("iphone") || -1 != t.indexOf("ipad"), p = (f = t.match(/micromessenger\/(\d+\.\d+\.\d+)/) || t.match(/micromessenger\/(\d+\.\d+)/)) ? f[1] : "", m = {
          initStartTime: L(),
          initEndTime: 0,
          preVerifyStartTime: 0,
          preVerifyEndTime: 0
        }, g = {
          version: 1,
          appId: "",
          initTime: 0,
          preVerifyTime: 0,
          networkType: "",
          isPreVerifyOk: 1,
          systemType: u ? 1 : l ? 2 : -1,
          clientVersion: p,
          url: encodeURIComponent(location.href)
        }, h = {}, S = { _completes: [] }, y = { state: 0, data: {} }, O(function() {
          m.initEndTime = L();
        }), I = false, v = [], _ = {
          config: function(e2) {
            C("config", h = e2);
            var o2 = false !== h.check;
            O(function() {
              if (o2)
                k(
                  a.config,
                  {
                    verifyJsApiList: A(h.jsApiList),
                    verifyOpenTagList: A(h.openTagList)
                  },
                  (S._complete = function(e4) {
                    m.preVerifyEndTime = L(), y.state = 1, y.data = e4;
                  }, S.success = function(e4) {
                    g.isPreVerifyOk = 0;
                  }, S.fail = function(e4) {
                    S._fail ? S._fail(e4) : y.state = -1;
                  }, (t2 = S._completes).push(function() {
                    B();
                  }), S.complete = function(e4) {
                    for (var n3 = 0, i3 = t2.length; n3 < i3; ++n3) t2[n3]();
                    S._completes = [];
                  }, S)
                ), m.preVerifyStartTime = L();
              else {
                y.state = 1;
                for (var e3 = S._completes, n2 = 0, i2 = e3.length; n2 < i2; ++n2)
                  e3[n2]();
                S._completes = [];
              }
              var t2;
            }), _.invoke || (_.invoke = function(e3, n2, i2) {
              r.WeixinJSBridge && WeixinJSBridge.invoke(e3, P(n2), i2);
            }, _.on = function(e3, n2) {
              r.WeixinJSBridge && WeixinJSBridge.on(e3, n2);
            });
          },
          ready: function(e2) {
            (0 != y.state || (S._completes.push(e2), !d && h.debug)) && e2();
          },
          error: function(e2) {
            p < "6.0.2" || (-1 == y.state ? e2(y.data) : S._fail = e2);
          },
          checkJsApi: function(e2) {
            k(
              "checkJsApi",
              { jsApiList: A(e2.jsApiList) },
              (e2._complete = function(e3) {
                l && (i2 = e3.checkResult) && (e3.checkResult = JSON.parse(i2));
                var n2, i2 = e3, t2 = i2.checkResult;
                for (n2 in t2) {
                  var o2 = c[n2];
                  o2 && (t2[o2] = t2[n2], delete t2[n2]);
                }
              }, e2)
            );
          },
          onMenuShareTimeline: function(e2) {
            M(
              a.onMenuShareTimeline,
              {
                complete: function() {
                  k(
                    "shareTimeline",
                    {
                      title: e2.title || i,
                      desc: e2.title || i,
                      img_url: e2.imgUrl || "",
                      link: e2.link || location.href,
                      type: e2.type || "link",
                      data_url: e2.dataUrl || ""
                    },
                    e2
                  );
                }
              },
              e2
            );
          },
          onMenuShareAppMessage: function(n2) {
            M(
              a.onMenuShareAppMessage,
              {
                complete: function(e2) {
                  "favorite" === e2.scene ? k("sendAppMessage", {
                    title: n2.title || i,
                    desc: n2.desc || "",
                    link: n2.link || location.href,
                    img_url: n2.imgUrl || "",
                    type: n2.type || "link",
                    data_url: n2.dataUrl || ""
                  }) : k(
                    "sendAppMessage",
                    {
                      title: n2.title || i,
                      desc: n2.desc || "",
                      link: n2.link || location.href,
                      img_url: n2.imgUrl || "",
                      type: n2.type || "link",
                      data_url: n2.dataUrl || ""
                    },
                    n2
                  );
                }
              },
              n2
            );
          },
          onMenuShareQQ: function(e2) {
            M(
              a.onMenuShareQQ,
              {
                complete: function() {
                  k(
                    "shareQQ",
                    {
                      title: e2.title || i,
                      desc: e2.desc || "",
                      img_url: e2.imgUrl || "",
                      link: e2.link || location.href
                    },
                    e2
                  );
                }
              },
              e2
            );
          },
          onMenuShareWeibo: function(e2) {
            M(
              a.onMenuShareWeibo,
              {
                complete: function() {
                  k(
                    "shareWeiboApp",
                    {
                      title: e2.title || i,
                      desc: e2.desc || "",
                      img_url: e2.imgUrl || "",
                      link: e2.link || location.href
                    },
                    e2
                  );
                }
              },
              e2
            );
          },
          onMenuShareQZone: function(e2) {
            M(
              a.onMenuShareQZone,
              {
                complete: function() {
                  k(
                    "shareQZone",
                    {
                      title: e2.title || i,
                      desc: e2.desc || "",
                      img_url: e2.imgUrl || "",
                      link: e2.link || location.href
                    },
                    e2
                  );
                }
              },
              e2
            );
          },
          updateTimelineShareData: function(e2) {
            k(
              "updateTimelineShareData",
              { title: e2.title, link: e2.link, imgUrl: e2.imgUrl },
              e2
            );
          },
          updateAppMessageShareData: function(e2) {
            k(
              "updateAppMessageShareData",
              { title: e2.title, desc: e2.desc, link: e2.link, imgUrl: e2.imgUrl },
              e2
            );
          },
          startRecord: function(e2) {
            k("startRecord", {}, e2);
          },
          stopRecord: function(e2) {
            k("stopRecord", {}, e2);
          },
          onVoiceRecordEnd: function(e2) {
            M("onVoiceRecordEnd", e2);
          },
          playVoice: function(e2) {
            k("playVoice", { localId: e2.localId }, e2);
          },
          pauseVoice: function(e2) {
            k("pauseVoice", { localId: e2.localId }, e2);
          },
          stopVoice: function(e2) {
            k("stopVoice", { localId: e2.localId }, e2);
          },
          onVoicePlayEnd: function(e2) {
            M("onVoicePlayEnd", e2);
          },
          uploadVoice: function(e2) {
            k(
              "uploadVoice",
              {
                localId: e2.localId,
                isShowProgressTips: 0 == e2.isShowProgressTips ? 0 : 1
              },
              e2
            );
          },
          downloadVoice: function(e2) {
            k(
              "downloadVoice",
              {
                serverId: e2.serverId,
                isShowProgressTips: 0 == e2.isShowProgressTips ? 0 : 1
              },
              e2
            );
          },
          translateVoice: function(e2) {
            k(
              "translateVoice",
              {
                localId: e2.localId,
                isShowProgressTips: 0 == e2.isShowProgressTips ? 0 : 1
              },
              e2
            );
          },
          chooseImage: function(e2) {
            k(
              "chooseImage",
              {
                scene: "1|2",
                count: e2.count || 9,
                sizeType: e2.sizeType || ["original", "compressed"],
                sourceType: e2.sourceType || ["album", "camera"]
              },
              (e2._complete = function(e3) {
                if (l) {
                  var n2 = e3.localIds;
                  try {
                    n2 && (e3.localIds = JSON.parse(n2));
                  } catch (e4) {
                  }
                }
              }, e2)
            );
          },
          getLocation: function(e2) {
            e2 = e2 || {}, k(
              a.getLocation,
              { type: e2.type || "wgs84" },
              (e2._complete = function(e3) {
                delete e3.type;
              }, e2)
            );
          },
          previewImage: function(e2) {
            k(a.previewImage, { current: e2.current, urls: e2.urls }, e2);
          },
          uploadImage: function(e2) {
            k(
              "uploadImage",
              {
                localId: e2.localId,
                isShowProgressTips: 0 == e2.isShowProgressTips ? 0 : 1
              },
              e2
            );
          },
          downloadImage: function(e2) {
            k(
              "downloadImage",
              {
                serverId: e2.serverId,
                isShowProgressTips: 0 == e2.isShowProgressTips ? 0 : 1
              },
              e2
            );
          },
          getLocalImgData: function(e2) {
            false === I ? (I = true, k(
              "getLocalImgData",
              { localId: e2.localId },
              (e2._complete = function(e3) {
                var n2;
                I = false, 0 < v.length && (n2 = v.shift(), wx.getLocalImgData(n2));
              }, e2)
            )) : v.push(e2);
          },
          getNetworkType: function(e2) {
            k(
              "getNetworkType",
              {},
              (e2._complete = function(e3) {
                var n2 = e3, e3 = n2.errMsg, i2 = (n2.errMsg = "getNetworkType:ok", n2.subtype);
                if (delete n2.subtype, i2) n2.networkType = i2;
                else {
                  var i2 = e3.indexOf(":"), t2 = e3.substring(i2 + 1);
                  switch (t2) {
                    case "wifi":
                    case "edge":
                    case "wwan":
                      n2.networkType = t2;
                      break;
                    default:
                      n2.errMsg = "getNetworkType:fail";
                  }
                }
              }, e2)
            );
          },
          openLocation: function(e2) {
            k(
              "openLocation",
              {
                latitude: e2.latitude,
                longitude: e2.longitude,
                name: e2.name || "",
                address: e2.address || "",
                scale: e2.scale || 28,
                infoUrl: e2.infoUrl || ""
              },
              e2
            );
          },
          hideOptionMenu: function(e2) {
            k("hideOptionMenu", {}, e2);
          },
          showOptionMenu: function(e2) {
            k("showOptionMenu", {}, e2);
          },
          closeWindow: function(e2) {
            k("closeWindow", {}, e2 = e2 || {});
          },
          hideMenuItems: function(e2) {
            k("hideMenuItems", { menuList: e2.menuList }, e2);
          },
          showMenuItems: function(e2) {
            k("showMenuItems", { menuList: e2.menuList }, e2);
          },
          hideAllNonBaseMenuItem: function(e2) {
            k("hideAllNonBaseMenuItem", {}, e2);
          },
          showAllNonBaseMenuItem: function(e2) {
            k("showAllNonBaseMenuItem", {}, e2);
          },
          scanQRCode: function(e2) {
            k(
              "scanQRCode",
              {
                needResult: (e2 = e2 || {}).needResult || 0,
                scanType: e2.scanType || ["qrCode", "barCode"]
              },
              (e2._complete = function(e3) {
                var n2;
                u && (n2 = e3.resultStr) && (n2 = JSON.parse(n2), e3.resultStr = n2 && n2.scan_code && n2.scan_code.scan_result);
              }, e2)
            );
          },
          openAddress: function(e2) {
            k(
              a.openAddress,
              {},
              (e2._complete = function(e3) {
                (e3 = e3).postalCode = e3.addressPostalCode, delete e3.addressPostalCode, e3.provinceName = e3.proviceFirstStageName, delete e3.proviceFirstStageName, e3.cityName = e3.addressCitySecondStageName, delete e3.addressCitySecondStageName, e3.countryName = e3.addressCountiesThirdStageName, delete e3.addressCountiesThirdStageName, e3.detailInfo = e3.addressDetailInfo, delete e3.addressDetailInfo;
              }, e2)
            );
          },
          openProductSpecificView: function(e2) {
            k(
              a.openProductSpecificView,
              {
                pid: e2.productId,
                view_type: e2.viewType || 0,
                ext_info: e2.extInfo
              },
              e2
            );
          },
          addCard: function(e2) {
            for (var n2 = e2.cardList, i2 = [], t2 = 0, o2 = n2.length; t2 < o2; ++t2) {
              var r2 = n2[t2], r2 = { card_id: r2.cardId, card_ext: r2.cardExt };
              i2.push(r2);
            }
            k(
              a.addCard,
              { card_list: i2 },
              (e2._complete = function(e3) {
                if (n3 = e3.card_list) {
                  for (var n3, i3 = 0, t3 = (n3 = JSON.parse(n3)).length; i3 < t3; ++i3) {
                    var o3 = n3[i3];
                    o3.cardId = o3.card_id, o3.cardExt = o3.card_ext, o3.isSuccess = !!o3.is_succ, delete o3.card_id, delete o3.card_ext, delete o3.is_succ;
                  }
                  e3.cardList = n3, delete e3.card_list;
                }
              }, e2)
            );
          },
          chooseCard: function(e2) {
            k(
              "chooseCard",
              {
                app_id: h.appId,
                location_id: e2.shopId || "",
                sign_type: e2.signType || "SHA1",
                card_id: e2.cardId || "",
                card_type: e2.cardType || "",
                card_sign: e2.cardSign,
                time_stamp: e2.timestamp + "",
                nonce_str: e2.nonceStr
              },
              (e2._complete = function(e3) {
                e3.cardList = e3.choose_card_info, delete e3.choose_card_info;
              }, e2)
            );
          },
          openCard: function(e2) {
            for (var n2 = e2.cardList, i2 = [], t2 = 0, o2 = n2.length; t2 < o2; ++t2) {
              var r2 = n2[t2], r2 = { card_id: r2.cardId, code: r2.code };
              i2.push(r2);
            }
            k(a.openCard, { card_list: i2 }, e2);
          },
          consumeAndShareCard: function(e2) {
            k(
              a.consumeAndShareCard,
              { consumedCardId: e2.cardId, consumedCode: e2.code },
              e2
            );
          },
          chooseWXPay: function(e2) {
            k(a.chooseWXPay, x(e2), e2), B({ jsApiName: "chooseWXPay" });
          },
          openEnterpriseRedPacket: function(e2) {
            k(a.openEnterpriseRedPacket, x(e2), e2);
          },
          startSearchBeacons: function(e2) {
            k(a.startSearchBeacons, { ticket: e2.ticket }, e2);
          },
          stopSearchBeacons: function(e2) {
            k(a.stopSearchBeacons, {}, e2);
          },
          onSearchBeacons: function(e2) {
            M(a.onSearchBeacons, e2);
          },
          openEnterpriseChat: function(e2) {
            k(
              "openEnterpriseChat",
              { useridlist: e2.userIds, chatname: e2.groupName },
              e2
            );
          },
          launchMiniProgram: function(e2) {
            k(
              "launchMiniProgram",
              {
                targetAppId: e2.targetAppId,
                path: function(e3) {
                  var n2;
                  if ("string" == typeof e3 && 0 < e3.length)
                    return n2 = e3.split("?")[0], n2 += ".html", void 0 !== (e3 = e3.split("?")[1]) ? n2 + "?" + e3 : n2;
                }(e2.path),
                envVersion: e2.envVersion
              },
              e2
            );
          },
          openBusinessView: function(e2) {
            k(
              "openBusinessView",
              {
                businessType: e2.businessType,
                queryString: e2.queryString || "",
                envVersion: e2.envVersion
              },
              (e2._complete = function(n2) {
                if (l) {
                  var e3 = n2.extraData;
                  if (e3)
                    try {
                      n2.extraData = JSON.parse(e3);
                    } catch (e4) {
                      n2.extraData = {};
                    }
                }
              }, e2)
            );
          },
          miniProgram: {
            navigateBack: function(e2) {
              e2 = e2 || {}, O(function() {
                k(
                  "invokeMiniProgramAPI",
                  { name: "navigateBack", arg: { delta: e2.delta || 1 } },
                  e2
                );
              });
            },
            navigateTo: function(e2) {
              O(function() {
                k(
                  "invokeMiniProgramAPI",
                  { name: "navigateTo", arg: { url: e2.url } },
                  e2
                );
              });
            },
            redirectTo: function(e2) {
              O(function() {
                k(
                  "invokeMiniProgramAPI",
                  { name: "redirectTo", arg: { url: e2.url } },
                  e2
                );
              });
            },
            switchTab: function(e2) {
              O(function() {
                k(
                  "invokeMiniProgramAPI",
                  { name: "switchTab", arg: { url: e2.url } },
                  e2
                );
              });
            },
            reLaunch: function(e2) {
              O(function() {
                k(
                  "invokeMiniProgramAPI",
                  { name: "reLaunch", arg: { url: e2.url } },
                  e2
                );
              });
            },
            postMessage: function(e2) {
              O(function() {
                k(
                  "invokeMiniProgramAPI",
                  { name: "postMessage", arg: e2.data || {} },
                  e2
                );
              });
            },
            getEnv: function(e2) {
              O(function() {
                e2({ miniprogram: "miniprogram" === r.__wxjs_environment });
              });
            }
          }
        }, w = 1, T = {}, n.addEventListener(
          "error",
          function(e2) {
            var n2, i2, t2;
            l || (t2 = (n2 = e2.target).tagName, i2 = n2.src, "IMG" != t2 && "VIDEO" != t2 && "AUDIO" != t2 && "SOURCE" != t2) || -1 != i2.indexOf("wxlocalresource://") && (e2.preventDefault(), e2.stopPropagation(), (t2 = n2["wx-id"]) || (t2 = w++, n2["wx-id"] = t2), T[t2] || (T[t2] = true, wx.ready(function() {
              wx.getLocalImgData({
                localId: i2,
                success: function(e3) {
                  n2.src = e3.localData;
                }
              });
            })));
          },
          true
        ), n.addEventListener(
          "load",
          function(e2) {
            var n2;
            l || (n2 = (e2 = e2.target).tagName, e2.src, "IMG" != n2 && "VIDEO" != n2 && "AUDIO" != n2 && "SOURCE" != n2) || (n2 = e2["wx-id"]) && (T[n2] = false);
          },
          true
        ), e && (r.wx = r.jWeixin = _), _;
      else return r.jWeixin;
      function k(n2, e2, i2) {
        r.WeixinJSBridge ? WeixinJSBridge.invoke(n2, P(e2), function(e3) {
          V(n2, e3, i2);
        }) : C(n2, i2);
      }
      function M(n2, i2, t2) {
        r.WeixinJSBridge ? WeixinJSBridge.on(n2, function(e2) {
          t2 && t2.trigger && t2.trigger(e2), V(n2, e2, i2);
        }) : C(n2, t2 || i2);
      }
      function P(e2) {
        return (e2 = e2 || {}).appId = h.appId, e2.verifyAppId = h.appId, e2.verifySignType = "sha1", e2.verifyTimestamp = h.timestamp + "", e2.verifyNonceStr = h.nonceStr, e2.verifySignature = h.signature, e2;
      }
      function x(e2) {
        return {
          timeStamp: e2.timestamp + "",
          nonceStr: e2.nonceStr,
          package: e2.package,
          paySign: e2.paySign,
          signType: e2.signType || "SHA1"
        };
      }
      function V(e2, n2, i2) {
        "openEnterpriseChat" != e2 && "openBusinessView" !== e2 || (n2.errCode = n2.err_code), delete n2.err_code, delete n2.err_desc, delete n2.err_detail;
        var t2 = n2.errMsg, e2 = (t2 || (t2 = n2.err_msg, delete n2.err_msg, t2 = function(e3, n3) {
          var i3 = c[e3];
          i3 && (e3 = i3);
          i3 = "ok";
          {
            var t3;
            n3 && (t3 = n3.indexOf(":"), "access denied" != (i3 = (i3 = (i3 = -1 != (i3 = -1 != (i3 = "failed" == (i3 = "confirm" == (i3 = n3.substring(t3 + 1)) ? "ok" : i3) ? "fail" : i3).indexOf("failed_") ? i3.substring(7) : i3).indexOf("fail_") ? i3.substring(5) : i3).replace(/_/g, " ")).toLowerCase()) && "no permission to execute" != i3 || (i3 = "permission denied"), "" == (i3 = "config" == e3 && "function not exist" == i3 ? "ok" : i3)) && (i3 = "fail");
          }
          return n3 = e3 + ":" + i3;
        }(e2, t2), n2.errMsg = t2), (i2 = i2 || {})._complete && (i2._complete(n2), delete i2._complete), t2 = n2.errMsg || "", h.debug && !i2.isInnerInvoke && alert(JSON.stringify(n2)), t2.indexOf(":"));
        switch (t2.substring(e2 + 1)) {
          case "ok":
            i2.success && i2.success(n2);
            break;
          case "cancel":
            i2.cancel && i2.cancel(n2);
            break;
          default:
            i2.fail && i2.fail(n2);
        }
        i2.complete && i2.complete(n2);
      }
      function A(e2) {
        if (e2) {
          for (var n2 = 0, i2 = e2.length; n2 < i2; ++n2) {
            var t2 = e2[n2], t2 = a[t2];
            t2 && (e2[n2] = t2);
          }
          return e2;
        }
      }
      function C(e2, n2) {
        var i2;
        !h.debug || n2 && n2.isInnerInvoke || ((i2 = c[e2]) && (e2 = i2), n2 && n2._complete && delete n2._complete, console.log('"' + e2 + '",', n2 || ""));
      }
      function B(n2) {
        var i2;
        o || s || h.debug || p < "6.0.2" || g.systemType < 0 || (i2 = new Image(), g.appId = h.appId, g.initTime = m.initEndTime - m.initStartTime, g.preVerifyTime = m.preVerifyEndTime - m.preVerifyStartTime, _.getNetworkType({
          isInnerInvoke: true,
          success: function(e2) {
            g.networkType = e2.networkType;
            e2 = "https://open.weixin.qq.com/sdk/report?v=" + g.version + "&o=" + g.isPreVerifyOk + "&s=" + g.systemType + "&c=" + g.clientVersion + "&a=" + g.appId + "&n=" + g.networkType + "&i=" + g.initTime + "&p=" + g.preVerifyTime + "&u=" + g.url + "&jsapi_name=" + (n2 ? n2.jsApiName : "");
            i2.src = e2;
          }
        }));
      }
      function L() {
        return (/* @__PURE__ */ new Date()).getTime();
      }
      function O(e2) {
        d && (r.WeixinJSBridge ? e2() : n.addEventListener && n.addEventListener("WeixinJSBridgeReady", e2, false));
      }
    });
  }
});
export default require_weixin_js_sdk();
//# sourceMappingURL=weixin-js-sdk.js.map
