{"version": 3, "sources": ["../../.pnpm/quill-image-uploader@1.3.0_quill@1.3.7/node_modules/quill-image-uploader/src/blots/image.js", "../../.pnpm/quill-image-uploader@1.3.0_quill@1.3.7/node_modules/quill-image-uploader/src/quill.imageUploader.js"], "sourcesContent": ["import Quill from \"quill\";\r\n\r\nconst InlineBlot = Quill.import(\"blots/block\");\r\n\r\nclass LoadingImage extends InlineBlot {\r\n    static create(src) {\r\n        const node = super.create(src);\r\n        if (src === true) return node;\r\n\r\n        const image = document.createElement(\"img\");\r\n        image.setAttribute(\"src\", src);\r\n        node.appendChild(image);\r\n        return node;\r\n    }\r\n    deleteAt(index, length) {\r\n        super.deleteAt(index, length);\r\n        this.cache = {};\r\n    }\r\n    static value(domNode) {\r\n        const { src, custom } = domNode.dataset;\r\n        return { src, custom };\r\n    }\r\n}\r\n\r\nLoadingImage.blotName = \"imageBlot\";\r\nLoadingImage.className = \"image-uploading\";\r\nLoadingImage.tagName = \"span\";\r\nQuill.register({ \"formats/imageBlot\": LoadingImage });\r\n\r\nexport default LoadingImage;", "import LoadingImage from \"./blots/image.js\";\r\n\r\nclass ImageUploader {\r\n    constructor(quill, options) {\r\n        this.quill = quill;\r\n        this.options = options;\r\n        this.range = null;             \r\n        this.placeholderDelta = null; \r\n\r\n        if (typeof this.options.upload !== \"function\")\r\n            console.warn(\r\n                \"[Missing config] upload function that returns a promise is required\"\r\n            );\r\n\r\n        var toolbar = this.quill.getModule(\"toolbar\");\r\n        if (toolbar) {\r\n            toolbar.addHandler(\"image\", this.selectLocalImage.bind(this));\r\n        }\r\n\r\n        this.handleDrop = this.handleDrop.bind(this);\r\n        this.handlePaste = this.handlePaste.bind(this);\r\n\r\n        this.quill.root.addEventListener(\"drop\", this.handleDrop, false);\r\n        this.quill.root.addEventListener(\"paste\", this.handlePaste, false);\r\n    }\r\n\r\n    selectLocalImage() {\r\n        this.quill.focus();\r\n        this.range = this.quill.getSelection();\r\n        this.fileHolder = document.createElement(\"input\");\r\n        this.fileHolder.setAttribute(\"type\", \"file\");\r\n        this.fileHolder.setAttribute(\"accept\", \"image/*\");\r\n        this.fileHolder.setAttribute(\"style\", \"visibility:hidden\");\r\n\r\n        this.fileHolder.onchange = this.fileChanged.bind(this);\r\n\r\n        document.body.appendChild(this.fileHolder);\r\n\r\n        this.fileHolder.click();\r\n\r\n        window.requestAnimationFrame(() => {\r\n            document.body.removeChild(this.fileHolder);\r\n        });\r\n    }\r\n\r\n    handleDrop(evt) {\r\n        if (\r\n            evt.dataTransfer &&\r\n            evt.dataTransfer.files &&\r\n            evt.dataTransfer.files.length\r\n        ) {\r\n            evt.stopPropagation();\r\n            evt.preventDefault();\r\n            if (document.caretRangeFromPoint) {\r\n                const selection = document.getSelection();\r\n                const range = document.caretRangeFromPoint(evt.clientX, evt.clientY);\r\n                if (selection && range) {\r\n                    selection.setBaseAndExtent(\r\n                        range.startContainer,\r\n                        range.startOffset,\r\n                        range.startContainer,\r\n                        range.startOffset\r\n                    );\r\n                }\r\n            } else {\r\n                const selection = document.getSelection();\r\n                const range = document.caretPositionFromPoint(evt.clientX, evt.clientY);\r\n                if (selection && range) {\r\n                    selection.setBaseAndExtent(\r\n                        range.offsetNode,\r\n                        range.offset,\r\n                        range.offsetNode,\r\n                        range.offset\r\n                    );\r\n                }\r\n            }\r\n\r\n            this.quill.focus();\r\n            this.range = this.quill.getSelection();\r\n            let file = evt.dataTransfer.files[0];\r\n\r\n            setTimeout(() => {\r\n                this.quill.focus();\r\n                this.range = this.quill.getSelection();\r\n                this.readAndUploadFile(file);\r\n            }, 0);\r\n        }\r\n    }\r\n\r\n    handlePaste(evt) {\r\n        let clipboard = evt.clipboardData || window.clipboardData;\r\n\r\n        // IE 11 is .files other browsers are .items\r\n        if (clipboard && (clipboard.items || clipboard.files)) {\r\n            let items = clipboard.items || clipboard.files;\r\n            const IMAGE_MIME_REGEX = /^image\\/(jpe?g|gif|png|svg|webp)$/i;\r\n\r\n            for (let i = 0; i < items.length; i++) {\r\n                if (IMAGE_MIME_REGEX.test(items[i].type)) {\r\n                    let file = items[i].getAsFile ? items[i].getAsFile() : items[i];\r\n\r\n                    if (file) {\r\n                        this.quill.focus();\r\n                        this.range = this.quill.getSelection();\r\n                        evt.preventDefault();\r\n                        setTimeout(() => {\r\n                            this.quill.focus();\r\n                            this.range = this.quill.getSelection();\r\n                            this.readAndUploadFile(file);\r\n                        }, 0);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    readAndUploadFile(file) {\r\n        let isUploadReject = false;\r\n\r\n        const fileReader = new FileReader();\r\n\r\n        fileReader.addEventListener(\r\n            \"load\",\r\n            () => {\r\n                if (!isUploadReject) {\r\n                    let base64ImageSrc = fileReader.result;\r\n                    this.insertBase64Image(base64ImageSrc);\r\n                }\r\n            },\r\n            false\r\n        );\r\n\r\n        if (file) {\r\n            fileReader.readAsDataURL(file);\r\n        }\r\n\r\n        this.options.upload(file).then(\r\n            (imageUrl) => {\r\n                this.insertToEditor(imageUrl);\r\n            },\r\n            (error) => {\r\n                isUploadReject = true;\r\n                this.removeBase64Image();\r\n                console.warn(error);\r\n            }\r\n        );\r\n    }\r\n\r\n    fileChanged() {\r\n        const file = this.fileHolder.files[0];\r\n        this.readAndUploadFile(file);\r\n    }\r\n\r\n    insertBase64Image(url) {\r\n        const range = this.range;\r\n                \r\n        this.placeholderDelta = this.quill.insertEmbed(\r\n            range.index,\r\n            LoadingImage.blotName,\r\n            `${url}`,\r\n            \"user\"\r\n        );\r\n    }\r\n\r\n    insertToEditor(url) {\r\n        const range = this.range;        \r\n\r\n        const lengthToDelete = this.calculatePlaceholderInsertLength();        \r\n        \r\n        // Delete the placeholder image\r\n        this.quill.deleteText(range.index, lengthToDelete, \"user\");\r\n        // Insert the server saved image\r\n        this.quill.insertEmbed(range.index, \"image\", `${url}`, \"user\");\r\n\r\n        range.index++;\r\n        this.quill.setSelection(range, \"user\");\r\n    }\r\n\r\n    // The length of the insert delta from insertBase64Image can vary depending on what part of the line the insert occurs\r\n    calculatePlaceholderInsertLength() {\r\n        return this.placeholderDelta.ops.reduce((accumulator, deltaOperation) => {            \r\n            if (deltaOperation.hasOwnProperty('insert'))\r\n                accumulator++;\r\n\r\n            return accumulator;\r\n        }, 0);\r\n    }\r\n\r\n    removeBase64Image() {        \r\n        const range = this.range;\r\n        const lengthToDelete = this.calculatePlaceholderInsertLength();\r\n\r\n        this.quill.deleteText(range.index, lengthToDelete, \"user\");\r\n    }\r\n}\r\n\r\nwindow.ImageUploader = ImageUploader;\r\nexport default ImageUploader;"], "mappings": ";;;;;;;;AAAA,mBAAkB;AAElB,IAAM,aAAa,aAAAA,QAAM,OAAO,aAAa;AAE7C,IAAM,eAAN,cAA2B,WAAW;AAAA,EAClC,OAAO,OAAO,KAAK;AACf,UAAM,OAAO,MAAM,OAAO,GAAG;AAC7B,QAAI,QAAQ,KAAM,QAAO;AAEzB,UAAM,QAAQ,SAAS,cAAc,KAAK;AAC1C,UAAM,aAAa,OAAO,GAAG;AAC7B,SAAK,YAAY,KAAK;AACtB,WAAO;AAAA,EACX;AAAA,EACA,SAAS,OAAO,QAAQ;AACpB,UAAM,SAAS,OAAO,MAAM;AAC5B,SAAK,QAAQ,CAAC;AAAA,EAClB;AAAA,EACA,OAAO,MAAM,SAAS;AAClB,UAAM,EAAE,KAAK,OAAO,IAAI,QAAQ;AAChC,WAAO,EAAE,KAAK,OAAO;AAAA,EACzB;AACJ;AAEA,aAAa,WAAW;AACxB,aAAa,YAAY;AACzB,aAAa,UAAU;AACvB,aAAAA,QAAM,SAAS,EAAE,qBAAqB,aAAa,CAAC;AAEpD,IAAO,gBAAQ;;;AC3Bf,IAAM,gBAAN,MAAoB;AAAA,EAChB,YAAY,OAAO,SAAS;AACxB,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,mBAAmB;AAExB,QAAI,OAAO,KAAK,QAAQ,WAAW;AAC/B,cAAQ;AAAA,QACJ;AAAA,MACJ;AAEJ,QAAI,UAAU,KAAK,MAAM,UAAU,SAAS;AAC5C,QAAI,SAAS;AACT,cAAQ,WAAW,SAAS,KAAK,iBAAiB,KAAK,IAAI,CAAC;AAAA,IAChE;AAEA,SAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,SAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAE7C,SAAK,MAAM,KAAK,iBAAiB,QAAQ,KAAK,YAAY,KAAK;AAC/D,SAAK,MAAM,KAAK,iBAAiB,SAAS,KAAK,aAAa,KAAK;AAAA,EACrE;AAAA,EAEA,mBAAmB;AACf,SAAK,MAAM,MAAM;AACjB,SAAK,QAAQ,KAAK,MAAM,aAAa;AACrC,SAAK,aAAa,SAAS,cAAc,OAAO;AAChD,SAAK,WAAW,aAAa,QAAQ,MAAM;AAC3C,SAAK,WAAW,aAAa,UAAU,SAAS;AAChD,SAAK,WAAW,aAAa,SAAS,mBAAmB;AAEzD,SAAK,WAAW,WAAW,KAAK,YAAY,KAAK,IAAI;AAErD,aAAS,KAAK,YAAY,KAAK,UAAU;AAEzC,SAAK,WAAW,MAAM;AAEtB,WAAO,sBAAsB,MAAM;AAC/B,eAAS,KAAK,YAAY,KAAK,UAAU;AAAA,IAC7C,CAAC;AAAA,EACL;AAAA,EAEA,WAAW,KAAK;AACZ,QACI,IAAI,gBACJ,IAAI,aAAa,SACjB,IAAI,aAAa,MAAM,QACzB;AACE,UAAI,gBAAgB;AACpB,UAAI,eAAe;AACnB,UAAI,SAAS,qBAAqB;AAC9B,cAAM,YAAY,SAAS,aAAa;AACxC,cAAM,QAAQ,SAAS,oBAAoB,IAAI,SAAS,IAAI,OAAO;AACnE,YAAI,aAAa,OAAO;AACpB,oBAAU;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ,OAAO;AACH,cAAM,YAAY,SAAS,aAAa;AACxC,cAAM,QAAQ,SAAS,uBAAuB,IAAI,SAAS,IAAI,OAAO;AACtE,YAAI,aAAa,OAAO;AACpB,oBAAU;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AAEA,WAAK,MAAM,MAAM;AACjB,WAAK,QAAQ,KAAK,MAAM,aAAa;AACrC,UAAI,OAAO,IAAI,aAAa,MAAM,CAAC;AAEnC,iBAAW,MAAM;AACb,aAAK,MAAM,MAAM;AACjB,aAAK,QAAQ,KAAK,MAAM,aAAa;AACrC,aAAK,kBAAkB,IAAI;AAAA,MAC/B,GAAG,CAAC;AAAA,IACR;AAAA,EACJ;AAAA,EAEA,YAAY,KAAK;AACb,QAAI,YAAY,IAAI,iBAAiB,OAAO;AAG5C,QAAI,cAAc,UAAU,SAAS,UAAU,QAAQ;AACnD,UAAI,QAAQ,UAAU,SAAS,UAAU;AACzC,YAAM,mBAAmB;AAEzB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,YAAI,iBAAiB,KAAK,MAAM,CAAC,EAAE,IAAI,GAAG;AACtC,cAAI,OAAO,MAAM,CAAC,EAAE,YAAY,MAAM,CAAC,EAAE,UAAU,IAAI,MAAM,CAAC;AAE9D,cAAI,MAAM;AACN,iBAAK,MAAM,MAAM;AACjB,iBAAK,QAAQ,KAAK,MAAM,aAAa;AACrC,gBAAI,eAAe;AACnB,uBAAW,MAAM;AACb,mBAAK,MAAM,MAAM;AACjB,mBAAK,QAAQ,KAAK,MAAM,aAAa;AACrC,mBAAK,kBAAkB,IAAI;AAAA,YAC/B,GAAG,CAAC;AAAA,UACR;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,kBAAkB,MAAM;AACpB,QAAI,iBAAiB;AAErB,UAAM,aAAa,IAAI,WAAW;AAElC,eAAW;AAAA,MACP;AAAA,MACA,MAAM;AACF,YAAI,CAAC,gBAAgB;AACjB,cAAI,iBAAiB,WAAW;AAChC,eAAK,kBAAkB,cAAc;AAAA,QACzC;AAAA,MACJ;AAAA,MACA;AAAA,IACJ;AAEA,QAAI,MAAM;AACN,iBAAW,cAAc,IAAI;AAAA,IACjC;AAEA,SAAK,QAAQ,OAAO,IAAI,EAAE;AAAA,MACtB,CAAC,aAAa;AACV,aAAK,eAAe,QAAQ;AAAA,MAChC;AAAA,MACA,CAAC,UAAU;AACP,yBAAiB;AACjB,aAAK,kBAAkB;AACvB,gBAAQ,KAAK,KAAK;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,cAAc;AACV,UAAM,OAAO,KAAK,WAAW,MAAM,CAAC;AACpC,SAAK,kBAAkB,IAAI;AAAA,EAC/B;AAAA,EAEA,kBAAkB,KAAK;AACnB,UAAM,QAAQ,KAAK;AAEnB,SAAK,mBAAmB,KAAK,MAAM;AAAA,MAC/B,MAAM;AAAA,MACN,cAAa;AAAA,MACb,GAAG,GAAG;AAAA,MACN;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,eAAe,KAAK;AAChB,UAAM,QAAQ,KAAK;AAEnB,UAAM,iBAAiB,KAAK,iCAAiC;AAG7D,SAAK,MAAM,WAAW,MAAM,OAAO,gBAAgB,MAAM;AAEzD,SAAK,MAAM,YAAY,MAAM,OAAO,SAAS,GAAG,GAAG,IAAI,MAAM;AAE7D,UAAM;AACN,SAAK,MAAM,aAAa,OAAO,MAAM;AAAA,EACzC;AAAA;AAAA,EAGA,mCAAmC;AAC/B,WAAO,KAAK,iBAAiB,IAAI,OAAO,CAAC,aAAa,mBAAmB;AACrE,UAAI,eAAe,eAAe,QAAQ;AACtC;AAEJ,aAAO;AAAA,IACX,GAAG,CAAC;AAAA,EACR;AAAA,EAEA,oBAAoB;AAChB,UAAM,QAAQ,KAAK;AACnB,UAAM,iBAAiB,KAAK,iCAAiC;AAE7D,SAAK,MAAM,WAAW,MAAM,OAAO,gBAAgB,MAAM;AAAA,EAC7D;AACJ;AAEA,OAAO,gBAAgB;AACvB,IAAO,8BAAQ;", "names": ["<PERSON><PERSON><PERSON>"]}