{"version": 3, "sources": ["../../.pnpm/print-js@1.6.0/node_modules/print-js/dist/webpack:/printJS/webpack/universalModuleDefinition", "../../.pnpm/print-js@1.6.0/node_modules/print-js/dist/webpack:/printJS/webpack/bootstrap", "../../.pnpm/print-js@1.6.0/node_modules/print-js/dist/webpack:/printJS/src/index.js", "../../.pnpm/print-js@1.6.0/node_modules/print-js/dist/webpack:/printJS/src/js/browser.js", "../../.pnpm/print-js@1.6.0/node_modules/print-js/dist/webpack:/printJS/src/js/functions.js", "../../.pnpm/print-js@1.6.0/node_modules/print-js/dist/webpack:/printJS/src/js/html.js", "../../.pnpm/print-js@1.6.0/node_modules/print-js/dist/webpack:/printJS/src/js/image.js", "../../.pnpm/print-js@1.6.0/node_modules/print-js/dist/webpack:/printJS/src/js/init.js", "../../.pnpm/print-js@1.6.0/node_modules/print-js/dist/webpack:/printJS/src/js/json.js", "../../.pnpm/print-js@1.6.0/node_modules/print-js/dist/webpack:/printJS/src/js/modal.js", "../../.pnpm/print-js@1.6.0/node_modules/print-js/dist/webpack:/printJS/src/js/pdf.js", "../../.pnpm/print-js@1.6.0/node_modules/print-js/dist/webpack:/printJS/src/js/print.js", "../../.pnpm/print-js@1.6.0/node_modules/print-js/dist/webpack:/printJS/src/js/raw-html.js", "../../.pnpm/print-js@1.6.0/node_modules/print-js/dist/webpack:/printJS/src/sass/index.scss"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"printJS\"] = factory();\n\telse\n\t\troot[\"printJS\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 0);\n", "import './sass/index.scss'\nimport print from './js/init'\n\nconst printJS = print.init\n\nif (typeof window !== 'undefined') {\n  window.printJS = printJS\n}\n\nexport default printJS\n", "const Browser = {\n  // Firefox 1.0+\n  isFirefox: () => {\n    return typeof InstallTrigger !== 'undefined'\n  },\n  // Internet Explorer 6-11\n  isIE: () => {\n    return navigator.userAgent.indexOf('MSIE') !== -1 || !!document.documentMode\n  },\n  // Edge 20+\n  isEdge: () => {\n    return !Browser.isIE() && !!window.StyleMedia\n  },\n  // Chrome 1+\n  isChrome: (context = window) => {\n    return !!context.chrome\n  },\n  // At least Safari 3+: \"[object HTMLElementConstructor]\"\n  isSafari: () => {\n    return Object.prototype.toString.call(window.HTMLElement).indexOf('Constructor') > 0 ||\n        navigator.userAgent.toLowerCase().indexOf('safari') !== -1\n  },\n  // IOS Chrome\n  isIOSChrome: () => {\n    return navigator.userAgent.toLowerCase().indexOf('crios') !== -1\n  }\n}\n\nexport default Browser\n", "import Modal from './modal'\nimport Browser from './browser'\n\nexport function addWrapper (htmlData, params) {\n  const bodyStyle = 'font-family:' + params.font + ' !important; font-size: ' + params.font_size + ' !important; width:100%;'\n  return '<div style=\"' + bodyStyle + '\">' + htmlData + '</div>'\n}\n\nexport function capitalizePrint (obj) {\n  return obj.charAt(0).toUpperCase() + obj.slice(1)\n}\n\nexport function collectStyles (element, params) {\n  const win = document.defaultView || window\n\n  // String variable to hold styling for each element\n  let elementStyle = ''\n\n  // Loop over computed styles\n  const styles = win.getComputedStyle(element, '')\n\n  for (let key = 0; key < styles.length; key++) {\n    // Check if style should be processed\n    if (params.targetStyles.indexOf('*') !== -1 || params.targetStyle.indexOf(styles[key]) !== -1 || targetStylesMatch(params.targetStyles, styles[key])) {\n      if (styles.getPropertyValue(styles[key])) elementStyle += styles[key] + ':' + styles.getPropertyValue(styles[key]) + ';'\n    }\n  }\n\n  // Print friendly defaults (deprecated)\n  elementStyle += 'max-width: ' + params.maxWidth + 'px !important; font-size: ' + params.font_size + ' !important;'\n\n  return elementStyle\n}\n\nfunction targetStylesMatch (styles, value) {\n  for (let i = 0; i < styles.length; i++) {\n    if (typeof value === 'object' && value.indexOf(styles[i]) !== -1) return true\n  }\n  return false\n}\n\nexport function addHeader (printElement, params) {\n  // Create the header container div\n  const headerContainer = document.createElement('div')\n\n  // Check if the header is text or raw html\n  if (isRawHTML(params.header)) {\n    headerContainer.innerHTML = params.header\n  } else {\n    // Create header element\n    const headerElement = document.createElement('h1')\n\n    // Create header text node\n    const headerNode = document.createTextNode(params.header)\n\n    // Build and style\n    headerElement.appendChild(headerNode)\n    headerElement.setAttribute('style', params.headerStyle)\n    headerContainer.appendChild(headerElement)\n  }\n\n  printElement.insertBefore(headerContainer, printElement.childNodes[0])\n}\n\nexport function cleanUp (params) {\n  // If we are showing a feedback message to user, remove it\n  if (params.showModal) Modal.close()\n\n  // Check for a finished loading hook function\n  if (params.onLoadingEnd) params.onLoadingEnd()\n\n  // If preloading pdf files, clean blob url\n  if (params.showModal || params.onLoadingStart) window.URL.revokeObjectURL(params.printable)\n\n  // Run onPrintDialogClose callback\n  let event = 'mouseover'\n\n  if (Browser.isChrome() || Browser.isFirefox()) {\n    // Ps.: Firefox will require an extra click in the document to fire the focus event.\n    event = 'focus'\n  }\n\n  const handler = () => {\n    // Make sure the event only happens once.\n    window.removeEventListener(event, handler)\n\n    params.onPrintDialogClose()\n\n    // Remove iframe from the DOM\n    const iframe = document.getElementById(params.frameId)\n\n    if (iframe) {\n      iframe.remove()\n    }\n  }\n\n  window.addEventListener(event, handler)\n}\n\nexport function isRawHTML (raw) {\n  const regexHtml = new RegExp('<([A-Za-z][A-Za-z0-9]*)\\\\b[^>]*>(.*?)</\\\\1>')\n  return regexHtml.test(raw)\n}\n", "import { collectStyles, addHeader } from './functions'\nimport Print from './print'\n\nexport default {\n  print: (params, printFrame) => {\n    // Get the DOM printable element\n    const printElement = isHtmlElement(params.printable) ? params.printable : document.getElementById(params.printable)\n\n    // Check if the element exists\n    if (!printElement) {\n      window.console.error('Invalid HTML element id: ' + params.printable)\n      return\n    }\n\n    // Clone the target element including its children (if available)\n    params.printableElement = cloneElement(printElement, params)\n\n    // Add header\n    if (params.header) {\n      addHeader(params.printableElement, params)\n    }\n\n    // Print html element contents\n    Print.send(params, printFrame)\n  }\n}\n\nfunction cloneElement (element, params) {\n  // Clone the main node (if not already inside the recursion process)\n  const clone = element.cloneNode()\n\n  // Loop over and process the children elements / nodes (including text nodes)\n  const childNodesArray = Array.prototype.slice.call(element.childNodes)\n  for (let i = 0; i < childNodesArray.length; i++) {\n    // Check if we are skipping the current element\n    if (params.ignoreElements.indexOf(childNodesArray[i].id) !== -1) {\n      continue\n    }\n\n    // Clone the child element\n    const clonedChild = cloneElement(childNodesArray[i], params)\n\n    // Attach the cloned child to the cloned parent node\n    clone.appendChild(clonedChild)\n  }\n\n  // Get all styling for print element (for nodes of type element only)\n  if (params.scanStyles && element.nodeType === 1) {\n    clone.setAttribute('style', collectStyles(element, params))\n  }\n\n  // Check if the element needs any state processing (copy user input data)\n  switch (element.tagName) {\n    case 'SELECT':\n      // Copy the current selection value to its clone\n      clone.value = element.value\n      break\n    case 'CANVAS':\n      // Copy the canvas content to its clone\n      clone.getContext('2d').drawImage(element, 0, 0)\n      break\n  }\n\n  return clone\n}\n\nfunction isHtmlElement (printable) {\n  // Check if element is instance of HTMLElement or has nodeType === 1 (for elements in iframe)\n  return typeof printable === 'object' && printable && (printable instanceof HTMLElement || printable.nodeType === 1)\n}\n", "import { addHeader } from './functions'\nimport Print from './print'\nimport Browser from './browser'\n\nexport default {\n  print: (params, printFrame) => {\n    // Check if we are printing one image or multiple images\n    if (params.printable.constructor !== Array) {\n      // Create array with one image\n      params.printable = [params.printable]\n    }\n\n    // Create printable element (container)\n    params.printableElement = document.createElement('div')\n\n    // Create all image elements and append them to the printable container\n    params.printable.forEach(src => {\n      // Create the image element\n      const img = document.createElement('img')\n      img.setAttribute('style', params.imageStyle)\n\n      // Set image src with the file url\n      img.src = src\n\n      // The following block is for Firefox, which for some reason requires the image's src to be fully qualified in\n      // order to print it\n      if (Browser.isFirefox()) {\n        const fullyQualifiedSrc = img.src\n        img.src = fullyQualifiedSrc\n      }\n\n      // Create the image wrapper\n      const imageWrapper = document.createElement('div')\n\n      // Append image to the wrapper element\n      imageWrapper.appendChild(img)\n\n      // Append wrapper to the printable element\n      params.printableElement.appendChild(imageWrapper)\n    })\n\n    // Check if we are adding a print header\n    if (params.header) addHeader(params.printableElement, params)\n\n    // Print image\n    Print.send(params, printFrame)\n  }\n}\n", "'use strict'\n\nimport Browser from './browser'\nimport Modal from './modal'\nimport Pdf from './pdf'\nimport Html from './html'\nimport RawHtml from './raw-html'\nimport Image from './image'\nimport J<PERSON> from './json'\n\nconst printTypes = ['pdf', 'html', 'image', 'json', 'raw-html']\n\nexport default {\n  init () {\n    const params = {\n      printable: null,\n      fallbackPrintable: null,\n      type: 'pdf',\n      header: null,\n      headerStyle: 'font-weight: 300;',\n      maxWidth: 800,\n      properties: null,\n      gridHeaderStyle: 'font-weight: bold; padding: 5px; border: 1px solid #dddddd;',\n      gridStyle: 'border: 1px solid lightgray; margin-bottom: -1px;',\n      showModal: false,\n      onError: (error) => { throw error },\n      onLoadingStart: null,\n      onLoadingEnd: null,\n      onPrintDialogClose: () => {},\n      onIncompatibleBrowser: () => {},\n      modalMessage: 'Retrieving Document...',\n      frameId: 'printJS',\n      printableElement: null,\n      documentTitle: 'Document',\n      targetStyle: ['clear', 'display', 'width', 'min-width', 'height', 'min-height', 'max-height'],\n      targetStyles: ['border', 'box', 'break', 'text-decoration'],\n      ignoreElements: [],\n      repeatTableHeader: true,\n      css: null,\n      style: null,\n      scanStyles: true,\n      base64: false,\n\n      // Deprecated\n      onPdfOpen: null,\n      font: 'TimesNewRoman',\n      font_size: '12pt',\n      honorMarginPadding: true,\n      honorColor: false,\n      imageStyle: 'max-width: 100%;'\n    }\n\n    // Check if a printable document or object was supplied\n    const args = arguments[0]\n    if (args === undefined) {\n      throw new Error('printJS expects at least 1 attribute.')\n    }\n\n    // Process parameters\n    switch (typeof args) {\n      case 'string':\n        params.printable = encodeURI(args)\n        params.fallbackPrintable = params.printable\n        params.type = arguments[1] || params.type\n        break\n      case 'object':\n        params.printable = args.printable\n        params.fallbackPrintable = typeof args.fallbackPrintable !== 'undefined' ? args.fallbackPrintable : params.printable\n        params.fallbackPrintable = params.base64 ? `data:application/pdf;base64,${params.fallbackPrintable}` : params.fallbackPrintable\n        for (var k in params) {\n          if (k === 'printable' || k === 'fallbackPrintable') continue\n\n          params[k] = typeof args[k] !== 'undefined' ? args[k] : params[k]\n        }\n        break\n      default:\n        throw new Error('Unexpected argument type! Expected \"string\" or \"object\", got ' + typeof args)\n    }\n\n    // Validate printable\n    if (!params.printable) throw new Error('Missing printable information.')\n\n    // Validate type\n    if (!params.type || typeof params.type !== 'string' || printTypes.indexOf(params.type.toLowerCase()) === -1) {\n      throw new Error('Invalid print type. Available types are: pdf, html, image and json.')\n    }\n\n    // Check if we are showing a feedback message to the user (useful for large files)\n    if (params.showModal) Modal.show(params)\n\n    // Check for a print start hook function\n    if (params.onLoadingStart) params.onLoadingStart()\n\n    // To prevent duplication and issues, remove any used printFrame from the DOM\n    const usedFrame = document.getElementById(params.frameId)\n\n    if (usedFrame) usedFrame.parentNode.removeChild(usedFrame)\n\n    // Create a new iframe for the print job\n    const printFrame = document.createElement('iframe')\n\n    if (Browser.isFirefox()) {\n      // Set the iframe to be is visible on the page (guaranteed by fixed position) but hidden using opacity 0, because\n      // this works in Firefox. The height needs to be sufficient for some part of the document other than the PDF\n      // viewer's toolbar to be visible in the page\n      printFrame.setAttribute('style', 'width: 1px; height: 100px; position: fixed; left: 0; top: 0; opacity: 0; border-width: 0; margin: 0; padding: 0')\n    } else {\n      // Hide the iframe in other browsers\n      printFrame.setAttribute('style', 'visibility: hidden; height: 0; width: 0; position: absolute; border: 0')\n    }\n\n    // Set iframe element id\n    printFrame.setAttribute('id', params.frameId)\n\n    // For non pdf printing, pass an html document string to srcdoc (force onload callback)\n    if (params.type !== 'pdf') {\n      printFrame.srcdoc = '<html><head><title>' + params.documentTitle + '</title>'\n\n      // Attach css files\n      if (params.css) {\n        // Add support for single file\n        if (!Array.isArray(params.css)) params.css = [params.css]\n\n        // Create link tags for each css file\n        params.css.forEach(file => {\n          printFrame.srcdoc += '<link rel=\"stylesheet\" href=\"' + file + '\">'\n        })\n      }\n\n      printFrame.srcdoc += '</head><body></body></html>'\n    }\n\n    // Check printable type\n    switch (params.type) {\n      case 'pdf':\n        // Check browser support for pdf and if not supported we will just open the pdf file instead\n        if (Browser.isIE()) {\n          try {\n            console.info('Print.js doesn\\'t support PDF printing in Internet Explorer.')\n            const win = window.open(params.fallbackPrintable, '_blank')\n            win.focus()\n            params.onIncompatibleBrowser()\n          } catch (error) {\n            params.onError(error)\n          } finally {\n            // Make sure there is no loading modal opened\n            if (params.showModal) Modal.close()\n            if (params.onLoadingEnd) params.onLoadingEnd()\n          }\n        } else {\n          Pdf.print(params, printFrame)\n        }\n        break\n      case 'image':\n        Image.print(params, printFrame)\n        break\n      case 'html':\n        Html.print(params, printFrame)\n        break\n      case 'raw-html':\n        RawHtml.print(params, printFrame)\n        break\n      case 'json':\n        Json.print(params, printFrame)\n        break\n    }\n  }\n}\n", "import { capitalizePrint, addHeader } from './functions'\nimport Print from './print'\n\nexport default {\n  print: (params, printFrame) => {\n    // Check if we received proper data\n    if (typeof params.printable !== 'object') {\n      throw new Error('Invalid javascript data object (JSON).')\n    }\n\n    // Validate repeatTableHeader\n    if (typeof params.repeatTableHeader !== 'boolean') {\n      throw new Error('Invalid value for repeatTableHeader attribute (JSON).')\n    }\n\n    // Validate properties\n    if (!params.properties || !Array.isArray(params.properties)) {\n      throw new Error('Invalid properties array for your JSON data.')\n    }\n\n    // We will format the property objects to keep the JSON api compatible with older releases\n    params.properties = params.properties.map(property => {\n      return {\n        field: typeof property === 'object' ? property.field : property,\n        displayName: typeof property === 'object' ? property.displayName : property,\n        columnSize: typeof property === 'object' && property.columnSize ? property.columnSize + ';' : 100 / params.properties.length + '%;'\n      }\n    })\n\n    // Create a print container element\n    params.printableElement = document.createElement('div')\n\n    // Check if we are adding a print header\n    if (params.header) {\n      addHeader(params.printableElement, params)\n    }\n\n    // Build the printable html data\n    params.printableElement.innerHTML += jsonToHTML(params)\n\n    // Print the json data\n    Print.send(params, printFrame)\n  }\n}\n\nfunction jsonToHTML (params) {\n  // Get the row and column data\n  const data = params.printable\n  const properties = params.properties\n\n  // Create a html table\n  let htmlData = '<table style=\"border-collapse: collapse; width: 100%;\">'\n\n  // Check if the header should be repeated\n  if (params.repeatTableHeader) {\n    htmlData += '<thead>'\n  }\n\n  // Add the table header row\n  htmlData += '<tr>'\n\n  // Add the table header columns\n  for (let a = 0; a < properties.length; a++) {\n    htmlData += '<th style=\"width:' + properties[a].columnSize + ';' + params.gridHeaderStyle + '\">' + capitalizePrint(properties[a].displayName) + '</th>'\n  }\n\n  // Add the closing tag for the table header row\n  htmlData += '</tr>'\n\n  // If the table header is marked as repeated, add the closing tag\n  if (params.repeatTableHeader) {\n    htmlData += '</thead>'\n  }\n\n  // Create the table body\n  htmlData += '<tbody>'\n\n  // Add the table data rows\n  for (let i = 0; i < data.length; i++) {\n    // Add the row starting tag\n    htmlData += '<tr>'\n\n    // Print selected properties only\n    for (let n = 0; n < properties.length; n++) {\n      let stringData = data[i]\n\n      // Support nested objects\n      const property = properties[n].field.split('.')\n      if (property.length > 1) {\n        for (let p = 0; p < property.length; p++) {\n          stringData = stringData[property[p]]\n        }\n      } else {\n        stringData = stringData[properties[n].field]\n      }\n\n      // Add the row contents and styles\n      htmlData += '<td style=\"width:' + properties[n].columnSize + params.gridStyle + '\">' + stringData + '</td>'\n    }\n\n    // Add the row closing tag\n    htmlData += '</tr>'\n  }\n\n  // Add the table and body closing tags\n  htmlData += '</tbody></table>'\n\n  return htmlData\n}\n", "const Modal = {\n  show (params) {\n    // Build modal\n    const modalStyle = 'font-family:sans-serif; ' +\n        'display:table; ' +\n        'text-align:center; ' +\n        'font-weight:300; ' +\n        'font-size:30px; ' +\n        'left:0; top:0;' +\n        'position:fixed; ' +\n        'z-index: 9990;' +\n        'color: #0460B5; ' +\n        'width: 100%; ' +\n        'height: 100%; ' +\n        'background-color:rgba(255,255,255,.9);' +\n        'transition: opacity .3s ease;'\n\n    // Create wrapper\n    const printModal = document.createElement('div')\n    printModal.setAttribute('style', modalStyle)\n    printModal.setAttribute('id', 'printJS-Modal')\n\n    // Create content div\n    const contentDiv = document.createElement('div')\n    contentDiv.setAttribute('style', 'display:table-cell; vertical-align:middle; padding-bottom:100px;')\n\n    // Add close button (requires print.css)\n    const closeButton = document.createElement('div')\n    closeButton.setAttribute('class', 'printClose')\n    closeButton.setAttribute('id', 'printClose')\n    contentDiv.appendChild(closeButton)\n\n    // Add spinner (requires print.css)\n    const spinner = document.createElement('span')\n    spinner.setAttribute('class', 'printSpinner')\n    contentDiv.appendChild(spinner)\n\n    // Add message\n    const messageNode = document.createTextNode(params.modalMessage)\n    contentDiv.appendChild(messageNode)\n\n    // Add contentDiv to printModal\n    printModal.appendChild(contentDiv)\n\n    // Append print modal element to document body\n    document.getElementsByTagName('body')[0].appendChild(printModal)\n\n    // Add event listener to close button\n    document.getElementById('printClose').addEventListener('click', function () {\n      Modal.close()\n    })\n  },\n  close () {\n    const printModal = document.getElementById('printJS-Modal')\n\n    if (printModal) {\n      printModal.parentNode.removeChild(printModal)\n    }\n  }\n}\n\nexport default Modal\n", "import Print from './print'\nimport { cleanUp } from './functions'\n\nexport default {\n  print: (params, printFrame) => {\n    // Check if we have base64 data\n    if (params.base64) {\n      const bytesArray = Uint8Array.from(atob(params.printable), c => c.charCodeAt(0))\n      createBlobAndPrint(params, printFrame, bytesArray)\n      return\n    }\n\n    // Format pdf url\n    params.printable = /^(blob|http|\\/\\/)/i.test(params.printable)\n      ? params.printable\n      : window.location.origin + (params.printable.charAt(0) !== '/' ? '/' + params.printable : params.printable)\n\n    // Get the file through a http request (Preload)\n    const req = new window.XMLHttpRequest()\n    req.responseType = 'arraybuffer'\n\n    req.addEventListener('error', () => {\n      cleanUp(params)\n      params.onError(req.statusText, req)\n\n      // Since we don't have a pdf document available, we will stop the print job\n    })\n\n    req.addEventListener('load', () => {\n      // Check for errors\n      if ([200, 201].indexOf(req.status) === -1) {\n        cleanUp(params)\n        params.onError(req.statusText, req)\n\n        // Since we don't have a pdf document available, we will stop the print job\n        return\n      }\n\n      // Print requested document\n      createBlobAndPrint(params, printFrame, req.response)\n    })\n\n    req.open('GET', params.printable, true)\n    req.send()\n  }\n}\n\nfunction createBlobAndPrint (params, printFrame, data) {\n  // Pass response or base64 data to a blob and create a local object url\n  let localPdf = new window.Blob([data], { type: 'application/pdf' })\n  localPdf = window.URL.createObjectURL(localPdf)\n\n  // Set iframe src with pdf document url\n  printFrame.setAttribute('src', localPdf)\n\n  Print.send(params, printFrame)\n}\n", "import Browser from './browser'\nimport { cleanUp } from './functions'\n\nconst Print = {\n  send: (params, printFrame) => {\n    // Append iframe element to document body\n    document.getElementsByTagName('body')[0].appendChild(printFrame)\n\n    // Get iframe element\n    const iframeElement = document.getElementById(params.frameId)\n\n    // Wait for iframe to load all content\n    iframeElement.onload = () => {\n      if (params.type === 'pdf') {\n        // Add a delay for Firefox. In my tests, 1000ms was sufficient but 100ms was not\n        if (Browser.isFirefox()) {\n          setTimeout(() => performPrint(iframeElement, params), 1000)\n        } else {\n          performPrint(iframeElement, params)\n        }\n        return\n      }\n\n      // Get iframe element document\n      let printDocument = (iframeElement.contentWindow || iframeElement.contentDocument)\n      if (printDocument.document) printDocument = printDocument.document\n\n      // Append printable element to the iframe body\n      printDocument.body.appendChild(params.printableElement)\n\n      // Add custom style\n      if (params.type !== 'pdf' && params.style) {\n        // Create style element\n        const style = document.createElement('style')\n        style.innerHTML = params.style\n\n        // Append style element to iframe's head\n        printDocument.head.appendChild(style)\n      }\n\n      // If printing images, wait for them to load inside the iframe\n      const images = printDocument.getElementsByTagName('img')\n\n      if (images.length > 0) {\n        loadIframeImages(Array.from(images)).then(() => performPrint(iframeElement, params))\n      } else {\n        performPrint(iframeElement, params)\n      }\n    }\n  }\n}\n\nfunction performPrint (iframeElement, params) {\n  try {\n    iframeElement.focus()\n\n    // If Edge or IE, try catch with execCommand\n    if (Browser.isEdge() || Browser.isIE()) {\n      try {\n        iframeElement.contentWindow.document.execCommand('print', false, null)\n      } catch (e) {\n        iframeElement.contentWindow.print()\n      }\n    } else {\n      // Other browsers\n      iframeElement.contentWindow.print()\n    }\n  } catch (error) {\n    params.onError(error)\n  } finally {\n    if (Browser.isFirefox()) {\n      // Move the iframe element off-screen and make it invisible\n      iframeElement.style.visibility = 'hidden'\n      iframeElement.style.left = '-1px'\n    }\n\n    cleanUp(params)\n  }\n}\n\nfunction loadIframeImages (images) {\n  const promises = images.map(image => {\n    if (image.src && image.src !== window.location.href) {\n      return loadIframeImage(image)\n    }\n  })\n\n  return Promise.all(promises)\n}\n\nfunction loadIframeImage (image) {\n  return new Promise(resolve => {\n    const pollImage = () => {\n      !image || typeof image.naturalWidth === 'undefined' || image.naturalWidth === 0 || !image.complete\n        ? setTimeout(pollImage, 500)\n        : resolve()\n    }\n    pollImage()\n  })\n}\n\nexport default Print\n", "import Print from './print'\n\nexport default {\n  print: (params, printFrame) => {\n    // Create printable element (container)\n    params.printableElement = document.createElement('div')\n    params.printableElement.setAttribute('style', 'width:100%')\n\n    // Set our raw html as the printable element inner html content\n    params.printableElement.innerHTML = params.printable\n\n    // Print html contents\n    Print.send(params, printFrame)\n  }\n}\n", "// extracted by mini-css-extract-plugin"], "mappings": ";;;;;AAAA;;KAAA,SAAA,iCAAA,MAAA,SAAA;AACA,UAAA,OAAA,YAAA,YAAA,OAAA,WAAA;AACA,eAAA,UAAA,QAAA;eACA,OAAA,WAAA,cAAA,OAAA;AACA,eAAA,CAAA,GAAA,OAAA;eACA,OAAA,YAAA;AACA,gBAAA,SAAA,IAAA,QAAA;;AAEA,aAAA,SAAA,IAAA,QAAA;IACA,GAAC,QAAA,WAAA;AACD;;QAAA,SAAA,SAAA;ACTA,cAAA,mBAAA,CAAA;AAGA,mBAAA,oBAAA,UAAA;AAGA,gBAAA,iBAAA,QAAA,GAAA;AACA,qBAAA,iBAAA,QAAA,EAAA;YACA;AAEA,gBAAAA,UAAA,iBAAA,QAAA,IAAA;;cACA,GAAA;;cACA,GAAA;;cACA,SAAA,CAAA;;YACA;AAGA,oBAAA,QAAA,EAAA,KAAAA,QAAA,SAAAA,SAAAA,QAAA,SAAA,mBAAA;AAGA,YAAAA,QAAA,IAAA;AAGA,mBAAAA,QAAA;UACA;AAIA,8BAAA,IAAA;AAGA,8BAAA,IAAA;AAGA,8BAAA,IAAA,SAAAC,UAAA,MAAA,QAAA;AACA,gBAAA,CAAA,oBAAA,EAAAA,UAAA,IAAA,GAAA;AACA,qBAAA,eAAAA,UAAA,MAAA,EAA0C,YAAA,MAAA,KAAA,OAAA,CAAgC;YAC1E;UACA;AAGA,8BAAA,IAAA,SAAAA,UAAA;AACA,gBAAA,OAAA,WAAA,eAAA,OAAA,aAAA;AACA,qBAAA,eAAAA,UAAA,OAAA,aAAA,EAAwD,OAAA,SAAA,CAAkB;YAC1E;AACA,mBAAA,eAAAA,UAAA,cAAA,EAAiD,OAAA,KAAA,CAAc;UAC/D;AAOA,8BAAA,IAAA,SAAA,OAAA,MAAA;AACA,gBAAA,OAAA,EAAA,SAAA,oBAAA,KAAA;AACA,gBAAA,OAAA,EAAA,QAAA;AACA,gBAAA,OAAA,KAAA,OAAA,UAAA,YAAA,SAAA,MAAA,WAAA,QAAA;AACA,gBAAA,KAAA,uBAAA,OAAA,IAAA;AACA,gCAAA,EAAA,EAAA;AACA,mBAAA,eAAA,IAAA,WAAA,EAAyC,YAAA,MAAA,MAAA,CAAiC;AAC1E,gBAAA,OAAA,KAAA,OAAA,SAAA,SAAA,UAAA,OAAA,MAAA,qBAAA,EAAA,IAAA,KAAA,SAAAC,MAAA;AAAgH,qBAAA,MAAAA,IAAA;YAAmB,EAAE,KAAA,MAAA,GAAA,CAAA;AACrI,mBAAA;UACA;AAGA,8BAAA,IAAA,SAAAF,SAAA;AACA,gBAAA,SAAAA,WAAAA,QAAA;;cACA,SAAA,aAAA;AAA2B,uBAAAA,QAAA,SAAA;cAA0B;;;cACrD,SAAA,mBAAA;AAAiC,uBAAAA;cAAe;;AAChD,gCAAA,EAAA,QAAA,KAAA,MAAA;AACA,mBAAA;UACA;AAGA,8BAAA,IAAA,SAAA,QAAA,UAAA;AAAsD,mBAAA,OAAA,UAAA,eAAA,KAAA,QAAA,QAAA;UAA+D;AAGrH,8BAAA,IAAA;AAIA,iBAAA,oBAAA,oBAAA,IAAA,CAAA;;;;;;;;;;;AClFA,kCAAA,EAAA,mBAAA;AAAA,kBAAA,gDAAA;;gBAAA;cAAA;AAAA,kBAAA,wDAAA,oBAAA,EAAA,6CAAA;AAAA,kBAAA,wCAAA;;gBAAA;cAAA;AAGA,kBAAMG,UAAUC,sCAAAA,SAAAA,EAAMC;AAEtB,kBAAI,OAAOC,WAAW,aAAa;AACjCA,uBAAOH,UAAUA;cAClB;AAEcA,kCAAAA,SAAAA,IAAAA;;;;;;;;;;;;ACTf,kCAAA,EAAA,mBAAA;AAAA,kBAAMI,UAAU;;gBAEdC,WAAW,SAAA,YAAM;AACf,yBAAO,OAAOC,mBAAmB;gBAClC;;gBAEDC,MAAM,SAAA,OAAM;AACV,yBAAOC,UAAUC,UAAUC,QAAQ,MAA5B,MAAwC,MAAM,CAAC,CAACC,SAASC;gBACjE;;gBAEDC,QAAQ,SAAA,SAAM;AACZ,yBAAO,CAACT,QAAQG,KAAR,KAAkB,CAAC,CAACJ,OAAOW;gBACpC;;gBAEDC,UAAU,SAAA,WAAsB;AAAA,sBAArBC,UAAqB,UAAA,SAAA,KAAA,UAAA,CAAA,MAAA,SAAA,UAAA,CAAA,IAAXb;AACnB,yBAAO,CAAC,CAACa,QAAQC;gBAClB;;gBAEDC,UAAU,SAAA,WAAM;AACd,yBAAOC,OAAOC,UAAUC,SAASC,KAAKnB,OAAOoB,WAAtC,EAAmDb,QAAQ,aAA3D,IAA4E,KAC/EF,UAAUC,UAAUe,YAApB,EAAkCd,QAAQ,QAA1C,MAAwD;gBAC7D;;gBAEDe,aAAa,SAAA,cAAM;AACjB,yBAAOjB,UAAUC,UAAUe,YAApB,EAAkCd,QAAQ,OAA1C,MAAuD;gBAC/D;cAzBa;AA4BDN,kCAAAA,SAAAA,IAAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzBR,uBAASsB,WAAYC,UAAUC,QAAQ;AAC5C,oBAAMC,YAAY,iBAAiBD,OAAOE,OAAO,6BAA6BF,OAAOG,YAAY;AACjG,uBAAO,iBAAiBF,YAAY,OAAOF,WAAW;cACvD;AAEM,uBAASK,gBAAiBC,KAAK;AACpC,uBAAOA,IAAIC,OAAO,CAAX,EAAcC,YAAd,IAA8BF,IAAIG,MAAM,CAAV;cACtC;AAEM,uBAASC,cAAeC,SAASV,QAAQ;AAC9C,oBAAMW,MAAM5B,SAAS6B,eAAerC;AAGpC,oBAAIsC,eAAe;AAGnB,oBAAMC,SAASH,IAAII,iBAAiBL,SAAS,EAA9B;AAEf,yBAASvC,MAAM,GAAGA,MAAM2C,OAAOE,QAAQ7C,OAAO;AAE5C,sBAAI6B,OAAOiB,aAAanC,QAAQ,GAA5B,MAAqC,MAAMkB,OAAOkB,YAAYpC,QAAQgC,OAAO3C,GAAD,CAAjC,MAA4C,MAAMgD,kBAAkBnB,OAAOiB,cAAcH,OAAO3C,GAAD,CAA5B,GAAoC;AACpJ,wBAAI2C,OAAOM,iBAAiBN,OAAO3C,GAAD,CAA9B,EAAsC0C,iBAAgBC,OAAO3C,GAAD,IAAQ,MAAM2C,OAAOM,iBAAiBN,OAAO3C,GAAD,CAA9B,IAAuC;kBACtH;gBACF;AAGD0C,gCAAgB,gBAAgBb,OAAOqB,WAAW,+BAA+BrB,OAAOG,YAAY;AAEpG,uBAAOU;cACR;AAED,uBAASM,kBAAmBL,QAAQQ,OAAO;AACzC,yBAASC,IAAI,GAAGA,IAAIT,OAAOE,QAAQO,KAAK;AACtC,sBAAI,QAAOD,KAAP,MAAiB,YAAYA,MAAMxC,QAAQgC,OAAOS,CAAD,CAApB,MAA6B,GAAI,QAAO;gBAC1E;AACD,uBAAO;cACR;AAEM,uBAASC,UAAWC,cAAczB,QAAQ;AAE/C,oBAAM0B,kBAAkB3C,SAAS4C,cAAc,KAAvB;AAGxB,oBAAIC,UAAU5B,OAAO6B,MAAR,GAAiB;AAC5BH,kCAAgBI,YAAY9B,OAAO6B;gBACpC,OAAM;AAEL,sBAAME,gBAAgBhD,SAAS4C,cAAc,IAAvB;AAGtB,sBAAMK,aAAajD,SAASkD,eAAejC,OAAO6B,MAA/B;AAGnBE,gCAAcG,YAAYF,UAA1B;AACAD,gCAAcI,aAAa,SAASnC,OAAOoC,WAA3C;AACAV,kCAAgBQ,YAAYH,aAA5B;gBACD;AAEDN,6BAAaY,aAAaX,iBAAiBD,aAAaa,WAAW,CAAxB,CAA3C;cACD;AAEM,uBAASC,QAASvC,QAAQ;AAE/B,oBAAIA,OAAOwC,UAAWC,qCAAAA,SAAAA,EAAMC,MAAN;AAGtB,oBAAI1C,OAAO2C,aAAc3C,QAAO2C,aAAP;AAGzB,oBAAI3C,OAAOwC,aAAaxC,OAAO4C,eAAgBrE,QAAOsE,IAAIC,gBAAgB9C,OAAO+C,SAAlC;AAG/C,oBAAIC,QAAQ;AAEZ,oBAAIxE,sCAAAA,SAAAA,EAAQW,SAAR,KAAsBX,sCAAAA,SAAAA,EAAQC,UAAR,GAAqB;AAE7CuE,0BAAQ;gBACT;AAED,oBAAMC,UAAU,SAAVA,WAAgB;AAEpB1E,yBAAO2E,oBAAoBF,OAAOC,QAAlC;AAEAjD,yBAAOmD,mBAAP;AAGA,sBAAMC,SAASrE,SAASsE,eAAerD,OAAOsD,OAA/B;AAEf,sBAAIF,QAAQ;AACVA,2BAAOG,OAAP;kBACD;gBACF;AAEDhF,uBAAOiF,iBAAiBR,OAAOC,OAA/B;cACD;AAEM,uBAASrB,UAAW6B,KAAK;AAC9B,oBAAMC,YAAY,IAAIC,OAAO,6CAAX;AAClB,uBAAOD,UAAUE,KAAKH,GAAf;cACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnGc,kCAAA,SAAA,IAAA;gBACbpF,OAAO,SAAA,MAAC2B,QAAQ6D,YAAe;AAE7B,sBAAMpC,eAAeqC,cAAc9D,OAAO+C,SAAR,IAAqB/C,OAAO+C,YAAYhE,SAASsE,eAAerD,OAAO+C,SAA/B;AAG1E,sBAAI,CAACtB,cAAc;AACjBlD,2BAAOwF,QAAQC,MAAM,8BAA8BhE,OAAO+C,SAA1D;AACA;kBACD;AAGD/C,yBAAOiE,mBAAmBC,aAAazC,cAAczB,MAAf;AAGtC,sBAAIA,OAAO6B,QAAQ;AACjBL,2BAAAA,wCAAAA,WAAAA,CAAAA,EAAUxB,OAAOiE,kBAAkBjE,MAA1B;kBACV;AAGDmE,sDAAAA,SAAAA,EAAMC,KAAKpE,QAAQ6D,UAAnB;gBACD;cArBY;AAwBf,uBAASK,aAAcxD,SAASV,QAAQ;AAEtC,oBAAMqE,QAAQ3D,QAAQ4D,UAAR;AAGd,oBAAMC,kBAAkBC,MAAMhF,UAAUgB,MAAMd,KAAKgB,QAAQ4B,UAAnC;AACxB,yBAASf,IAAI,GAAGA,IAAIgD,gBAAgBvD,QAAQO,KAAK;AAE/C,sBAAIvB,OAAOyE,eAAe3F,QAAQyF,gBAAgBhD,CAAD,EAAImD,EAAjD,MAAyD,IAAI;AAC/D;kBACD;AAGD,sBAAMC,cAAcT,aAAaK,gBAAgBhD,CAAD,GAAKvB,MAArB;AAGhCqE,wBAAMnC,YAAYyC,WAAlB;gBACD;AAGD,oBAAI3E,OAAO4E,cAAclE,QAAQmE,aAAa,GAAG;AAC/CR,wBAAMlC,aAAa,SAAS1B,OAAAA,wCAAAA,eAAAA,CAAAA,EAAcC,SAASV,MAAV,CAAzC;gBACD;AAGD,wBAAQU,QAAQoE,SAAhB;kBACE,KAAK;AAEHT,0BAAM/C,QAAQZ,QAAQY;AACtB;kBACF,KAAK;AAEH+C,0BAAMU,WAAW,IAAjB,EAAuBC,UAAUtE,SAAS,GAAG,CAA7C;AACA;gBARJ;AAWA,uBAAO2D;cACR;AAED,uBAASP,cAAef,WAAW;AAEjC,uBAAO,QAAOA,SAAP,MAAqB,YAAYA,cAAcA,qBAAqBpD,eAAeoD,UAAU8B,aAAa;cAClH;;;;;;;;;;;;ACrED,kCAAA,EAAA,mBAAA;AAAA,kBAAA,0CAAA;;gBAAA;cAAA;AAAA,kBAAA,sCAAA;;gBAAA;cAAA;AAAA,kBAAA,wCAAA;;gBAAA;cAAA;AAIe,kCAAA,SAAA,IAAA;gBACbxG,OAAO,SAAA,MAAC2B,QAAQ6D,YAAe;AAE7B,sBAAI7D,OAAO+C,UAAUkC,gBAAgBT,OAAO;AAE1CxE,2BAAO+C,YAAY,CAAC/C,OAAO+C,SAAR;kBACpB;AAGD/C,yBAAOiE,mBAAmBlF,SAAS4C,cAAc,KAAvB;AAG1B3B,yBAAO+C,UAAUmC,QAAQ,SAAAC,KAAO;AAE9B,wBAAMC,MAAMrG,SAAS4C,cAAc,KAAvB;AACZyD,wBAAIjD,aAAa,SAASnC,OAAOqF,UAAjC;AAGAD,wBAAID,MAAMA;AAIV,wBAAI3G,sCAAAA,SAAAA,EAAQC,UAAR,GAAqB;AACvB,0BAAM6G,oBAAoBF,IAAID;AAC9BC,0BAAID,MAAMG;oBACX;AAGD,wBAAMC,eAAexG,SAAS4C,cAAc,KAAvB;AAGrB4D,iCAAarD,YAAYkD,GAAzB;AAGApF,2BAAOiE,iBAAiB/B,YAAYqD,YAApC;kBACD,CAvBD;AA0BA,sBAAIvF,OAAO6B,OAAQL,QAAAA,wCAAAA,WAAAA,CAAAA,EAAUxB,OAAOiE,kBAAkBjE,MAA1B;AAG5BmE,sDAAAA,SAAAA,EAAMC,KAAKpE,QAAQ6D,UAAnB;gBACD;cA1CY;;;;;;;;;;;;ACJf,kCAAA,EAAA,mBAAA;AAAA,kBAAA,wCAAA;;gBAAA;cAAA;AAAA,kBAAA,sCAAA;;gBAAA;cAAA;AAAA,kBAAA,oCAAA;;gBAAA;cAAA;AAAA,kBAAA,qCAAA;;gBAAA;cAAA;AAAA,kBAAA,yCAAA;;gBAAA;cAAA;AAAA,kBAAA,sCAAA;;gBAAA;cAAA;AAAA,kBAAA,qCAAA;;gBAAA;cAAA;;;;;;;;;;;;;;AAUA,kBAAM2B,aAAa,CAAC,OAAO,QAAQ,SAAS,QAAQ,UAAjC;AAEJ,kCAAA,SAAA,IAAA;gBACblH,MADa,SAAA,OACL;AACN,sBAAM0B,SAAS;oBACb+C,WAAW;oBACX0C,mBAAmB;oBACnBC,MAAM;oBACN7D,QAAQ;oBACRO,aAAa;oBACbf,UAAU;oBACVsE,YAAY;oBACZC,iBAAiB;oBACjBC,WAAW;oBACXrD,WAAW;oBACXsD,SAAS,SAAA,QAAC9B,OAAU;AAAE,4BAAMA;oBAAO;oBACnCpB,gBAAgB;oBAChBD,cAAc;oBACdQ,oBAAoB,SAAA,qBAAM;oBAAE;oBAC5B4C,uBAAuB,SAAA,wBAAM;oBAAE;oBAC/BC,cAAc;oBACd1C,SAAS;oBACTW,kBAAkB;oBAClBgC,eAAe;oBACf/E,aAAa,CAAC,SAAS,WAAW,SAAS,aAAa,UAAU,cAAc,YAAnE;oBACbD,cAAc,CAAC,UAAU,OAAO,SAAS,iBAA3B;oBACdwD,gBAAgB,CAAA;oBAChByB,mBAAmB;oBACnBC,KAAK;oBACLC,OAAO;oBACPxB,YAAY;oBACZyB,QAAQ;;oBAGRC,WAAW;oBACXpG,MAAM;oBACNC,WAAW;oBACXoG,oBAAoB;oBACpBC,YAAY;oBACZnB,YAAY;kBAnCC;AAuCf,sBAAMoB,OAAOC,UAAU,CAAD;AACtB,sBAAID,SAASE,QAAW;AACtB,0BAAM,IAAIC,MAAM,uCAAV;kBACP;AAGD,0BAAA,QAAeH,IAAf,GAAA;oBACE,KAAK;AACHzG,6BAAO+C,YAAY8D,UAAUJ,IAAD;AAC5BzG,6BAAOyF,oBAAoBzF,OAAO+C;AAClC/C,6BAAO0F,OAAOgB,UAAU,CAAD,KAAO1G,OAAO0F;AACrC;oBACF,KAAK;AACH1F,6BAAO+C,YAAY0D,KAAK1D;AACxB/C,6BAAOyF,oBAAoB,OAAOgB,KAAKhB,sBAAsB,cAAcgB,KAAKhB,oBAAoBzF,OAAO+C;AAC3G/C,6BAAOyF,oBAAoBzF,OAAOqG,SAAP,+BAAA,OAA+CrG,OAAOyF,iBAAtD,IAA4EzF,OAAOyF;AAC9G,+BAASqB,KAAK9G,QAAQ;AACpB,4BAAI8G,MAAM,eAAeA,MAAM,oBAAqB;AAEpD9G,+BAAO8G,CAAD,IAAM,OAAOL,KAAKK,CAAD,MAAQ,cAAcL,KAAKK,CAAD,IAAM9G,OAAO8G,CAAD;sBAC9D;AACD;oBACF;AACE,4BAAM,IAAIF,MAAM,kEAAA,QAAyEH,IAAzE,CAAV;kBAjBV;AAqBA,sBAAI,CAACzG,OAAO+C,UAAW,OAAM,IAAI6D,MAAM,gCAAV;AAG7B,sBAAI,CAAC5G,OAAO0F,QAAQ,OAAO1F,OAAO0F,SAAS,YAAYF,WAAW1G,QAAQkB,OAAO0F,KAAK9F,YAAZ,CAAnB,MAAkD,IAAI;AAC3G,0BAAM,IAAIgH,MAAM,qEAAV;kBACP;AAGD,sBAAI5G,OAAOwC,UAAWC,qCAAAA,SAAAA,EAAMsE,KAAK/G,MAAX;AAGtB,sBAAIA,OAAO4C,eAAgB5C,QAAO4C,eAAP;AAG3B,sBAAMoE,YAAYjI,SAASsE,eAAerD,OAAOsD,OAA/B;AAElB,sBAAI0D,UAAWA,WAAUC,WAAWC,YAAYF,SAAjC;AAGf,sBAAMnD,aAAa9E,SAAS4C,cAAc,QAAvB;AAEnB,sBAAInD,sCAAAA,SAAAA,EAAQC,UAAR,GAAqB;AAIvBoF,+BAAW1B,aAAa,SAAS,iHAAjC;kBACD,OAAM;AAEL0B,+BAAW1B,aAAa,SAAS,wEAAjC;kBACD;AAGD0B,6BAAW1B,aAAa,MAAMnC,OAAOsD,OAArC;AAGA,sBAAItD,OAAO0F,SAAS,OAAO;AACzB7B,+BAAWsD,SAAS,wBAAwBnH,OAAOiG,gBAAgB;AAGnE,wBAAIjG,OAAOmG,KAAK;AAEd,0BAAI,CAAC3B,MAAM4C,QAAQpH,OAAOmG,GAArB,EAA2BnG,QAAOmG,MAAM,CAACnG,OAAOmG,GAAR;AAG7CnG,6BAAOmG,IAAIjB,QAAQ,SAAAmC,MAAQ;AACzBxD,mCAAWsD,UAAU,kCAAkCE,OAAO;sBAC/D,CAFD;oBAGD;AAEDxD,+BAAWsD,UAAU;kBACtB;AAGD,0BAAQnH,OAAO0F,MAAf;oBACE,KAAK;AAEH,0BAAIlH,sCAAAA,SAAAA,EAAQG,KAAR,GAAgB;AAClB,4BAAI;AACFoF,kCAAQuD,KAAK,6DAAb;AACA,8BAAM3G,MAAMpC,OAAOgJ,KAAKvH,OAAOyF,mBAAmB,QAAtC;AACZ9E,8BAAI6G,MAAJ;AACAxH,iCAAO+F,sBAAP;wBACD,SAAQ/B,OAAO;AACdhE,iCAAO8F,QAAQ9B,KAAf;wBACD,UAPD;AASE,8BAAIhE,OAAOwC,UAAWC,qCAAAA,SAAAA,EAAMC,MAAN;AACtB,8BAAI1C,OAAO2C,aAAc3C,QAAO2C,aAAP;wBAC1B;sBACF,OAAM;AACL8E,0DAAAA,SAAAA,EAAIpJ,MAAM2B,QAAQ6D,UAAlB;sBACD;AACD;oBACF,KAAK;AACH6D,0DAAAA,SAAAA,EAAMrJ,MAAM2B,QAAQ6D,UAApB;AACA;oBACF,KAAK;AACH8D,yDAAAA,SAAAA,EAAKtJ,MAAM2B,QAAQ6D,UAAnB;AACA;oBACF,KAAK;AACH+D,6DAAAA,SAAAA,EAAQvJ,MAAM2B,QAAQ6D,UAAtB;AACA;oBACF,KAAK;AACHgE,yDAAAA,SAAAA,EAAKxJ,MAAM2B,QAAQ6D,UAAnB;AACA;kBA/BJ;gBAiCD;cA1JY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACTA,kCAAA,SAAA,IAAA;gBACbxF,OAAO,SAAA,MAAC2B,QAAQ6D,YAAe;AAE7B,sBAAI,QAAO7D,OAAO+C,SAAd,MAA4B,UAAU;AACxC,0BAAM,IAAI6D,MAAM,wCAAV;kBACP;AAGD,sBAAI,OAAO5G,OAAOkG,sBAAsB,WAAW;AACjD,0BAAM,IAAIU,MAAM,uDAAV;kBACP;AAGD,sBAAI,CAAC5G,OAAO2F,cAAc,CAACnB,MAAM4C,QAAQpH,OAAO2F,UAArB,GAAkC;AAC3D,0BAAM,IAAIiB,MAAM,8CAAV;kBACP;AAGD5G,yBAAO2F,aAAa3F,OAAO2F,WAAWmC,IAAI,SAAAC,UAAY;AACpD,2BAAO;sBACLC,OAAO,QAAOD,QAAP,MAAoB,WAAWA,SAASC,QAAQD;sBACvDE,aAAa,QAAOF,QAAP,MAAoB,WAAWA,SAASE,cAAcF;sBACnEG,YAAY,QAAOH,QAAP,MAAoB,YAAYA,SAASG,aAAaH,SAASG,aAAa,MAAM,MAAMlI,OAAO2F,WAAW3E,SAAS;oBAH1H;kBAKR,CANmB;AASpBhB,yBAAOiE,mBAAmBlF,SAAS4C,cAAc,KAAvB;AAG1B,sBAAI3B,OAAO6B,QAAQ;AACjBL,2BAAAA,wCAAAA,WAAAA,CAAAA,EAAUxB,OAAOiE,kBAAkBjE,MAA1B;kBACV;AAGDA,yBAAOiE,iBAAiBnC,aAAaqG,WAAWnI,MAAD;AAG/CmE,sDAAAA,SAAAA,EAAMC,KAAKpE,QAAQ6D,UAAnB;gBACD;cAvCY;AA0Cf,uBAASsE,WAAYnI,QAAQ;AAE3B,oBAAMoI,OAAOpI,OAAO+C;AACpB,oBAAM4C,aAAa3F,OAAO2F;AAG1B,oBAAI5F,WAAW;AAGf,oBAAIC,OAAOkG,mBAAmB;AAC5BnG,8BAAY;gBACb;AAGDA,4BAAY;AAGZ,yBAASsI,IAAI,GAAGA,IAAI1C,WAAW3E,QAAQqH,KAAK;AAC1CtI,8BAAY,sBAAsB4F,WAAW0C,CAAD,EAAIH,aAAa,MAAMlI,OAAO4F,kBAAkB,OAAOxF,OAAAA,wCAAAA,iBAAAA,CAAAA,EAAgBuF,WAAW0C,CAAD,EAAIJ,WAAf,IAA8B;gBACjJ;AAGDlI,4BAAY;AAGZ,oBAAIC,OAAOkG,mBAAmB;AAC5BnG,8BAAY;gBACb;AAGDA,4BAAY;AAGZ,yBAASwB,IAAI,GAAGA,IAAI6G,KAAKpH,QAAQO,KAAK;AAEpCxB,8BAAY;AAGZ,2BAASuI,IAAI,GAAGA,IAAI3C,WAAW3E,QAAQsH,KAAK;AAC1C,wBAAIC,aAAaH,KAAK7G,CAAD;AAGrB,wBAAMwG,WAAWpC,WAAW2C,CAAD,EAAIN,MAAMQ,MAAM,GAA1B;AACjB,wBAAIT,SAAS/G,SAAS,GAAG;AACvB,+BAASyH,IAAI,GAAGA,IAAIV,SAAS/G,QAAQyH,KAAK;AACxCF,qCAAaA,WAAWR,SAASU,CAAD,CAAT;sBACxB;oBACF,OAAM;AACLF,mCAAaA,WAAW5C,WAAW2C,CAAD,EAAIN,KAAf;oBACxB;AAGDjI,gCAAY,sBAAsB4F,WAAW2C,CAAD,EAAIJ,aAAalI,OAAO6F,YAAY,OAAO0C,aAAa;kBACrG;AAGDxI,8BAAY;gBACb;AAGDA,4BAAY;AAEZ,uBAAOA;cACR;;;;;;;;;;;;AC5GD,kCAAA,EAAA,mBAAA;AAAA,kBAAM0C,QAAQ;gBACZsE,MADY,SAAA,KACN/G,QAAQ;AAEZ,sBAAM0I,aAAa;AAenB,sBAAMC,aAAa5J,SAAS4C,cAAc,KAAvB;AACnBgH,6BAAWxG,aAAa,SAASuG,UAAjC;AACAC,6BAAWxG,aAAa,MAAM,eAA9B;AAGA,sBAAMyG,aAAa7J,SAAS4C,cAAc,KAAvB;AACnBiH,6BAAWzG,aAAa,SAAS,kEAAjC;AAGA,sBAAM0G,cAAc9J,SAAS4C,cAAc,KAAvB;AACpBkH,8BAAY1G,aAAa,SAAS,YAAlC;AACA0G,8BAAY1G,aAAa,MAAM,YAA/B;AACAyG,6BAAW1G,YAAY2G,WAAvB;AAGA,sBAAMC,UAAU/J,SAAS4C,cAAc,MAAvB;AAChBmH,0BAAQ3G,aAAa,SAAS,cAA9B;AACAyG,6BAAW1G,YAAY4G,OAAvB;AAGA,sBAAMC,cAAchK,SAASkD,eAAejC,OAAOgG,YAA/B;AACpB4C,6BAAW1G,YAAY6G,WAAvB;AAGAJ,6BAAWzG,YAAY0G,UAAvB;AAGA7J,2BAASiK,qBAAqB,MAA9B,EAAsC,CAAtC,EAAyC9G,YAAYyG,UAArD;AAGA5J,2BAASsE,eAAe,YAAxB,EAAsCG,iBAAiB,SAAS,WAAY;AAC1Ef,0BAAMC,MAAN;kBACD,CAFD;gBAGD;gBACDA,OApDY,SAAA,QAoDH;AACP,sBAAMiG,aAAa5J,SAASsE,eAAe,eAAxB;AAEnB,sBAAIsF,YAAY;AACdA,+BAAW1B,WAAWC,YAAYyB,UAAlC;kBACD;gBACF;cA1DW;AA6DClG,kCAAAA,SAAAA,IAAAA;;;;;;;;;;;;AC7Df,kCAAA,EAAA,mBAAA;AAAA,kBAAA,sCAAA;;gBAAA;cAAA;AAAA,kBAAA,0CAAA;;gBAAA;cAAA;AAGe,kCAAA,SAAA,IAAA;gBACbpE,OAAO,SAAA,MAAC2B,QAAQ6D,YAAe;AAE7B,sBAAI7D,OAAOqG,QAAQ;AACjB,wBAAM4C,aAAaC,WAAWC,KAAKC,KAAKpJ,OAAO+C,SAAR,GAAoB,SAAAsG,GAAC;AAAA,6BAAIA,EAAEC,WAAW,CAAb;oBAAJ,CAAzC;AACnBC,uCAAmBvJ,QAAQ6D,YAAYoF,UAArB;AAClB;kBACD;AAGDjJ,yBAAO+C,YAAY,qBAAqBa,KAAK5D,OAAO+C,SAAjC,IACf/C,OAAO+C,YACPxE,OAAOiL,SAASC,UAAUzJ,OAAO+C,UAAUzC,OAAO,CAAxB,MAA+B,MAAM,MAAMN,OAAO+C,YAAY/C,OAAO+C;AAGnG,sBAAM2G,MAAM,IAAInL,OAAOoL,eAAX;AACZD,sBAAIE,eAAe;AAEnBF,sBAAIlG,iBAAiB,SAAS,WAAM;AAClCjB,2BAAAA,wCAAAA,SAAAA,CAAAA,EAAQvC,MAAD;AACPA,2BAAO8F,QAAQ4D,IAAIG,YAAYH,GAA/B;kBAGD,CALD;AAOAA,sBAAIlG,iBAAiB,QAAQ,WAAM;AAEjC,wBAAI,CAAC,KAAK,GAAN,EAAW1E,QAAQ4K,IAAII,MAAvB,MAAmC,IAAI;AACzCvH,6BAAAA,wCAAAA,SAAAA,CAAAA,EAAQvC,MAAD;AACPA,6BAAO8F,QAAQ4D,IAAIG,YAAYH,GAA/B;AAGA;oBACD;AAGDH,uCAAmBvJ,QAAQ6D,YAAY6F,IAAIK,QAAzB;kBACnB,CAZD;AAcAL,sBAAInC,KAAK,OAAOvH,OAAO+C,WAAW,IAAlC;AACA2G,sBAAItF,KAAJ;gBACD;cAzCY;AA4Cf,uBAASmF,mBAAoBvJ,QAAQ6D,YAAYuE,MAAM;AAErD,oBAAI4B,WAAW,IAAIzL,OAAO0L,KAAK,CAAC7B,IAAD,GAAQ;kBAAE1C,MAAM;gBAAR,CAAxB;AACfsE,2BAAWzL,OAAOsE,IAAIqH,gBAAgBF,QAA3B;AAGXnG,2BAAW1B,aAAa,OAAO6H,QAA/B;AAEA7F,oDAAAA,SAAAA,EAAMC,KAAKpE,QAAQ6D,UAAnB;cACD;;;;;;;;;;;;ACxDD,kCAAA,EAAA,mBAAA;AAAA,kBAAA,wCAAA;;gBAAA;cAAA;AAAA,kBAAA,0CAAA;;gBAAA;cAAA;AAGA,kBAAMM,QAAQ;gBACZC,MAAM,SAAA,KAACpE,QAAQ6D,YAAe;AAE5B9E,2BAASiK,qBAAqB,MAA9B,EAAsC,CAAtC,EAAyC9G,YAAY2B,UAArD;AAGA,sBAAMsG,gBAAgBpL,SAASsE,eAAerD,OAAOsD,OAA/B;AAGtB6G,gCAAcC,SAAS,WAAM;AAC3B,wBAAIpK,OAAO0F,SAAS,OAAO;AAEzB,0BAAIlH,sCAAAA,SAAAA,EAAQC,UAAR,GAAqB;AACvB4L,mCAAW,WAAA;AAAA,iCAAMC,aAAaH,eAAenK,MAAhB;wBAAlB,GAA2C,GAA5C;sBACX,OAAM;AACLsK,qCAAaH,eAAenK,MAAhB;sBACb;AACD;oBACD;AAGD,wBAAIuK,gBAAiBJ,cAAcK,iBAAiBL,cAAcM;AAClE,wBAAIF,cAAcxL,SAAUwL,iBAAgBA,cAAcxL;AAG1DwL,kCAAcG,KAAKxI,YAAYlC,OAAOiE,gBAAtC;AAGA,wBAAIjE,OAAO0F,SAAS,SAAS1F,OAAOoG,OAAO;AAEzC,0BAAMA,QAAQrH,SAAS4C,cAAc,OAAvB;AACdyE,4BAAMtE,YAAY9B,OAAOoG;AAGzBmE,oCAAcI,KAAKzI,YAAYkE,KAA/B;oBACD;AAGD,wBAAMwE,SAASL,cAAcvB,qBAAqB,KAAnC;AAEf,wBAAI4B,OAAO5J,SAAS,GAAG;AACrB6J,uCAAiBrG,MAAM2E,KAAKyB,MAAX,CAAD,EAAqBE,KAAK,WAAA;AAAA,+BAAMR,aAAaH,eAAenK,MAAhB;sBAAlB,CAA1C;oBACD,OAAM;AACLsK,mCAAaH,eAAenK,MAAhB;oBACb;kBACF;gBACF;cA9CW;AAiDd,uBAASsK,aAAcH,eAAenK,QAAQ;AAC5C,oBAAI;AACFmK,gCAAc3C,MAAd;AAGA,sBAAIhJ,sCAAAA,SAAAA,EAAQS,OAAR,KAAoBT,sCAAAA,SAAAA,EAAQG,KAAR,GAAgB;AACtC,wBAAI;AACFwL,oCAAcK,cAAczL,SAASgM,YAAY,SAAS,OAAO,IAAjE;oBACD,SAAQC,GAAG;AACVb,oCAAcK,cAAcnM,MAA5B;oBACD;kBACF,OAAM;AAEL8L,kCAAcK,cAAcnM,MAA5B;kBACD;gBACF,SAAQ2F,OAAO;AACdhE,yBAAO8F,QAAQ9B,KAAf;gBACD,UAhBD;AAiBE,sBAAIxF,sCAAAA,SAAAA,EAAQC,UAAR,GAAqB;AAEvB0L,kCAAc/D,MAAM6E,aAAa;AACjCd,kCAAc/D,MAAM8E,OAAO;kBAC5B;AAED3I,yBAAAA,wCAAAA,SAAAA,CAAAA,EAAQvC,MAAD;gBACR;cACF;AAED,uBAAS6K,iBAAkBD,QAAQ;AACjC,oBAAMO,WAAWP,OAAO9C,IAAI,SAAAsD,OAAS;AACnC,sBAAIA,MAAMjG,OAAOiG,MAAMjG,QAAQ5G,OAAOiL,SAAS6B,MAAM;AACnD,2BAAOC,gBAAgBF,KAAD;kBACvB;gBACF,CAJgB;AAMjB,uBAAOG,QAAQC,IAAIL,QAAZ;cACR;AAED,uBAASG,gBAAiBF,OAAO;AAC/B,uBAAO,IAAIG,QAAQ,SAAAE,SAAW;AAC5B,sBAAMC,YAAY,SAAZA,aAAkB;AACtB,qBAACN,SAAS,OAAOA,MAAMO,iBAAiB,eAAeP,MAAMO,iBAAiB,KAAK,CAACP,MAAMQ,WACtFvB,WAAWqB,YAAW,GAAZ,IACVD,QAAO;kBACZ;AACDC,4BAAS;gBACV,CAPM;cAQR;AAEcvH,kCAAAA,SAAAA,IAAAA;;;;;;;;;;;;ACrGf,kCAAA,EAAA,mBAAA;AAAA,kBAAA,sCAAA;;gBAAA;cAAA;AAEe,kCAAA,SAAA,IAAA;gBACb9F,OAAO,SAAA,MAAC2B,QAAQ6D,YAAe;AAE7B7D,yBAAOiE,mBAAmBlF,SAAS4C,cAAc,KAAvB;AAC1B3B,yBAAOiE,iBAAiB9B,aAAa,SAAS,YAA9C;AAGAnC,yBAAOiE,iBAAiBnC,YAAY9B,OAAO+C;AAG3CoB,sDAAAA,SAAAA,EAAMC,KAAKpE,QAAQ6D,UAAnB;gBACD;cAXY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "names": ["module", "exports", "key", "printJS", "print", "init", "window", "Browser", "isFirefox", "InstallTrigger", "isIE", "navigator", "userAgent", "indexOf", "document", "documentMode", "isEdge", "StyleMedia", "isChrome", "context", "chrome", "<PERSON><PERSON><PERSON><PERSON>", "Object", "prototype", "toString", "call", "HTMLElement", "toLowerCase", "isIOSChrome", "addWrapper", "htmlData", "params", "bodyStyle", "font", "font_size", "capitalizePrint", "obj", "char<PERSON>t", "toUpperCase", "slice", "collectStyles", "element", "win", "defaultView", "elementStyle", "styles", "getComputedStyle", "length", "targetStyles", "targetStyle", "targetStylesMatch", "getPropertyValue", "max<PERSON><PERSON><PERSON>", "value", "i", "addHeader", "printElement", "headerContainer", "createElement", "isRawHTML", "header", "innerHTML", "headerElement", "headerNode", "createTextNode", "append<PERSON><PERSON><PERSON>", "setAttribute", "headerStyle", "insertBefore", "childNodes", "cleanUp", "showModal", "Modal", "close", "onLoadingEnd", "onLoadingStart", "URL", "revokeObjectURL", "printable", "event", "handler", "removeEventListener", "onPrintDialogClose", "iframe", "getElementById", "frameId", "remove", "addEventListener", "raw", "regexHtml", "RegExp", "test", "printFrame", "isHtmlElement", "console", "error", "printableElement", "cloneElement", "Print", "send", "clone", "cloneNode", "childNodesArray", "Array", "ignoreElements", "id", "clone<PERSON><PERSON><PERSON><PERSON>", "scanStyles", "nodeType", "tagName", "getContext", "drawImage", "constructor", "for<PERSON>ach", "src", "img", "imageStyle", "fullyQualifiedSrc", "imageWrapper", "printTypes", "fallbackPrintable", "type", "properties", "gridHeaderStyle", "gridStyle", "onError", "onIncompatibleBrowser", "modalMessage", "documentTitle", "repeatTableHeader", "css", "style", "base64", "onPdfOpen", "honorMarginPadding", "honorColor", "args", "arguments", "undefined", "Error", "encodeURI", "k", "show", "usedFrame", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "srcdoc", "isArray", "file", "info", "open", "focus", "Pdf", "Image", "Html", "RawHtml", "Json", "map", "property", "field", "displayName", "columnSize", "jsonToHTML", "data", "a", "n", "stringData", "split", "p", "modalStyle", "printModal", "contentDiv", "closeButton", "spinner", "messageNode", "getElementsByTagName", "bytesArray", "Uint8Array", "from", "atob", "c", "charCodeAt", "createBlobAndPrint", "location", "origin", "req", "XMLHttpRequest", "responseType", "statusText", "status", "response", "localPdf", "Blob", "createObjectURL", "iframeElement", "onload", "setTimeout", "performPrint", "printDocument", "contentWindow", "contentDocument", "body", "head", "images", "loadIframeImages", "then", "execCommand", "e", "visibility", "left", "promises", "image", "href", "loadIframeImage", "Promise", "all", "resolve", "pollImage", "naturalWidth", "complete"]}