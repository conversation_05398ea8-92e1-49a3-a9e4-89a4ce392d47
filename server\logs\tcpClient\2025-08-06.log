2025-08-06T10:05:15.815+08:00 [DEBU] {2c06a1d5430b59187269f1329e4211a4} E:/NaviHotgo/server/internal/library/network/tcp/client.go:166: connect to 127.0.0.1:8099 error: net.DialTimeout failed with network "tcp", address "127.0.0.1:8099", timeout "10s": dial tcp 127.0.0.1:8099: connectex: No connection could be made because the target machine actively refused it.
2025-08-06T10:09:29.326+08:00 [DEBU] {2c06a1d5430b59187269f1329e4211a4} E:/NaviHotgo/server/internal/library/network/tcp/client.go:213: read packet err:read tcp 127.0.0.1:54061->127.0.0.1:8099: use of closed network connection conn closed
2025-08-06T10:09:29.327+08:00 [DEBU] {2c06a1d5430b59187269f1329e4211a4} E:/NaviHotgo/server/internal/library/network/tcp/client.go:207: client are about to be reconnected..
2025-08-06T10:12:12.201+08:00 [DEBU] {04be49c8a40b5918ac3f1f1007d81b9d} E:/NaviHotgo/server/internal/library/network/tcp/client.go:166: connect to 127.0.0.1:8099 error: net.DialTimeout failed with network "tcp", address "127.0.0.1:8099", timeout "10s": dial tcp 127.0.0.1:8099: connectex: No connection could be made because the target machine actively refused it.
